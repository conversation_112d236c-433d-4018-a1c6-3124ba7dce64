{"cells": [{"cell_type": "code", "execution_count": 2, "id": "7aed5694", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pathlib as pl"]}, {"cell_type": "code", "execution_count": 6, "id": "4dd89b05", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55,112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47,123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n"]}], "source": ["# Make sure jupyter lab's current directory is the project root\n", "DATA_PATH = pl.Path.cwd() / \"data\" / \"Lending Club loan data\" / \"loan.csv\"\n", "\n", "\n", "chunk_size = 10000\n", "chunks = []\n", "\n", "for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "    chunks.append(chunk)\n", "\n", "df = pd.concat(chunks, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1efa294e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column 19 - 'desc':\n", "Unique values: [nan ' '\n", " \"I currently have a loan out with CashCall. The interest rate is 96%! At the time I took out the loan, it helped with a family crisis, but now the interest is crazy to be paying. I'd rather be paying my $200 a month to pay down a loan rather than just the interest.   Also, the remainder of my debt has interest rates ranging from 20 - 24%. This includes credit cards, a student loan, and some personal loans. So, I would like to consolidate the rest of my debt and pay a lower interest rate of 15%.  By doing this, I could lower my interest and my monthly payments and save at least $4000 over the next few years.  I do have a bankruptcy that was discharged 3 years ago due to having $40,000 in debt from an uninsured hospital stay. However, I've not missed a payment on anything since.  My credit report does from time time to time relist things as collections and then I have to dispute them as included in bankruptcy and they go back to being listed that way. I'm not sure why the status gets messed up sometimes.   Thank you for your time and consideration! \"\n", " ... 'I need to pay $2,100 for fixing my Volvo :)  Any help appreciated!'\n", " \"Hi,   I'm buying  a used car. Anybody on facebook wants to finance me?   Thanks\"\n", " 'I need to make several improvements around the house - fix garage, fix back fencing, and misc other.']\n", "+==================================================+\n", "Column 47 - 'next_pymnt_d':\n", "Unique values: ['Mar-2019' nan 'Feb-2019' 'Apr-2019' 'Dec-2018' 'Sep-2018' 'Aug-2018'\n", " 'Feb-2018' 'Jan-2016' 'Sep-2013' 'Feb-2016' 'Feb-2014' 'May-2014'\n", " 'Jun-2013' 'Mar-2012' 'Apr-2012' 'May-2013' 'Aug-2012' 'Aug-2013'\n", " 'Jun-2012' 'Nov-2013' 'Feb-2012' 'Oct-2011' 'Jan-2013' 'Jan-2014'\n", " 'Jul-2013' 'Jul-2015' 'Jan-2012' 'Dec-2012' 'Jun-2011' 'Feb-2013'\n", " 'Nov-2011' 'Nov-2012' 'Dec-2011' 'Aug-2011' 'Sep-2011' 'Apr-2011'\n", " 'Mar-2014' 'Apr-2013' 'Mar-2011' 'Jul-2012' 'Aug-2014' 'Oct-2013'\n", " 'Sep-2012' 'May-2012' 'Apr-2015' 'Jul-2011' 'Dec-2015' 'Dec-2013'\n", " 'Jan-2011' 'Oct-2012' 'Nov-2014' 'Mar-2013' 'Aug-2015' 'Feb-2015'\n", " 'May-2015' 'Jul-2014' 'Nov-2015' 'Sep-2014' 'Oct-2015' 'May-2011'\n", " 'Feb-2011' 'Dec-2014' 'Jun-2015' 'Apr-2014' 'Jan-2015' 'Sep-2015'\n", " 'Jun-2014' 'Nov-2010' 'Oct-2010' 'Dec-2010' 'Mar-2015' 'Oct-2014'\n", " 'Jul-2010' 'Sep-2010' 'May-2010' 'Aug-2010' 'Mar-2010' 'Jun-2010'\n", " 'Apr-2010' 'Feb-2010' 'Dec-2009' 'Nov-2009' 'Oct-2009' 'Jan-2010'\n", " 'Sep-2009' 'Jun-2009' 'Aug-2009' 'Jul-2009' 'May-2009' 'Apr-2009'\n", " 'Jan-2009' 'Oct-2008' 'Feb-2009' 'Nov-2008' 'Sep-2008' 'Mar-2009'\n", " 'Dec-2008' 'Aug-2008' 'Jun-2008' 'Jul-2008' 'Apr-2008' 'May-2008'\n", " 'Feb-2008' 'Jan-2008' 'Mar-2008']\n", "+==================================================+\n", "Column 123 - 'hardship_type':\n", "Unique values: [nan 'INTEREST ONLY-3 MONTHS DEFERRAL']\n", "+==================================================+\n", "Column 124 - 'hardship_reason':\n", "Unique values: [nan 'UNEMPLOYMENT' 'NATURAL_DISASTER' 'EXCESSIVE_OBLIGATIONS' 'MEDICAL'\n", " 'INCOME_CURTAILMENT' 'DISABILITY' 'REDUCED_HOURS' 'FAMILY_DEATH'\n", " 'DIVORCE']\n", "+==================================================+\n", "Column 125 - 'hardship_status':\n", "Unique values: [nan 'ACTIVE' 'COMPLETED' 'BROKEN']\n", "+==================================================+\n", "Column 128 - 'hardship_start_date':\n", "Unique values: [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Sep-2018' 'Jan-2019' 'Dec-2018'\n", " 'Aug-2018' 'Jul-2018' 'May-2018' 'Sep-2017' 'Feb-2018' 'Dec-2017'\n", " 'Apr-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Jun-2018' 'Oct-2017'\n", " 'Nov-2017' 'Jul-2017' 'Jun-2017' 'May-2017' 'Feb-2017' 'Apr-2017'\n", " 'Jan-2017' 'Mar-2017']\n", "+==================================================+\n", "Column 129 - 'hardship_end_date':\n", "Unique values: [nan 'Apr-2019' 'Dec-2018' 'Jan-2019' 'Feb-2019' 'Oct-2018' 'May-2019'\n", " 'Mar-2019' 'Nov-2018' 'Aug-2018' 'Dec-2017' 'May-2018' 'Nov-2017'\n", " 'Mar-2018' 'Jul-2018' 'Apr-2018' 'Jun-2018' 'Sep-2017' 'Sep-2018'\n", " 'Oct-2017' 'Jan-2018' 'Feb-2018' 'Aug-2017' 'Jul-2017' 'Jun-2017'\n", " 'May-2017' 'Apr-2017' 'Mar-2017']\n", "+==================================================+\n", "Column 130 - 'payment_plan_start_date':\n", "Unique values: [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Dec-2018' 'Jan-2019' 'Sep-2018'\n", " 'Mar-2019' 'Aug-2018' 'Jun-2018' 'Sep-2017' 'Oct-2017' 'Feb-2018'\n", " 'Dec-2017' 'May-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Apr-2018'\n", " 'Nov-2017' 'Jul-2018' 'Jul-2017' 'Jun-2017' 'May-2017' 'Mar-2017'\n", " 'Feb-2017' 'Apr-2017']\n", "+==================================================+\n", "Column 133 - 'hardship_loan_status':\n", "Unique values: [nan 'Late (16-30 days)' 'Issued' 'Current' 'Late (31-120 days)'\n", " 'In Grace Period']\n", "+==================================================+\n", "Column 139 - 'debt_settlement_flag_date':\n", "Unique values: [nan 'Feb-2019' 'Dec-2018' 'Jan-2019' 'Nov-2018' 'Oct-2018' 'Sep-2018'\n", " 'Aug-2018' 'Jul-2018' 'Jun-2018' 'Apr-2018' 'Dec-2017' 'Feb-2018'\n", " 'Sep-2017' 'May-2018' 'Mar-2018' 'Nov-2017' 'Jan-2018' 'Jun-2017'\n", " 'May-2017' 'Jul-2017' 'Aug-2017' 'Oct-2017' 'Apr-2017' 'Mar-2017'\n", " 'Jan-2017' 'Feb-2017' 'Dec-2016' 'Nov-2016' 'Aug-2016' 'Sep-2016'\n", " 'Jul-2016' 'Oct-2016' 'Jun-2016' 'May-2016' 'Apr-2016' 'Mar-2016'\n", " 'Feb-2016' 'Jan-2016' 'Dec-2015' 'Sep-2015' 'Oct-2015' 'Aug-2015'\n", " 'Jun-2015' 'Feb-2015' 'Nov-2015' 'May-2015' 'Nov-2014' 'Mar-2015'\n", " 'Apr-2015' 'Jul-2015' 'Jan-2015' 'Dec-2014' 'May-2014' 'Sep-2014'\n", " 'Jul-2014' 'Apr-2014' 'Jun-2014' 'Oct-2014' 'Aug-2014' 'Aug-2013'\n", " 'Mar-2014' 'Jan-2014' 'Nov-2013' 'Oct-2013' 'Feb-2014' 'Sep-2013'\n", " 'Mar-2013' 'Jun-2013' 'Nov-2012' 'Dec-2013' 'Oct-2012' 'Jun-2012'\n", " 'Apr-2013' 'Jul-2013' 'Feb-2012' 'Sep-2012' 'Feb-2011' 'Dec-2012'\n", " 'Oct-2011' 'Feb-2013' 'Nov-2011' 'Feb-2010']\n", "+==================================================+\n", "Column 140 - 'settlement_status':\n", "Unique values: [nan 'ACTIVE' 'COMPLETE' 'BROKEN']\n", "+==================================================+\n", "Column 141 - 'settlement_date':\n", "Unique values: [nan 'Feb-2019' 'Dec-2018' 'Jan-2019' 'Nov-2018' 'Oct-2018' 'Sep-2018'\n", " 'Aug-2018' 'Jul-2018' 'Jun-2018' 'Apr-2018' 'Aug-2017' 'Nov-2017'\n", " 'Jan-2018' 'Jun-2017' 'May-2018' 'Jul-2017' 'Feb-2018' 'Mar-2018'\n", " 'Dec-2017' 'May-2017' 'Sep-2017' 'Oct-2017' 'Feb-2017' 'Jan-2017'\n", " 'Mar-2017' 'Apr-2017' 'Dec-2016' 'Nov-2016' 'Oct-2016' 'Sep-2016'\n", " 'Aug-2016' 'Jul-2016' 'Jun-2016' 'Apr-2016' 'May-2016' 'Mar-2016'\n", " 'Feb-2016' 'Jan-2016' 'Dec-2015' 'Oct-2015' 'Sep-2015' 'Aug-2015'\n", " 'Nov-2015' 'Jun-2015' 'Jul-2015' 'May-2015' 'Sep-2014' 'Apr-2015'\n", " 'Oct-2014' 'Jan-2015' 'Mar-2015' 'Feb-2015' 'Nov-2014' 'Aug-2014'\n", " 'Dec-2014' 'Jul-2014' 'Apr-2014' 'Jun-2014' 'Mar-2014' 'May-2014'\n", " 'Feb-2014' 'Jan-2014' 'Nov-2013' 'Oct-2013' 'Dec-2013' 'Jul-2013'\n", " 'Aug-2013' 'Sep-2013' 'Jun-2013' 'Feb-2013' 'Mar-2013' 'May-2013'\n", " 'Nov-2012' 'Dec-2012' 'Apr-2013' 'Oct-2012' 'May-2012' 'Jun-2012'\n", " 'Aug-2012' 'Jan-2012' 'Sep-2012' 'Oct-2011' 'Feb-2012' 'Jul-2012'\n", " 'Feb-2011' 'Jul-2011' 'Mar-2012' 'Feb-2010' 'Mar-2009']\n", "+==================================================+\n"]}], "source": ["column_indices = [19, 47, 123, 124, 125, 128, 129, 130, 133, 139, 140, 141]\n", "\n", "# Iterate through the indices and print the column name and unique values\n", "for idx in column_indices:\n", "    if idx < len(df.columns):\n", "        column_name = df.columns[idx]\n", "        unique_values = df.iloc[:, idx].unique()\n", "        print(f\"Column {idx} - '{column_name}':\")\n", "        print(f\"Unique values: {unique_values}\")\n", "        print(\"+\" + \"=\"*50 + \"+\")\n", "    else:\n", "        print(f\"Column {idx}: Index out of range\")"]}, {"cell_type": "code", "execution_count": 32, "id": "96291847", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2260668, 145)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 53, "id": "5cf8f371", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of unique values: 2\n", "Unique values in 'hardship_flag': ['N' 'Y']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "N: 2259783\n", "Y: 885\n", "+==================================================+\n", "Number of unique values: 2\n", "Unique values in 'hardship_type': [nan 'INTEREST ONLY-3 MONTHS DEFERRAL']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "INTEREST ONLY-3 MONTHS DEFERRAL: 10613\n", "+==================================================+\n", "Number of unique values: 10\n", "Unique values in 'hardship_reason': [nan 'UNEMPLOYMENT' 'NATURAL_DISASTER' 'EXCESSIVE_OBLIGATIONS' 'MEDICAL'\n", " 'INCOME_CURTAILMENT' 'DISABILITY' 'REDUCED_HOURS' 'FAMILY_DEATH'\n", " 'DIVORCE']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "UNEMPLOYMENT: 1834\n", "NATURAL_DISASTER: 2965\n", "EXCESSIVE_OBLIGATIONS: 2079\n", "MEDICAL: 1249\n", "INCOME_CURTAILMENT: 1279\n", "DISABILITY: 154\n", "REDUCED_HOURS: 629\n", "FAMILY_DEATH: 206\n", "DIVORCE: 218\n", "+==================================================+\n", "Number of unique values: 4\n", "Unique values in 'hardship_status': [nan 'ACTIVE' 'COMPLETED' 'BROKEN']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "ACTIVE: 885\n", "COMPLETED: 7541\n", "BROKEN: 2187\n", "+==================================================+\n", "Number of unique values: 2\n", "Unique values in 'deferral_term': [nan  3.]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "3.0: 10613\n", "+==================================================+\n", "Number of unique values: 8951\n", "Unique values in 'hardship_amount': [   nan 378.39 203.67 ... 109.91 210.59 295.13]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "378.39: 1\n", "203.67: 1\n", "378.9: 1\n", "126.27: 1\n", "119.59: 1\n", "243.56: 1\n", "159.94: 1\n", "241.9: 1\n", "134.81: 1\n", "194.82: 4\n", "55.04: 1\n", "215.7: 1\n", "248.46: 2\n", "109.26: 2\n", "22.69: 2\n", "218.13: 1\n", "57.5: 1\n", "12.26: 2\n", "94.78: 1\n", "5.95: 1\n", "114.36: 2\n", "55.58: 1\n", "171.04: 1\n", "36.63: 1\n", "219.99: 1\n", "118.5: 1\n", "96.14: 1\n", "242.52: 1\n", "175.66: 1\n", "467.15: 1\n", "399.53: 1\n", "93.46: 1\n", "478.38: 1\n", "196.12: 2\n", "289.13: 1\n", "290.52: 1\n", "114.38: 2\n", "116.91: 3\n", "121.43: 1\n", "183.65: 1\n", "90.36: 1\n", "265.36: 1\n", "302.84: 1\n", "326.6: 1\n", "231.94: 1\n", "85.03: 1\n", "472.35: 1\n", "119.04: 1\n", "104.74: 1\n", "340.41: 1\n", "120.72: 2\n", "147.81: 1\n", "110.19: 1\n", "51.08: 1\n", "53.11: 1\n", "75.4: 2\n", "413.99: 1\n", "293.41: 1\n", "170.05: 1\n", "133.89: 1\n", "104.6: 1\n", "158.91: 3\n", "94.22: 2\n", "10.55: 1\n", "63.85: 2\n", "252.97: 1\n", "359.38: 1\n", "81.03: 1\n", "283.79: 1\n", "112.7: 2\n", "194.04: 2\n", "394.51: 1\n", "283.73: 1\n", "55.55: 2\n", "65.44: 2\n", "247.68: 1\n", "26.94: 1\n", "63.88: 4\n", "189.69: 2\n", "200.66: 1\n", "204.25: 1\n", "136.12: 1\n", "29.69: 3\n", "261.97: 1\n", "122.88: 1\n", "25.6: 2\n", "575.28: 1\n", "247.33: 1\n", "335.22: 1\n", "126.8: 2\n", "80.77: 3\n", "129.01: 1\n", "126.53: 2\n", "162.06: 2\n", "216.82: 1\n", "75.26: 1\n", "96.37: 2\n", "74.89: 1\n", "54.62: 3\n", "66.81: 1\n", "110.79: 1\n", "61.1: 1\n", "51.2: 4\n", "718.25: 1\n", "39.55: 2\n", "325.51: 1\n", "335.18: 2\n", "323.32: 1\n", "86.24: 2\n", "217.78: 1\n", "57.85: 2\n", "317.13: 1\n", "497.11: 1\n", "130.36: 1\n", "266.11: 2\n", "41.26: 1\n", "129.28: 1\n", "248.87: 1\n", "300.23: 1\n", "432.66: 1\n", "84.91: 1\n", "113.93: 2\n", "247.22: 1\n", "225.42: 1\n", "163.65: 2\n", "837.68: 1\n", "62.18: 1\n", "89.74: 2\n", "845.22: 1\n", "89.49: 2\n", "140.41: 2\n", "218.05: 1\n", "109.19: 2\n", "222.1: 2\n", "154.28: 2\n", "93.18: 1\n", "132.21: 1\n", "214.97: 2\n", "649.97: 1\n", "62.13: 3\n", "299.96: 1\n", "124.83: 1\n", "87.21: 2\n", "317.72: 1\n", "491.2: 1\n", "265.43: 1\n", "137.84: 1\n", "231.12: 1\n", "46.23: 1\n", "88.49: 2\n", "216.92: 1\n", "83.63: 2\n", "275.69: 1\n", "140.27: 2\n", "273.3: 1\n", "254.77: 2\n", "98.0: 2\n", "19.81: 1\n", "790.68: 1\n", "60.52: 1\n", "133.8: 2\n", "439.62: 1\n", "184.9: 2\n", "16.96: 2\n", "111.75: 1\n", "403.54: 1\n", "108.23: 1\n", "286.03: 1\n", "463.02: 1\n", "436.37: 1\n", "141.42: 1\n", "116.26: 1\n", "116.72: 2\n", "292.77: 1\n", "229.74: 2\n", "294.69: 2\n", "99.19: 1\n", "197.97: 2\n", "105.19: 1\n", "94.1: 1\n", "150.48: 1\n", "331.52: 1\n", "267.57: 1\n", "84.72: 1\n", "142.08: 2\n", "274.66: 1\n", "302.47: 1\n", "51.45: 1\n", "586.4: 1\n", "556.34: 1\n", "160.27: 1\n", "363.52: 1\n", "220.68: 1\n", "174.05: 3\n", "34.11: 1\n", "166.87: 1\n", "123.7: 2\n", "298.05: 1\n", "47.2: 1\n", "123.07: 1\n", "91.72: 2\n", "282.44: 1\n", "39.1: 1\n", "287.76: 2\n", "57.62: 1\n", "128.62: 1\n", "76.83: 1\n", "277.73: 1\n", "438.1: 1\n", "223.43: 1\n", "271.44: 1\n", "74.53: 1\n", "77.21: 2\n", "312.8: 1\n", "105.11: 1\n", "223.19: 2\n", "196.52: 2\n", "81.76: 1\n", "447.98: 1\n", "392.0: 1\n", "24.94: 1\n", "429.67: 1\n", "88.22: 1\n", "223.08: 1\n", "164.39: 2\n", "828.98: 1\n", "93.28: 2\n", "54.85: 2\n", "274.23: 1\n", "89.02: 2\n", "446.35: 1\n", "167.17: 1\n", "363.95: 1\n", "59.75: 2\n", "503.74: 1\n", "146.39: 2\n", "103.64: 1\n", "85.17: 2\n", "217.17: 1\n", "62.14: 1\n", "307.64: 1\n", "392.12: 1\n", "105.78: 2\n", "192.9: 1\n", "48.29: 1\n", "240.23: 1\n", "36.51: 1\n", "64.56: 1\n", "195.15: 1\n", "279.08: 1\n", "65.8: 1\n", "125.95: 1\n", "403.73: 1\n", "78.6: 1\n", "196.05: 1\n", "83.82: 1\n", "184.47: 1\n", "24.06: 1\n", "178.67: 1\n", "371.04: 1\n", "52.97: 2\n", "14.05: 1\n", "280.09: 2\n", "45.56: 3\n", "209.21: 1\n", "79.71: 2\n", "179.42: 2\n", "290.27: 1\n", "96.41: 2\n", "156.23: 1\n", "26.89: 2\n", "36.18: 2\n", "65.23: 3\n", "156.0: 1\n", "22.38: 1\n", "241.99: 1\n", "220.13: 1\n", "32.6: 1\n", "66.55: 1\n", "129.5: 2\n", "289.23: 1\n", "585.19: 1\n", "128.95: 1\n", "117.89: 2\n", "400.97: 1\n", "298.53: 1\n", "150.88: 4\n", "56.62: 3\n", "286.72: 1\n", "36.0: 1\n", "65.31: 3\n", "345.71: 1\n", "28.82: 4\n", "85.86: 2\n", "173.48: 1\n", "44.52: 1\n", "451.49: 1\n", "85.61: 1\n", "114.61: 1\n", "119.33: 1\n", "366.08: 2\n", "184.34: 1\n", "680.42: 1\n", "67.78: 1\n", "44.57: 2\n", "45.7: 1\n", "34.39: 2\n", "121.2: 1\n", "357.68: 1\n", "57.34: 1\n", "158.83: 4\n", "271.55: 2\n", "17.18: 1\n", "43.22: 2\n", "169.02: 1\n", "36.43: 1\n", "91.83: 3\n", "132.85: 1\n", "24.01: 2\n", "61.96: 1\n", "64.33: 2\n", "134.64: 2\n", "156.43: 2\n", "73.95: 2\n", "28.55: 1\n", "59.68: 1\n", "15.5: 1\n", "709.36: 1\n", "156.7: 1\n", "277.1: 1\n", "43.76: 2\n", "69.9: 5\n", "76.07: 2\n", "13.72: 1\n", "71.38: 3\n", "20.71: 1\n", "93.98: 1\n", "31.51: 1\n", "185.29: 1\n", "164.93: 1\n", "49.64: 2\n", "202.13: 1\n", "238.25: 1\n", "393.03: 2\n", "527.71: 1\n", "120.1: 2\n", "73.83: 2\n", "47.6: 1\n", "72.27: 2\n", "228.35: 1\n", "184.01: 1\n", "272.76: 1\n", "115.08: 3\n", "182.77: 1\n", "535.89: 1\n", "132.41: 1\n", "184.73: 2\n", "85.65: 1\n", "278.38: 1\n", "211.38: 1\n", "62.58: 3\n", "92.27: 2\n", "84.53: 2\n", "194.05: 1\n", "208.76: 3\n", "96.11: 3\n", "194.1: 1\n", "68.85: 2\n", "162.04: 1\n", "93.68: 2\n", "53.94: 3\n", "95.33: 1\n", "34.24: 1\n", "189.55: 1\n", "609.63: 1\n", "33.32: 2\n", "133.48: 2\n", "36.89: 2\n", "208.79: 1\n", "279.02: 1\n", "156.57: 1\n", "46.89: 1\n", "102.35: 2\n", "113.85: 2\n", "24.58: 1\n", "38.73: 1\n", "26.95: 1\n", "91.5: 2\n", "244.63: 1\n", "263.18: 1\n", "197.73: 1\n", "185.61: 1\n", "71.14: 1\n", "201.34: 2\n", "164.87: 1\n", "54.37: 1\n", "79.66: 4\n", "315.49: 1\n", "39.93: 1\n", "28.95: 1\n", "78.01: 2\n", "289.55: 1\n", "323.4: 1\n", "146.02: 1\n", "307.04: 1\n", "80.78: 2\n", "33.36: 2\n", "47.28: 1\n", "99.6: 1\n", "51.75: 2\n", "246.17: 1\n", "371.48: 1\n", "41.21: 2\n", "394.69: 1\n", "46.93: 1\n", "419.69: 1\n", "45.31: 1\n", "74.55: 3\n", "26.63: 1\n", "87.58: 2\n", "35.6: 1\n", "391.49: 1\n", "152.42: 2\n", "100.08: 1\n", "77.13: 3\n", "68.98: 1\n", "516.24: 1\n", "315.33: 2\n", "646.56: 1\n", "68.78: 2\n", "29.47: 2\n", "221.89: 1\n", "66.48: 1\n", "518.99: 1\n", "199.05: 1\n", "140.43: 2\n", "382.79: 1\n", "285.79: 1\n", "77.83: 1\n", "89.22: 2\n", "145.93: 1\n", "129.82: 1\n", "62.93: 2\n", "250.42: 1\n", "484.32: 1\n", "169.68: 2\n", "34.94: 1\n", "174.64: 3\n", "91.84: 1\n", "88.17: 2\n", "30.35: 2\n", "443.3: 1\n", "280.72: 1\n", "44.3: 2\n", "265.47: 1\n", "168.09: 1\n", "129.21: 2\n", "44.49: 1\n", "122.56: 1\n", "42.92: 1\n", "226.17: 1\n", "556.07: 1\n", "340.24: 1\n", "217.71: 1\n", "283.53: 1\n", "246.22: 1\n", "73.08: 1\n", "125.85: 1\n", "63.61: 1\n", "68.39: 1\n", "16.07: 1\n", "114.42: 1\n", "130.37: 1\n", "232.61: 1\n", "43.3: 4\n", "74.31: 1\n", "46.71: 1\n", "361.62: 1\n", "8.14: 1\n", "225.95: 2\n", "37.48: 1\n", "198.28: 1\n", "112.56: 2\n", "412.42: 1\n", "52.65: 1\n", "51.94: 2\n", "64.75: 1\n", "28.03: 1\n", "451.54: 1\n", "31.19: 1\n", "190.53: 4\n", "146.49: 1\n", "25.01: 2\n", "48.93: 2\n", "454.95: 1\n", "95.12: 1\n", "310.73: 1\n", "46.78: 1\n", "15.3: 1\n", "266.3: 1\n", "67.42: 2\n", "61.54: 1\n", "170.61: 1\n", "15.29: 1\n", "219.11: 1\n", "109.36: 1\n", "15.16: 1\n", "351.73: 1\n", "321.4: 1\n", "78.81: 1\n", "228.7: 1\n", "186.88: 1\n", "119.01: 2\n", "46.49: 2\n", "398.11: 1\n", "338.09: 1\n", "64.98: 2\n", "178.16: 1\n", "275.07: 2\n", "56.66: 2\n", "301.49: 1\n", "198.55: 2\n", "23.48: 2\n", "154.44: 3\n", "315.45: 1\n", "40.23: 2\n", "190.55: 2\n", "186.25: 1\n", "54.13: 1\n", "20.02: 1\n", "461.13: 1\n", "158.93: 1\n", "113.21: 1\n", "203.35: 2\n", "24.91: 1\n", "37.16: 2\n", "57.1: 1\n", "233.52: 1\n", "36.75: 2\n", "53.22: 1\n", "133.83: 1\n", "95.39: 1\n", "37.61: 1\n", "128.99: 3\n", "66.83: 1\n", "311.82: 1\n", "41.76: 3\n", "117.33: 1\n", "55.02: 1\n", "150.57: 1\n", "179.77: 1\n", "380.7: 1\n", "32.14: 1\n", "39.87: 2\n", "204.52: 1\n", "87.02: 1\n", "62.46: 2\n", "186.47: 1\n", "112.82: 1\n", "66.21: 2\n", "156.73: 1\n", "41.54: 1\n", "243.43: 1\n", "157.71: 1\n", "146.18: 1\n", "251.91: 1\n", "441.83: 1\n", "136.23: 1\n", "42.99: 2\n", "118.3: 1\n", "49.42: 2\n", "154.21: 1\n", "60.3: 2\n", "604.02: 1\n", "316.11: 1\n", "40.24: 3\n", "165.65: 1\n", "377.84: 1\n", "232.92: 1\n", "6.86: 1\n", "306.28: 1\n", "274.39: 1\n", "48.84: 1\n", "26.97: 1\n", "675.39: 1\n", "288.63: 1\n", "41.52: 2\n", "55.49: 1\n", "120.86: 1\n", "132.9: 1\n", "259.89: 2\n", "255.14: 1\n", "360.56: 1\n", "207.05: 1\n", "121.64: 2\n", "138.93: 2\n", "154.39: 1\n", "297.44: 1\n", "154.31: 1\n", "469.84: 1\n", "205.35: 1\n", "107.83: 1\n", "53.33: 2\n", "142.6: 1\n", "109.02: 1\n", "122.45: 2\n", "238.02: 1\n", "296.07: 1\n", "76.92: 1\n", "218.48: 1\n", "83.03: 2\n", "212.11: 1\n", "317.59: 1\n", "200.35: 1\n", "76.26: 1\n", "116.74: 2\n", "62.6: 3\n", "466.96: 1\n", "178.87: 1\n", "312.26: 1\n", "81.42: 3\n", "143.7: 1\n", "25.66: 1\n", "179.7: 1\n", "196.34: 1\n", "67.07: 2\n", "180.45: 1\n", "225.32: 1\n", "108.56: 1\n", "165.89: 2\n", "64.94: 1\n", "164.99: 2\n", "226.79: 1\n", "316.4: 1\n", "160.95: 1\n", "177.34: 1\n", "114.49: 1\n", "338.64: 1\n", "61.13: 1\n", "81.51: 1\n", "399.84: 1\n", "39.53: 1\n", "224.49: 1\n", "41.01: 2\n", "76.53: 2\n", "119.96: 3\n", "231.41: 1\n", "192.82: 1\n", "142.72: 1\n", "127.92: 2\n", "148.49: 1\n", "385.64: 1\n", "208.1: 1\n", "36.01: 1\n", "37.31: 1\n", "92.06: 1\n", "129.68: 1\n", "167.6: 1\n", "349.88: 1\n", "31.01: 3\n", "63.86: 1\n", "117.36: 2\n", "252.56: 1\n", "40.52: 1\n", "144.26: 1\n", "29.2: 1\n", "79.95: 1\n", "61.21: 1\n", "361.8: 1\n", "164.44: 2\n", "147.36: 2\n", "446.55: 1\n", "320.56: 1\n", "154.95: 1\n", "730.92: 1\n", "37.13: 1\n", "147.74: 1\n", "371.85: 1\n", "216.49: 1\n", "107.49: 1\n", "388.78: 1\n", "172.28: 1\n", "102.68: 1\n", "346.21: 1\n", "461.83: 1\n", "554.31: 1\n", "211.91: 1\n", "63.5: 2\n", "223.71: 2\n", "78.74: 1\n", "119.8: 1\n", "86.33: 1\n", "94.73: 1\n", "47.86: 1\n", "322.2: 1\n", "163.01: 1\n", "263.49: 1\n", "288.36: 1\n", "396.33: 2\n", "278.98: 1\n", "104.91: 1\n", "201.78: 1\n", "118.19: 1\n", "110.91: 2\n", "517.2: 1\n", "322.0: 1\n", "579.26: 1\n", "292.55: 1\n", "110.36: 1\n", "79.85: 2\n", "254.55: 2\n", "196.47: 2\n", "354.97: 1\n", "266.35: 1\n", "59.2: 1\n", "216.77: 1\n", "175.89: 1\n", "125.1: 1\n", "198.39: 1\n", "27.09: 1\n", "472.84: 1\n", "433.93: 1\n", "349.65: 1\n", "16.6: 1\n", "203.88: 1\n", "110.45: 1\n", "78.84: 1\n", "45.92: 2\n", "71.84: 2\n", "91.89: 1\n", "137.38: 1\n", "73.55: 1\n", "498.56: 1\n", "83.0: 2\n", "90.02: 2\n", "14.92: 1\n", "120.79: 3\n", "17.99: 1\n", "69.35: 2\n", "35.38: 1\n", "141.58: 1\n", "52.15: 2\n", "151.64: 1\n", "262.62: 1\n", "222.61: 1\n", "248.96: 1\n", "360.74: 1\n", "211.31: 1\n", "60.2: 1\n", "194.01: 1\n", "82.42: 1\n", "54.09: 2\n", "7.52: 1\n", "164.82: 2\n", "101.11: 2\n", "111.2: 1\n", "229.52: 1\n", "95.5: 1\n", "55.85: 2\n", "137.92: 2\n", "69.03: 2\n", "59.77: 1\n", "77.58: 1\n", "170.03: 1\n", "278.89: 1\n", "167.22: 1\n", "272.82: 1\n", "154.99: 1\n", "24.61: 1\n", "86.5: 1\n", "435.99: 1\n", "232.39: 1\n", "117.63: 1\n", "166.0: 1\n", "96.28: 1\n", "74.2: 1\n", "169.61: 1\n", "12.24: 1\n", "29.07: 1\n", "594.41: 1\n", "14.44: 1\n", "89.36: 2\n", "126.41: 1\n", "92.32: 1\n", "184.85: 2\n", "119.79: 1\n", "29.3: 1\n", "463.42: 1\n", "154.48: 2\n", "90.81: 1\n", "242.9: 1\n", "156.5: 1\n", "192.73: 1\n", "66.39: 1\n", "98.23: 1\n", "148.81: 1\n", "208.46: 1\n", "331.28: 1\n", "25.11: 1\n", "185.96: 1\n", "102.1: 3\n", "306.94: 1\n", "345.64: 2\n", "186.98: 1\n", "100.12: 1\n", "121.07: 1\n", "141.01: 1\n", "164.14: 1\n", "38.48: 2\n", "141.03: 2\n", "142.92: 1\n", "369.38: 1\n", "26.33: 2\n", "12.28: 1\n", "98.27: 1\n", "318.33: 1\n", "98.29: 1\n", "183.52: 1\n", "65.63: 1\n", "46.97: 1\n", "113.89: 2\n", "92.46: 1\n", "29.68: 2\n", "77.45: 1\n", "188.33: 1\n", "47.24: 3\n", "129.9: 3\n", "17.65: 1\n", "21.92: 1\n", "76.96: 1\n", "188.48: 1\n", "34.82: 2\n", "35.18: 1\n", "54.01: 1\n", "48.25: 1\n", "37.74: 1\n", "74.46: 2\n", "138.99: 2\n", "109.04: 1\n", "171.65: 1\n", "297.97: 1\n", "87.82: 1\n", "403.97: 1\n", "223.54: 1\n", "267.14: 1\n", "206.58: 1\n", "346.75: 1\n", "215.34: 1\n", "49.74: 2\n", "80.89: 1\n", "109.4: 1\n", "136.36: 2\n", "219.41: 2\n", "699.32: 1\n", "180.64: 1\n", "354.08: 1\n", "235.29: 1\n", "45.24: 3\n", "102.76: 2\n", "89.45: 1\n", "33.71: 2\n", "298.97: 1\n", "23.12: 2\n", "642.95: 1\n", "64.01: 2\n", "769.03: 1\n", "256.07: 1\n", "143.66: 1\n", "54.49: 1\n", "95.89: 1\n", "94.34: 2\n", "39.11: 1\n", "40.45: 1\n", "282.23: 1\n", "258.05: 1\n", "418.6: 2\n", "44.12: 2\n", "91.22: 2\n", "138.37: 1\n", "49.02: 2\n", "33.51: 1\n", "62.26: 2\n", "378.25: 1\n", "558.3: 1\n", "292.66: 3\n", "215.9: 1\n", "79.38: 1\n", "291.21: 1\n", "136.67: 1\n", "477.68: 1\n", "366.68: 1\n", "99.59: 2\n", "25.15: 1\n", "113.78: 1\n", "222.48: 1\n", "95.01: 1\n", "134.82: 1\n", "315.95: 1\n", "81.47: 2\n", "139.62: 1\n", "186.86: 1\n", "115.11: 2\n", "168.93: 2\n", "95.38: 1\n", "17.16: 1\n", "326.95: 1\n", "20.07: 1\n", "87.7: 1\n", "92.66: 3\n", "281.43: 1\n", "113.64: 2\n", "537.99: 1\n", "95.6: 2\n", "276.77: 1\n", "194.28: 1\n", "162.77: 1\n", "83.97: 1\n", "197.12: 1\n", "44.08: 1\n", "94.21: 1\n", "137.98: 1\n", "416.07: 1\n", "264.23: 1\n", "67.41: 1\n", "12.29: 2\n", "168.53: 2\n", "65.86: 1\n", "183.26: 1\n", "81.13: 2\n", "186.61: 1\n", "59.44: 2\n", "59.84: 1\n", "97.03: 1\n", "324.36: 1\n", "44.31: 1\n", "157.14: 1\n", "114.28: 1\n", "139.65: 2\n", "104.78: 1\n", "617.43: 1\n", "51.96: 1\n", "268.31: 1\n", "101.94: 2\n", "338.32: 1\n", "161.19: 2\n", "32.76: 1\n", "30.24: 2\n", "238.48: 1\n", "142.45: 2\n", "264.61: 1\n", "30.2: 1\n", "262.42: 1\n", "102.08: 2\n", "31.95: 1\n", "63.55: 2\n", "170.45: 2\n", "169.35: 1\n", "95.17: 2\n", "143.87: 1\n", "210.06: 1\n", "92.51: 1\n", "215.53: 1\n", "367.1: 2\n", "96.72: 1\n", "5.56: 1\n", "8.77: 1\n", "236.32: 1\n", "342.46: 1\n", "155.34: 1\n", "85.59: 1\n", "249.81: 1\n", "142.77: 1\n", "16.32: 2\n", "250.56: 2\n", "36.82: 1\n", "6.35: 1\n", "30.27: 1\n", "32.22: 1\n", "36.35: 3\n", "270.26: 2\n", "44.59: 2\n", "14.71: 2\n", "21.8: 1\n", "39.59: 1\n", "79.27: 1\n", "63.72: 2\n", "249.03: 1\n", "41.09: 1\n", "81.77: 1\n", "56.08: 2\n", "225.62: 1\n", "46.13: 2\n", "120.13: 1\n", "148.1: 1\n", "186.94: 1\n", "74.02: 1\n", "49.16: 3\n", "399.95: 1\n", "45.43: 1\n", "377.77: 1\n", "107.19: 1\n", "85.94: 1\n", "104.14: 2\n", "113.3: 1\n", "195.97: 1\n", "27.68: 1\n", "76.85: 1\n", "132.93: 2\n", "701.25: 1\n", "460.67: 1\n", "29.92: 1\n", "66.92: 2\n", "50.26: 1\n", "269.84: 1\n", "58.0: 4\n", "336.42: 1\n", "155.68: 1\n", "128.45: 1\n", "226.49: 1\n", "369.51: 1\n", "7.88: 1\n", "60.13: 1\n", "144.28: 1\n", "107.08: 1\n", "51.46: 1\n", "57.79: 2\n", "243.27: 1\n", "104.61: 1\n", "33.38: 1\n", "92.87: 1\n", "37.08: 2\n", "187.99: 1\n", "177.69: 1\n", "99.81: 2\n", "206.03: 1\n", "168.25: 1\n", "63.3: 1\n", "312.6: 1\n", "393.93: 1\n", "206.05: 2\n", "30.96: 1\n", "25.54: 1\n", "126.4: 1\n", "236.43: 1\n", "57.22: 1\n", "162.12: 1\n", "166.01: 1\n", "384.81: 1\n", "39.72: 1\n", "87.06: 1\n", "104.36: 1\n", "146.67: 1\n", "257.23: 1\n", "110.97: 2\n", "267.25: 1\n", "82.65: 1\n", "205.51: 1\n", "48.7: 3\n", "95.54: 2\n", "115.41: 1\n", "168.21: 1\n", "48.15: 3\n", "63.98: 2\n", "75.57: 2\n", "472.57: 1\n", "144.27: 2\n", "187.08: 2\n", "114.99: 1\n", "423.22: 1\n", "114.68: 1\n", "263.09: 1\n", "14.57: 2\n", "54.21: 1\n", "92.64: 1\n", "99.01: 1\n", "97.95: 1\n", "97.01: 1\n", "607.24: 1\n", "116.81: 1\n", "254.93: 1\n", "192.5: 1\n", "28.54: 1\n", "244.73: 1\n", "96.92: 1\n", "328.86: 1\n", "208.24: 1\n", "227.58: 1\n", "161.78: 3\n", "37.78: 1\n", "61.92: 3\n", "76.45: 1\n", "82.48: 2\n", "28.64: 1\n", "114.69: 1\n", "97.35: 1\n", "50.45: 3\n", "63.91: 1\n", "125.91: 1\n", "278.58: 1\n", "159.99: 1\n", "132.77: 1\n", "32.47: 1\n", "219.61: 1\n", "140.81: 1\n", "128.66: 1\n", "19.22: 1\n", "726.27: 1\n", "296.69: 2\n", "97.58: 1\n", "129.47: 1\n", "146.28: 1\n", "117.81: 2\n", "64.2: 1\n", "140.58: 2\n", "73.36: 1\n", "114.52: 1\n", "143.04: 2\n", "110.18: 1\n", "232.52: 1\n", "232.47: 2\n", "84.38: 1\n", "99.64: 1\n", "46.45: 1\n", "147.47: 2\n", "71.88: 2\n", "32.45: 2\n", "134.08: 1\n", "20.9: 2\n", "307.97: 1\n", "123.77: 1\n", "78.64: 1\n", "46.08: 3\n", "89.94: 2\n", "69.94: 2\n", "246.51: 2\n", "336.49: 1\n", "55.98: 2\n", "39.81: 1\n", "79.3: 1\n", "197.32: 1\n", "86.28: 2\n", "62.31: 1\n", "152.48: 2\n", "67.98: 2\n", "62.3: 4\n", "57.91: 1\n", "103.96: 1\n", "585.92: 1\n", "52.26: 2\n", "76.73: 1\n", "314.26: 2\n", "169.21: 1\n", "165.11: 1\n", "426.32: 1\n", "223.57: 1\n", "134.55: 1\n", "58.41: 2\n", "35.7: 1\n", "39.03: 1\n", "27.15: 2\n", "49.93: 1\n", "108.71: 1\n", "56.41: 1\n", "38.2: 1\n", "102.32: 2\n", "92.34: 1\n", "40.88: 2\n", "89.42: 3\n", "89.68: 1\n", "199.17: 3\n", "266.75: 1\n", "294.73: 1\n", "48.36: 2\n", "202.85: 1\n", "43.42: 3\n", "222.49: 1\n", "640.25: 1\n", "111.74: 1\n", "167.21: 1\n", "419.24: 1\n", "373.59: 1\n", "339.82: 1\n", "175.4: 1\n", "67.29: 2\n", "32.93: 1\n", "81.59: 2\n", "33.53: 1\n", "25.89: 1\n", "29.18: 2\n", "214.54: 1\n", "123.96: 1\n", "60.14: 2\n", "92.88: 2\n", "65.97: 2\n", "449.26: 1\n", "129.03: 1\n", "81.07: 1\n", "73.0: 1\n", "153.47: 1\n", "227.61: 1\n", "273.31: 1\n", "196.04: 3\n", "290.5: 1\n", "155.95: 1\n", "581.12: 1\n", "31.16: 1\n", "79.98: 1\n", "408.07: 1\n", "153.84: 1\n", "45.61: 2\n", "142.37: 1\n", "40.35: 1\n", "107.81: 1\n", "147.01: 1\n", "22.37: 1\n", "235.68: 1\n", "89.79: 2\n", "525.3: 1\n", "230.96: 1\n", "96.52: 1\n", "116.54: 1\n", "167.62: 1\n", "580.1: 1\n", "110.11: 1\n", "284.43: 1\n", "146.64: 1\n", "82.07: 2\n", "424.52: 1\n", "203.17: 1\n", "168.22: 1\n", "84.92: 1\n", "88.44: 1\n", "149.62: 1\n", "39.08: 2\n", "37.19: 2\n", "119.6: 1\n", "21.64: 1\n", "567.15: 1\n", "330.6: 1\n", "144.66: 2\n", "139.57: 1\n", "348.4: 1\n", "103.83: 2\n", "95.4: 2\n", "24.77: 1\n", "279.59: 1\n", "10.03: 2\n", "101.02: 2\n", "190.95: 1\n", "81.23: 1\n", "213.66: 1\n", "533.28: 1\n", "40.08: 3\n", "263.24: 1\n", "53.1: 1\n", "43.65: 1\n", "38.28: 1\n", "301.79: 1\n", "365.82: 1\n", "47.41: 2\n", "92.29: 1\n", "171.42: 2\n", "87.86: 2\n", "51.25: 1\n", "190.03: 1\n", "23.91: 2\n", "311.03: 2\n", "36.99: 2\n", "301.81: 1\n", "173.66: 1\n", "333.43: 1\n", "270.7: 1\n", "242.41: 1\n", "295.47: 1\n", "147.96: 1\n", "104.22: 2\n", "54.52: 2\n", "138.4: 1\n", "183.38: 1\n", "382.01: 1\n", "100.31: 1\n", "244.71: 1\n", "152.49: 1\n", "62.48: 1\n", "33.63: 1\n", "129.17: 1\n", "74.08: 1\n", "85.3: 1\n", "189.28: 2\n", "162.19: 2\n", "249.96: 1\n", "73.98: 2\n", "194.9: 1\n", "33.2: 1\n", "86.8: 3\n", "29.08: 1\n", "270.97: 1\n", "299.04: 1\n", "87.17: 2\n", "228.29: 1\n", "37.23: 1\n", "143.97: 1\n", "172.64: 2\n", "171.48: 3\n", "461.73: 2\n", "238.17: 1\n", "77.52: 1\n", "38.52: 1\n", "234.73: 1\n", "219.35: 2\n", "190.29: 1\n", "39.95: 3\n", "132.07: 1\n", "110.87: 2\n", "27.72: 2\n", "343.32: 1\n", "297.26: 1\n", "151.2: 1\n", "236.21: 1\n", "198.08: 2\n", "124.69: 1\n", "561.06: 1\n", "632.2: 1\n", "179.32: 2\n", "305.82: 2\n", "418.03: 1\n", "291.47: 1\n", "257.41: 1\n", "129.51: 1\n", "263.1: 1\n", "86.42: 1\n", "742.9: 1\n", "508.88: 1\n", "83.09: 1\n", "265.75: 1\n", "226.37: 1\n", "428.6: 1\n", "145.37: 1\n", "55.95: 1\n", "46.51: 2\n", "133.31: 2\n", "117.92: 1\n", "18.82: 1\n", "53.95: 1\n", "142.82: 1\n", "360.45: 1\n", "52.7: 1\n", "88.88: 2\n", "33.98: 1\n", "21.45: 2\n", "33.97: 3\n", "46.62: 2\n", "97.67: 2\n", "34.25: 2\n", "266.78: 1\n", "327.99: 1\n", "115.35: 3\n", "26.01: 1\n", "175.24: 1\n", "108.05: 1\n", "153.28: 1\n", "274.04: 1\n", "98.96: 1\n", "93.41: 1\n", "19.48: 1\n", "210.47: 2\n", "55.71: 1\n", "53.45: 1\n", "37.09: 1\n", "178.28: 1\n", "346.14: 1\n", "67.0: 1\n", "155.32: 1\n", "147.14: 3\n", "124.76: 1\n", "450.95: 1\n", "448.4: 1\n", "290.72: 1\n", "108.41: 1\n", "282.84: 1\n", "51.42: 1\n", "219.24: 1\n", "41.82: 2\n", "270.72: 1\n", "116.95: 1\n", "316.93: 1\n", "30.74: 1\n", "52.05: 1\n", "221.98: 1\n", "127.21: 2\n", "114.13: 1\n", "175.94: 1\n", "199.78: 1\n", "189.49: 1\n", "226.63: 1\n", "128.61: 1\n", "59.19: 1\n", "61.89: 3\n", "55.21: 1\n", "149.66: 4\n", "105.13: 1\n", "53.35: 1\n", "176.81: 1\n", "236.67: 1\n", "62.05: 1\n", "359.64: 1\n", "101.8: 2\n", "26.62: 1\n", "232.8: 1\n", "58.37: 1\n", "71.86: 2\n", "79.31: 1\n", "36.33: 1\n", "37.3: 2\n", "101.59: 1\n", "24.08: 1\n", "28.56: 2\n", "71.03: 1\n", "269.25: 1\n", "94.59: 5\n", "506.56: 1\n", "27.02: 2\n", "90.63: 1\n", "17.12: 2\n", "279.58: 1\n", "134.69: 1\n", "346.93: 1\n", "202.63: 1\n", "93.63: 1\n", "78.83: 1\n", "144.51: 1\n", "167.75: 1\n", "59.8: 1\n", "78.11: 1\n", "369.73: 1\n", "221.49: 1\n", "273.49: 1\n", "59.07: 2\n", "292.99: 1\n", "123.38: 1\n", "52.44: 1\n", "24.62: 1\n", "28.89: 1\n", "25.49: 1\n", "43.08: 1\n", "337.79: 1\n", "122.92: 1\n", "22.49: 2\n", "272.28: 1\n", "595.8: 1\n", "47.59: 1\n", "257.13: 1\n", "34.35: 1\n", "64.19: 1\n", "113.2: 1\n", "135.92: 1\n", "22.16: 1\n", "258.65: 1\n", "151.08: 1\n", "183.4: 1\n", "359.5: 1\n", "163.49: 1\n", "81.98: 2\n", "94.52: 1\n", "14.84: 1\n", "199.47: 1\n", "39.99: 1\n", "61.4: 1\n", "32.24: 1\n", "139.99: 1\n", "27.49: 2\n", "78.1: 2\n", "58.6: 1\n", "57.78: 1\n", "14.16: 1\n", "238.84: 1\n", "174.61: 1\n", "52.36: 1\n", "84.8: 1\n", "149.71: 4\n", "90.62: 1\n", "54.3: 1\n", "31.94: 1\n", "196.74: 1\n", "46.56: 2\n", "166.38: 1\n", "195.95: 1\n", "195.02: 1\n", "160.56: 2\n", "120.44: 1\n", "62.91: 1\n", "257.47: 1\n", "218.56: 1\n", "35.83: 1\n", "74.78: 1\n", "216.54: 1\n", "297.41: 1\n", "62.74: 1\n", "228.86: 1\n", "62.08: 2\n", "50.25: 1\n", "111.14: 1\n", "35.48: 1\n", "357.22: 1\n", "36.93: 2\n", "243.65: 1\n", "188.26: 1\n", "110.99: 1\n", "175.82: 1\n", "15.66: 3\n", "37.58: 2\n", "58.49: 1\n", "65.73: 1\n", "79.2: 2\n", "91.51: 1\n", "8.27: 2\n", "49.22: 1\n", "138.77: 2\n", "89.46: 1\n", "235.97: 2\n", "62.25: 2\n", "215.35: 1\n", "35.9: 1\n", "229.94: 1\n", "475.06: 1\n", "405.61: 1\n", "42.64: 3\n", "48.21: 1\n", "651.55: 1\n", "154.3: 2\n", "131.6: 3\n", "213.78: 1\n", "457.79: 1\n", "415.48: 1\n", "175.32: 1\n", "109.93: 1\n", "540.7: 1\n", "128.71: 1\n", "144.13: 3\n", "193.43: 1\n", "51.91: 1\n", "192.6: 3\n", "38.26: 2\n", "196.07: 1\n", "254.02: 1\n", "156.72: 1\n", "136.42: 2\n", "51.66: 2\n", "287.54: 1\n", "121.44: 2\n", "75.04: 1\n", "301.12: 1\n", "158.51: 3\n", "32.99: 2\n", "54.55: 4\n", "71.77: 1\n", "212.07: 2\n", "546.99: 1\n", "45.74: 3\n", "105.52: 2\n", "152.54: 1\n", "12.58: 1\n", "139.61: 2\n", "327.11: 1\n", "240.53: 1\n", "208.41: 1\n", "47.94: 2\n", "173.4: 1\n", "65.47: 1\n", "70.89: 3\n", "86.36: 1\n", "49.83: 1\n", "249.28: 1\n", "21.16: 1\n", "303.72: 1\n", "26.72: 1\n", "169.36: 1\n", "174.63: 2\n", "111.88: 2\n", "544.32: 1\n", "130.46: 2\n", "93.61: 2\n", "104.87: 2\n", "136.94: 1\n", "49.81: 2\n", "40.05: 3\n", "71.62: 2\n", "62.28: 1\n", "501.46: 1\n", "185.73: 1\n", "228.67: 2\n", "115.37: 1\n", "255.3: 1\n", "54.27: 1\n", "58.42: 1\n", "61.44: 1\n", "59.69: 3\n", "111.43: 2\n", "27.38: 1\n", "104.72: 1\n", "223.48: 1\n", "87.18: 1\n", "377.58: 1\n", "45.9: 1\n", "125.31: 1\n", "56.52: 1\n", "117.62: 1\n", "51.59: 4\n", "100.9: 2\n", "334.14: 1\n", "323.25: 1\n", "46.17: 2\n", "145.02: 1\n", "64.78: 2\n", "87.2: 1\n", "48.27: 4\n", "59.51: 2\n", "58.24: 1\n", "37.2: 2\n", "10.63: 1\n", "90.09: 1\n", "91.86: 1\n", "20.56: 1\n", "213.25: 1\n", "101.21: 1\n", "32.75: 1\n", "61.37: 1\n", "73.68: 1\n", "12.75: 1\n", "278.55: 1\n", "10.45: 1\n", "164.31: 1\n", "61.02: 3\n", "542.68: 1\n", "156.37: 4\n", "299.67: 1\n", "125.24: 1\n", "29.72: 3\n", "124.78: 2\n", "235.6: 1\n", "83.57: 1\n", "171.91: 1\n", "112.46: 1\n", "83.61: 1\n", "124.97: 1\n", "226.53: 1\n", "183.11: 1\n", "169.54: 1\n", "124.45: 2\n", "24.04: 2\n", "123.76: 2\n", "101.0: 2\n", "248.51: 2\n", "120.77: 1\n", "68.99: 1\n", "117.59: 1\n", "74.61: 1\n", "248.52: 1\n", "393.67: 1\n", "317.3: 1\n", "134.71: 1\n", "46.06: 1\n", "470.84: 1\n", "105.45: 1\n", "187.25: 1\n", "124.13: 1\n", "11.6: 1\n", "199.0: 2\n", "54.65: 1\n", "130.06: 1\n", "125.84: 1\n", "179.27: 1\n", "32.65: 1\n", "164.22: 1\n", "268.14: 1\n", "452.4: 1\n", "204.53: 1\n", "135.78: 2\n", "187.15: 1\n", "564.44: 1\n", "434.85: 1\n", "95.18: 1\n", "96.03: 1\n", "141.9: 1\n", "187.76: 1\n", "45.14: 1\n", "211.9: 1\n", "156.35: 1\n", "108.74: 2\n", "89.81: 1\n", "24.49: 2\n", "104.43: 1\n", "47.5: 1\n", "22.97: 2\n", "381.96: 2\n", "36.73: 3\n", "104.42: 1\n", "96.21: 3\n", "62.67: 1\n", "55.83: 2\n", "284.98: 1\n", "165.67: 1\n", "149.46: 1\n", "265.67: 2\n", "290.92: 2\n", "95.04: 1\n", "72.63: 2\n", "118.1: 1\n", "102.01: 1\n", "89.54: 1\n", "303.18: 1\n", "195.77: 1\n", "174.0: 1\n", "324.71: 1\n", "45.69: 3\n", "52.23: 1\n", "23.44: 1\n", "5.57: 1\n", "347.91: 1\n", "505.13: 1\n", "57.61: 1\n", "53.88: 2\n", "99.82: 2\n", "67.61: 1\n", "71.0: 1\n", "37.18: 1\n", "66.74: 2\n", "84.6: 1\n", "53.72: 1\n", "311.21: 2\n", "130.6: 1\n", "165.43: 1\n", "28.96: 1\n", "74.98: 2\n", "29.57: 1\n", "155.25: 1\n", "52.2: 1\n", "21.61: 1\n", "120.21: 1\n", "208.93: 1\n", "351.04: 1\n", "56.37: 1\n", "194.39: 1\n", "203.85: 1\n", "95.15: 2\n", "127.26: 1\n", "57.46: 1\n", "170.76: 1\n", "153.05: 2\n", "296.38: 1\n", "78.13: 1\n", "176.4: 1\n", "233.4: 1\n", "62.04: 1\n", "85.18: 1\n", "92.15: 1\n", "86.02: 1\n", "28.19: 2\n", "67.19: 1\n", "203.19: 1\n", "78.08: 1\n", "113.63: 1\n", "121.0: 1\n", "30.55: 1\n", "384.36: 1\n", "96.54: 2\n", "25.65: 2\n", "64.5: 1\n", "96.49: 1\n", "180.82: 2\n", "117.28: 1\n", "226.62: 1\n", "168.0: 1\n", "86.78: 3\n", "147.02: 1\n", "220.03: 1\n", "54.76: 1\n", "232.24: 1\n", "151.96: 1\n", "35.84: 1\n", "145.71: 1\n", "288.89: 1\n", "94.5: 1\n", "31.8: 2\n", "135.99: 1\n", "28.23: 1\n", "147.89: 1\n", "34.0: 1\n", "70.43: 1\n", "41.88: 2\n", "154.76: 2\n", "92.18: 1\n", "113.94: 2\n", "85.48: 1\n", "65.11: 3\n", "35.58: 1\n", "103.07: 1\n", "29.96: 2\n", "38.66: 1\n", "11.29: 1\n", "55.72: 3\n", "44.56: 1\n", "98.14: 1\n", "51.83: 1\n", "111.46: 1\n", "27.96: 1\n", "52.21: 1\n", "33.34: 1\n", "96.06: 1\n", "133.04: 3\n", "302.01: 1\n", "275.56: 1\n", "103.24: 1\n", "229.01: 1\n", "53.4: 2\n", "156.02: 1\n", "499.82: 1\n", "30.59: 2\n", "61.64: 1\n", "39.15: 1\n", "15.11: 1\n", "92.72: 1\n", "60.88: 1\n", "40.82: 3\n", "30.6: 1\n", "27.98: 1\n", "126.22: 1\n", "22.04: 1\n", "11.62: 1\n", "38.94: 2\n", "27.57: 1\n", "61.57: 1\n", "40.57: 1\n", "38.72: 1\n", "102.0: 1\n", "58.93: 1\n", "61.66: 1\n", "41.66: 2\n", "62.39: 1\n", "39.38: 2\n", "113.43: 1\n", "56.9: 1\n", "14.38: 1\n", "21.34: 2\n", "68.26: 1\n", "46.9: 2\n", "175.26: 1\n", "32.63: 1\n", "44.54: 1\n", "57.65: 2\n", "110.44: 1\n", "27.4: 2\n", "20.44: 1\n", "33.81: 1\n", "35.98: 2\n", "146.17: 1\n", "9.41: 2\n", "44.97: 1\n", "174.98: 1\n", "53.71: 2\n", "21.11: 1\n", "74.71: 1\n", "61.88: 1\n", "83.5: 3\n", "96.86: 2\n", "56.92: 1\n", "273.53: 1\n", "88.6: 1\n", "165.63: 2\n", "56.05: 2\n", "20.89: 1\n", "70.58: 2\n", "214.16: 1\n", "178.77: 1\n", "8.72: 1\n", "10.59: 1\n", "375.67: 1\n", "159.58: 1\n", "78.82: 3\n", "206.66: 1\n", "92.26: 2\n", "75.43: 2\n", "214.04: 1\n", "342.83: 1\n", "21.3: 1\n", "70.5: 2\n", "38.35: 1\n", "48.26: 1\n", "147.21: 1\n", "28.0: 1\n", "257.36: 1\n", "22.05: 1\n", "323.3: 1\n", "643.28: 1\n", "19.18: 1\n", "100.23: 1\n", "49.49: 1\n", "186.59: 1\n", "38.25: 2\n", "123.01: 1\n", "22.31: 2\n", "114.79: 2\n", "189.12: 1\n", "272.78: 2\n", "54.96: 2\n", "154.22: 2\n", "202.56: 1\n", "26.78: 1\n", "42.54: 3\n", "85.28: 2\n", "71.99: 2\n", "20.0: 1\n", "370.65: 1\n", "85.78: 2\n", "181.2: 1\n", "275.41: 1\n", "306.14: 1\n", "68.18: 2\n", "57.11: 1\n", "204.05: 1\n", "102.5: 1\n", "280.26: 1\n", "447.62: 1\n", "27.51: 1\n", "16.95: 1\n", "67.54: 1\n", "32.64: 1\n", "359.26: 1\n", "240.34: 1\n", "331.56: 1\n", "140.47: 2\n", "216.01: 2\n", "160.44: 1\n", "20.53: 1\n", "207.62: 1\n", "58.08: 1\n", "76.74: 2\n", "432.03: 1\n", "367.36: 1\n", "212.32: 1\n", "115.56: 2\n", "15.35: 1\n", "42.91: 2\n", "338.41: 1\n", "389.73: 1\n", "105.92: 2\n", "45.34: 1\n", "403.68: 1\n", "155.18: 1\n", "57.04: 1\n", "47.17: 1\n", "159.84: 1\n", "99.02: 1\n", "618.2: 1\n", "75.14: 2\n", "217.58: 1\n", "75.36: 4\n", "151.87: 1\n", "184.19: 2\n", "247.15: 1\n", "97.24: 1\n", "41.5: 4\n", "178.68: 2\n", "178.13: 1\n", "149.98: 3\n", "15.58: 1\n", "34.48: 1\n", "170.26: 1\n", "120.81: 2\n", "56.94: 2\n", "85.04: 1\n", "104.38: 1\n", "71.58: 1\n", "266.92: 1\n", "130.11: 1\n", "71.85: 2\n", "166.03: 1\n", "128.33: 1\n", "162.46: 2\n", "59.37: 2\n", "206.01: 2\n", "168.15: 2\n", "233.77: 1\n", "313.03: 1\n", "150.99: 1\n", "125.16: 1\n", "52.77: 1\n", "450.03: 1\n", "26.99: 1\n", "266.99: 1\n", "262.99: 1\n", "86.84: 1\n", "161.06: 2\n", "139.98: 1\n", "90.59: 2\n", "22.42: 1\n", "157.29: 1\n", "105.84: 1\n", "189.96: 1\n", "50.27: 1\n", "30.34: 1\n", "288.05: 1\n", "463.29: 1\n", "65.38: 1\n", "71.17: 1\n", "353.26: 1\n", "110.65: 1\n", "132.33: 5\n", "114.41: 1\n", "119.13: 1\n", "59.63: 3\n", "384.37: 1\n", "119.25: 1\n", "98.15: 1\n", "110.37: 2\n", "72.09: 2\n", "236.71: 1\n", "104.64: 1\n", "73.31: 1\n", "79.29: 2\n", "277.95: 1\n", "56.01: 1\n", "398.07: 1\n", "33.06: 2\n", "52.22: 1\n", "145.07: 2\n", "116.97: 1\n", "509.51: 1\n", "532.79: 1\n", "468.63: 1\n", "240.43: 1\n", "494.66: 1\n", "79.62: 1\n", "218.57: 1\n", "268.27: 1\n", "75.16: 2\n", "148.47: 3\n", "119.55: 1\n", "45.64: 2\n", "52.28: 2\n", "350.85: 1\n", "272.58: 1\n", "29.29: 1\n", "70.18: 2\n", "227.19: 2\n", "66.24: 1\n", "90.26: 2\n", "6.06: 1\n", "176.24: 3\n", "235.36: 1\n", "209.24: 1\n", "249.35: 1\n", "62.79: 2\n", "278.82: 1\n", "528.5: 1\n", "206.62: 1\n", "45.21: 1\n", "93.62: 1\n", "67.28: 1\n", "91.97: 1\n", "179.51: 1\n", "59.54: 2\n", "20.31: 1\n", "175.18: 1\n", "109.53: 3\n", "195.8: 1\n", "307.51: 1\n", "83.47: 1\n", "41.38: 1\n", "196.08: 1\n", "45.41: 1\n", "61.5: 4\n", "37.35: 3\n", "121.58: 2\n", "219.22: 1\n", "112.8: 3\n", "92.22: 1\n", "131.51: 1\n", "394.86: 1\n", "257.52: 2\n", "48.45: 1\n", "182.85: 1\n", "145.94: 1\n", "371.5: 1\n", "111.55: 2\n", "303.64: 1\n", "149.69: 1\n", "209.98: 1\n", "62.07: 1\n", "299.01: 1\n", "34.4: 1\n", "45.15: 1\n", "87.05: 1\n", "120.24: 2\n", "211.39: 1\n", "149.09: 1\n", "35.13: 1\n", "39.98: 1\n", "58.55: 1\n", "73.07: 2\n", "179.67: 1\n", "30.46: 1\n", "159.46: 2\n", "567.37: 1\n", "313.74: 1\n", "92.85: 1\n", "134.09: 1\n", "208.16: 1\n", "258.76: 1\n", "18.64: 1\n", "165.37: 1\n", "61.03: 2\n", "129.63: 1\n", "41.0: 3\n", "42.89: 1\n", "43.46: 1\n", "46.11: 3\n", "94.13: 1\n", "585.99: 2\n", "99.46: 1\n", "136.72: 1\n", "81.73: 1\n", "123.68: 1\n", "375.11: 1\n", "82.32: 1\n", "24.86: 2\n", "74.32: 1\n", "78.55: 1\n", "151.88: 1\n", "142.96: 1\n", "12.76: 2\n", "191.25: 1\n", "38.06: 1\n", "18.88: 2\n", "93.3: 1\n", "100.42: 2\n", "69.74: 1\n", "58.57: 2\n", "84.32: 1\n", "35.19: 1\n", "26.08: 1\n", "50.9: 1\n", "319.74: 1\n", "161.91: 1\n", "284.86: 1\n", "79.86: 2\n", "99.74: 1\n", "182.68: 1\n", "15.31: 1\n", "35.42: 1\n", "116.05: 1\n", "22.39: 2\n", "76.87: 1\n", "187.92: 1\n", "85.77: 2\n", "91.81: 1\n", "356.7: 1\n", "54.81: 1\n", "73.32: 3\n", "50.64: 2\n", "62.61: 3\n", "18.79: 1\n", "46.8: 1\n", "65.28: 3\n", "46.94: 1\n", "297.72: 1\n", "51.0: 1\n", "3.39: 1\n", "139.39: 1\n", "28.21: 1\n", "90.42: 1\n", "32.08: 2\n", "86.14: 1\n", "370.42: 1\n", "54.43: 2\n", "261.17: 1\n", "18.76: 1\n", "194.43: 1\n", "181.52: 1\n", "134.13: 1\n", "246.4: 1\n", "48.81: 1\n", "23.81: 1\n", "460.07: 2\n", "36.57: 1\n", "68.05: 1\n", "5.44: 1\n", "53.46: 1\n", "135.94: 2\n", "53.73: 1\n", "65.75: 1\n", "148.14: 1\n", "284.53: 2\n", "63.04: 1\n", "87.95: 2\n", "222.72: 1\n", "314.92: 1\n", "389.82: 1\n", "152.31: 1\n", "316.74: 1\n", "66.2: 1\n", "228.1: 1\n", "40.48: 1\n", "90.2: 1\n", "90.93: 1\n", "167.03: 2\n", "213.26: 1\n", "53.83: 1\n", "68.51: 1\n", "13.95: 1\n", "73.6: 1\n", "172.08: 1\n", "50.57: 1\n", "23.76: 3\n", "108.85: 1\n", "98.65: 1\n", "75.62: 2\n", "35.97: 1\n", "77.98: 1\n", "91.34: 1\n", "168.88: 1\n", "377.96: 1\n", "83.96: 1\n", "10.69: 1\n", "130.54: 2\n", "107.67: 2\n", "47.75: 1\n", "35.96: 2\n", "124.73: 1\n", "140.22: 1\n", "43.05: 1\n", "108.11: 2\n", "90.55: 1\n", "11.32: 1\n", "355.2: 1\n", "51.49: 2\n", "159.54: 1\n", "128.98: 1\n", "297.55: 1\n", "18.6: 2\n", "613.12: 1\n", "294.46: 1\n", "99.86: 2\n", "111.51: 2\n", "25.76: 1\n", "126.24: 1\n", "27.9: 1\n", "407.35: 1\n", "115.74: 1\n", "118.73: 1\n", "67.53: 1\n", "18.51: 1\n", "24.34: 1\n", "107.71: 1\n", "88.53: 1\n", "29.99: 2\n", "22.58: 1\n", "110.53: 1\n", "93.82: 1\n", "275.67: 1\n", "353.55: 1\n", "207.67: 1\n", "22.24: 1\n", "255.9: 1\n", "332.31: 1\n", "86.45: 1\n", "133.14: 1\n", "278.9: 1\n", "108.26: 1\n", "25.18: 1\n", "185.89: 3\n", "63.95: 1\n", "188.15: 1\n", "147.18: 1\n", "288.8: 1\n", "87.09: 2\n", "59.86: 1\n", "61.04: 1\n", "149.65: 2\n", "142.63: 2\n", "171.43: 1\n", "56.75: 2\n", "104.81: 1\n", "121.55: 3\n", "264.77: 1\n", "53.18: 1\n", "18.68: 1\n", "12.82: 2\n", "115.86: 1\n", "286.45: 1\n", "131.0: 1\n", "400.65: 1\n", "169.53: 2\n", "47.22: 1\n", "412.54: 1\n", "374.98: 1\n", "179.54: 1\n", "713.31: 1\n", "22.89: 1\n", "336.64: 1\n", "36.02: 1\n", "159.17: 1\n", "78.35: 1\n", "88.41: 2\n", "115.32: 1\n", "258.77: 1\n", "193.78: 2\n", "41.39: 2\n", "77.11: 1\n", "62.33: 1\n", "305.86: 1\n", "59.29: 1\n", "53.74: 1\n", "83.75: 1\n", "5.36: 1\n", "22.59: 1\n", "61.11: 2\n", "241.59: 1\n", "98.85: 2\n", "89.5: 1\n", "340.59: 1\n", "262.72: 1\n", "314.82: 1\n", "27.26: 3\n", "23.75: 2\n", "82.26: 2\n", "239.71: 1\n", "174.42: 1\n", "228.21: 2\n", "93.02: 1\n", "9.92: 1\n", "124.16: 3\n", "80.15: 1\n", "90.15: 1\n", "340.11: 1\n", "210.37: 1\n", "25.87: 1\n", "184.49: 2\n", "27.59: 1\n", "357.28: 1\n", "50.75: 1\n", "25.55: 1\n", "77.81: 3\n", "118.91: 1\n", "166.28: 1\n", "25.73: 1\n", "91.64: 1\n", "116.04: 1\n", "65.87: 1\n", "279.6: 1\n", "449.02: 1\n", "123.51: 1\n", "178.53: 1\n", "207.9: 1\n", "175.06: 1\n", "27.29: 2\n", "136.07: 1\n", "166.66: 1\n", "86.13: 1\n", "53.49: 1\n", "151.62: 2\n", "292.04: 1\n", "336.33: 1\n", "49.31: 2\n", "30.17: 1\n", "191.77: 1\n", "69.42: 1\n", "370.98: 1\n", "159.26: 1\n", "22.03: 1\n", "252.91: 1\n", "385.04: 1\n", "166.24: 1\n", "168.64: 1\n", "127.01: 1\n", "165.42: 2\n", "191.64: 1\n", "422.96: 1\n", "239.54: 1\n", "89.86: 1\n", "140.01: 1\n", "152.67: 1\n", "18.26: 2\n", "96.1: 1\n", "209.02: 2\n", "556.8: 1\n", "317.17: 1\n", "118.36: 1\n", "71.63: 1\n", "46.81: 1\n", "364.7: 1\n", "59.46: 2\n", "134.76: 1\n", "38.01: 1\n", "109.07: 1\n", "224.3: 1\n", "158.27: 2\n", "127.96: 1\n", "134.89: 1\n", "185.84: 1\n", "99.75: 1\n", "197.05: 1\n", "64.8: 2\n", "268.88: 2\n", "183.21: 1\n", "124.54: 1\n", "133.71: 2\n", "124.34: 1\n", "111.82: 1\n", "35.78: 3\n", "73.29: 3\n", "450.15: 1\n", "54.95: 1\n", "402.9: 1\n", "87.59: 2\n", "281.4: 1\n", "36.95: 1\n", "292.21: 1\n", "61.78: 1\n", "105.12: 1\n", "49.86: 2\n", "114.33: 2\n", "85.81: 1\n", "53.34: 2\n", "506.61: 1\n", "72.19: 1\n", "399.35: 1\n", "508.34: 1\n", "252.23: 1\n", "39.57: 2\n", "156.07: 2\n", "360.21: 1\n", "193.21: 2\n", "102.92: 1\n", "238.28: 1\n", "82.74: 2\n", "127.33: 1\n", "45.46: 1\n", "20.16: 1\n", "365.03: 1\n", "18.38: 1\n", "91.62: 2\n", "22.61: 1\n", "115.59: 2\n", "75.31: 2\n", "73.88: 1\n", "108.61: 2\n", "211.12: 1\n", "44.53: 1\n", "629.99: 1\n", "92.13: 1\n", "20.2: 1\n", "12.81: 1\n", "214.93: 1\n", "279.04: 1\n", "4.47: 1\n", "232.67: 1\n", "76.21: 1\n", "546.74: 1\n", "154.9: 1\n", "223.21: 1\n", "145.43: 1\n", "191.56: 1\n", "101.05: 1\n", "50.6: 1\n", "33.35: 1\n", "103.18: 1\n", "150.27: 1\n", "465.14: 1\n", "175.64: 1\n", "319.5: 1\n", "135.7: 1\n", "93.49: 1\n", "211.15: 1\n", "103.46: 2\n", "23.49: 1\n", "302.17: 1\n", "262.01: 2\n", "336.89: 1\n", "402.02: 1\n", "36.72: 2\n", "36.64: 2\n", "20.57: 1\n", "282.96: 1\n", "245.92: 1\n", "374.32: 1\n", "241.55: 1\n", "338.43: 2\n", "40.73: 1\n", "94.76: 1\n", "191.81: 1\n", "58.66: 1\n", "36.66: 1\n", "196.55: 1\n", "360.41: 1\n", "396.97: 1\n", "351.93: 1\n", "24.17: 2\n", "19.16: 3\n", "90.24: 2\n", "148.63: 1\n", "38.44: 2\n", "42.42: 1\n", "282.38: 1\n", "162.84: 1\n", "157.44: 1\n", "143.33: 1\n", "38.02: 2\n", "89.16: 1\n", "114.64: 1\n", "302.99: 1\n", "12.94: 1\n", "49.2: 2\n", "14.31: 1\n", "27.74: 1\n", "46.52: 2\n", "32.16: 2\n", "196.73: 1\n", "29.85: 2\n", "44.0: 1\n", "31.35: 1\n", "110.38: 1\n", "49.06: 1\n", "141.07: 1\n", "63.33: 1\n", "119.77: 1\n", "131.19: 1\n", "56.45: 1\n", "43.16: 1\n", "77.59: 1\n", "157.59: 1\n", "11.85: 1\n", "68.73: 1\n", "150.64: 1\n", "69.06: 2\n", "289.08: 1\n", "73.23: 1\n", "181.78: 1\n", "388.15: 1\n", "129.22: 1\n", "255.24: 1\n", "343.8: 1\n", "326.16: 1\n", "144.45: 1\n", "274.67: 1\n", "224.95: 1\n", "108.9: 1\n", "201.01: 1\n", "444.91: 1\n", "251.93: 1\n", "252.51: 1\n", "240.71: 2\n", "319.76: 1\n", "430.73: 1\n", "120.05: 1\n", "279.38: 1\n", "448.53: 1\n", "68.1: 1\n", "304.91: 1\n", "263.96: 1\n", "87.45: 2\n", "169.76: 1\n", "372.25: 1\n", "198.93: 2\n", "343.51: 1\n", "313.97: 1\n", "169.03: 1\n", "118.42: 1\n", "102.98: 1\n", "205.5: 1\n", "446.77: 1\n", "304.35: 1\n", "229.25: 1\n", "238.05: 1\n", "131.87: 2\n", "567.48: 1\n", "313.06: 1\n", "186.58: 1\n", "137.71: 1\n", "200.37: 1\n", "142.91: 1\n", "119.68: 2\n", "65.81: 1\n", "70.79: 2\n", "61.25: 1\n", "137.01: 1\n", "140.85: 1\n", "32.56: 2\n", "82.4: 2\n", "207.86: 1\n", "21.47: 1\n", "88.96: 2\n", "98.87: 3\n", "253.13: 1\n", "41.04: 3\n", "108.6: 1\n", "157.34: 2\n", "226.72: 1\n", "532.15: 1\n", "130.62: 2\n", "282.01: 1\n", "57.55: 1\n", "75.67: 3\n", "129.27: 1\n", "113.57: 2\n", "105.98: 2\n", "61.86: 1\n", "31.56: 2\n", "83.69: 1\n", "180.48: 1\n", "134.37: 1\n", "23.16: 2\n", "437.63: 1\n", "144.29: 1\n", "39.02: 1\n", "92.78: 1\n", "175.3: 1\n", "36.71: 1\n", "199.46: 1\n", "438.55: 1\n", "399.21: 1\n", "224.72: 1\n", "106.92: 1\n", "81.38: 1\n", "344.27: 1\n", "45.63: 1\n", "36.67: 3\n", "74.63: 2\n", "82.89: 1\n", "97.68: 1\n", "249.31: 1\n", "49.76: 1\n", "49.47: 1\n", "20.98: 1\n", "86.12: 1\n", "21.06: 1\n", "113.03: 1\n", "114.29: 2\n", "17.17: 1\n", "386.74: 1\n", "73.86: 1\n", "20.24: 1\n", "37.4: 4\n", "62.53: 2\n", "241.94: 2\n", "483.13: 1\n", "254.5: 1\n", "25.37: 1\n", "79.57: 3\n", "89.82: 2\n", "69.95: 2\n", "376.35: 1\n", "86.32: 1\n", "22.85: 1\n", "346.44: 1\n", "387.64: 1\n", "137.12: 1\n", "148.73: 1\n", "49.51: 1\n", "26.84: 1\n", "142.51: 1\n", "153.2: 1\n", "339.33: 1\n", "482.4: 1\n", "18.77: 1\n", "209.86: 1\n", "61.27: 1\n", "17.15: 1\n", "186.15: 1\n", "23.98: 1\n", "25.0: 3\n", "30.33: 1\n", "363.81: 1\n", "62.76: 2\n", "50.31: 1\n", "195.54: 1\n", "363.02: 1\n", "155.37: 1\n", "78.0: 3\n", "219.82: 1\n", "312.68: 1\n", "89.55: 1\n", "32.68: 1\n", "124.65: 3\n", "48.18: 1\n", "187.8: 2\n", "140.05: 1\n", "48.82: 1\n", "115.83: 2\n", "29.42: 1\n", "48.58: 1\n", "308.0: 1\n", "181.71: 1\n", "79.78: 1\n", "144.96: 1\n", "48.56: 5\n", "111.21: 1\n", "74.86: 2\n", "92.75: 1\n", "182.58: 1\n", "68.56: 1\n", "164.17: 1\n", "79.04: 1\n", "430.99: 1\n", "100.25: 1\n", "163.89: 1\n", "280.31: 1\n", "193.36: 1\n", "64.15: 2\n", "100.64: 1\n", "118.15: 1\n", "65.41: 1\n", "90.01: 1\n", "42.36: 1\n", "103.82: 1\n", "126.33: 2\n", "365.34: 1\n", "154.37: 2\n", "21.44: 3\n", "14.07: 1\n", "506.89: 1\n", "58.61: 1\n", "245.15: 1\n", "8.81: 1\n", "87.98: 1\n", "289.94: 2\n", "80.53: 1\n", "80.03: 1\n", "81.63: 2\n", "175.6: 1\n", "41.7: 1\n", "225.7: 2\n", "198.91: 2\n", "38.9: 2\n", "169.88: 1\n", "51.03: 1\n", "154.57: 1\n", "84.97: 1\n", "145.84: 1\n", "52.83: 2\n", "129.18: 2\n", "97.71: 1\n", "390.43: 1\n", "169.06: 1\n", "181.55: 2\n", "163.3: 1\n", "37.56: 1\n", "114.6: 1\n", "58.25: 1\n", "40.42: 1\n", "237.86: 2\n", "94.53: 1\n", "453.01: 1\n", "82.3: 1\n", "97.15: 2\n", "311.2: 1\n", "145.2: 2\n", "210.09: 1\n", "215.37: 1\n", "327.9: 1\n", "19.61: 3\n", "160.18: 1\n", "30.92: 1\n", "389.94: 2\n", "339.86: 1\n", "320.84: 1\n", "90.88: 1\n", "56.03: 2\n", "494.42: 1\n", "38.12: 1\n", "18.73: 1\n", "85.69: 2\n", "509.88: 1\n", "15.54: 1\n", "62.34: 1\n", "175.22: 1\n", "20.45: 1\n", "107.2: 1\n", "104.21: 1\n", "29.56: 1\n", "148.43: 2\n", "93.43: 1\n", "55.97: 1\n", "39.76: 1\n", "269.19: 1\n", "77.71: 1\n", "282.02: 1\n", "77.61: 1\n", "464.01: 1\n", "151.7: 1\n", "40.92: 1\n", "83.93: 1\n", "29.83: 2\n", "373.36: 1\n", "143.72: 1\n", "380.04: 1\n", "82.83: 1\n", "70.03: 1\n", "88.36: 1\n", "198.66: 1\n", "74.76: 1\n", "436.51: 1\n", "295.06: 1\n", "29.38: 2\n", "131.74: 2\n", "129.25: 1\n", "24.16: 1\n", "50.98: 1\n", "128.44: 1\n", "44.33: 2\n", "128.09: 1\n", "184.04: 1\n", "222.36: 1\n", "24.03: 1\n", "236.69: 1\n", "107.66: 1\n", "67.71: 2\n", "53.31: 1\n", "187.23: 1\n", "40.06: 1\n", "123.03: 1\n", "47.14: 2\n", "48.37: 1\n", "116.28: 3\n", "139.72: 1\n", "350.65: 1\n", "60.75: 1\n", "58.72: 1\n", "155.22: 1\n", "141.94: 1\n", "23.64: 1\n", "270.6: 1\n", "139.1: 2\n", "229.73: 1\n", "32.73: 1\n", "39.22: 1\n", "57.63: 2\n", "359.71: 1\n", "104.28: 1\n", "80.2: 1\n", "114.75: 1\n", "9.18: 1\n", "58.65: 1\n", "278.11: 1\n", "96.09: 1\n", "68.36: 1\n", "53.32: 2\n", "52.31: 1\n", "64.32: 3\n", "100.66: 1\n", "72.37: 2\n", "280.12: 2\n", "75.3: 1\n", "51.68: 2\n", "558.42: 1\n", "49.26: 2\n", "203.01: 2\n", "119.58: 1\n", "427.95: 1\n", "182.8: 1\n", "262.31: 1\n", "231.6: 1\n", "206.68: 1\n", "51.88: 2\n", "80.39: 1\n", "117.55: 2\n", "164.49: 3\n", "212.54: 1\n", "246.7: 1\n", "110.0: 1\n", "28.6: 1\n", "337.25: 1\n", "169.59: 1\n", "72.6: 1\n", "224.15: 1\n", "212.71: 1\n", "192.4: 1\n", "407.06: 1\n", "67.65: 1\n", "119.84: 1\n", "121.79: 1\n", "93.12: 1\n", "232.33: 1\n", "127.99: 1\n", "155.89: 1\n", "91.73: 2\n", "47.71: 2\n", "248.75: 2\n", "5.97: 1\n", "297.85: 2\n", "25.14: 2\n", "233.03: 2\n", "135.45: 1\n", "182.76: 1\n", "315.69: 1\n", "145.06: 1\n", "82.85: 1\n", "122.91: 1\n", "118.53: 1\n", "26.4: 2\n", "78.45: 3\n", "362.07: 1\n", "154.11: 1\n", "225.91: 1\n", "133.96: 1\n", "125.56: 2\n", "19.45: 1\n", "333.55: 1\n", "242.23: 1\n", "96.76: 1\n", "41.99: 1\n", "185.74: 1\n", "124.52: 2\n", "153.72: 1\n", "299.66: 1\n", "98.76: 1\n", "54.51: 2\n", "141.23: 1\n", "45.77: 1\n", "267.96: 1\n", "132.11: 1\n", "54.03: 3\n", "309.09: 1\n", "116.85: 1\n", "375.21: 1\n", "66.52: 3\n", "133.61: 1\n", "108.84: 1\n", "394.13: 1\n", "63.81: 1\n", "178.6: 1\n", "33.96: 1\n", "254.73: 1\n", "43.75: 1\n", "107.36: 1\n", "32.5: 1\n", "71.65: 1\n", "346.12: 1\n", "58.47: 2\n", "19.94: 1\n", "224.25: 1\n", "154.67: 1\n", "75.94: 1\n", "156.85: 1\n", "90.83: 1\n", "181.38: 1\n", "155.21: 1\n", "26.81: 1\n", "40.74: 1\n", "46.01: 1\n", "238.7: 1\n", "66.25: 1\n", "18.35: 3\n", "46.86: 2\n", "133.73: 1\n", "272.66: 1\n", "79.16: 1\n", "44.92: 1\n", "107.53: 2\n", "100.41: 1\n", "194.3: 1\n", "42.51: 1\n", "221.45: 1\n", "131.17: 1\n", "137.04: 1\n", "122.98: 2\n", "157.5: 1\n", "102.52: 1\n", "200.08: 1\n", "90.57: 2\n", "167.02: 1\n", "120.53: 1\n", "109.01: 1\n", "237.99: 3\n", "86.6: 1\n", "108.59: 1\n", "43.59: 1\n", "186.19: 1\n", "139.42: 1\n", "37.32: 1\n", "111.58: 1\n", "353.3: 1\n", "286.8: 1\n", "56.32: 1\n", "271.36: 1\n", "77.9: 1\n", "53.92: 1\n", "28.1: 2\n", "133.02: 2\n", "86.82: 2\n", "223.04: 1\n", "280.91: 1\n", "87.3: 2\n", "164.32: 1\n", "76.95: 1\n", "84.59: 1\n", "160.1: 3\n", "248.99: 1\n", "254.46: 1\n", "90.9: 2\n", "34.04: 1\n", "213.37: 1\n", "331.0: 1\n", "68.69: 3\n", "41.32: 1\n", "117.46: 1\n", "137.25: 1\n", "170.42: 1\n", "287.75: 1\n", "170.66: 1\n", "407.94: 1\n", "102.11: 2\n", "189.11: 2\n", "171.56: 1\n", "487.86: 1\n", "32.61: 1\n", "129.64: 1\n", "239.73: 1\n", "219.76: 1\n", "78.38: 1\n", "256.41: 1\n", "87.16: 2\n", "160.76: 1\n", "267.0: 1\n", "205.02: 2\n", "219.73: 1\n", "63.73: 1\n", "123.28: 1\n", "19.75: 1\n", "60.84: 1\n", "100.34: 1\n", "42.25: 1\n", "128.74: 1\n", "166.77: 2\n", "7.55: 1\n", "55.75: 1\n", "204.45: 1\n", "211.27: 1\n", "41.57: 1\n", "354.45: 1\n", "72.12: 1\n", "294.83: 1\n", "21.59: 2\n", "44.25: 1\n", "43.53: 1\n", "42.27: 1\n", "56.82: 2\n", "188.84: 1\n", "21.75: 1\n", "37.39: 2\n", "25.67: 1\n", "184.52: 1\n", "359.51: 1\n", "25.94: 2\n", "162.66: 1\n", "43.84: 2\n", "219.77: 1\n", "54.86: 3\n", "98.81: 1\n", "277.31: 1\n", "226.29: 1\n", "37.11: 1\n", "174.2: 3\n", "527.73: 1\n", "40.64: 2\n", "91.28: 1\n", "124.37: 1\n", "57.39: 1\n", "61.17: 1\n", "15.59: 1\n", "251.71: 1\n", "65.58: 2\n", "121.75: 2\n", "150.85: 1\n", "106.71: 1\n", "225.02: 1\n", "274.12: 1\n", "326.44: 1\n", "9.16: 1\n", "150.05: 2\n", "388.18: 1\n", "220.6: 1\n", "27.16: 1\n", "353.32: 1\n", "291.45: 1\n", "226.52: 1\n", "94.97: 3\n", "44.63: 1\n", "81.99: 1\n", "124.6: 1\n", "119.17: 2\n", "16.54: 2\n", "194.89: 1\n", "17.42: 1\n", "58.63: 1\n", "123.32: 1\n", "137.06: 1\n", "53.05: 5\n", "59.48: 1\n", "67.62: 2\n", "269.87: 1\n", "37.06: 1\n", "167.49: 2\n", "137.67: 1\n", "18.98: 1\n", "45.2: 1\n", "321.32: 1\n", "318.1: 1\n", "136.05: 1\n", "469.19: 1\n", "39.35: 1\n", "28.2: 1\n", "496.55: 1\n", "15.25: 1\n", "173.09: 1\n", "248.8: 1\n", "277.34: 1\n", "73.19: 1\n", "152.17: 2\n", "55.9: 1\n", "57.49: 1\n", "32.62: 1\n", "17.14: 1\n", "214.08: 1\n", "65.09: 1\n", "140.7: 1\n", "32.41: 1\n", "285.48: 1\n", "103.63: 1\n", "225.18: 2\n", "74.68: 2\n", "285.91: 1\n", "140.62: 1\n", "54.36: 1\n", "15.33: 1\n", "73.9: 2\n", "106.72: 1\n", "27.1: 1\n", "32.19: 2\n", "37.46: 1\n", "13.6: 1\n", "235.0: 1\n", "547.23: 1\n", "54.69: 1\n", "415.62: 1\n", "23.09: 1\n", "39.33: 1\n", "277.39: 1\n", "139.06: 1\n", "21.79: 1\n", "106.02: 1\n", "49.82: 1\n", "28.86: 2\n", "141.75: 2\n", "289.5: 1\n", "339.68: 1\n", "250.37: 1\n", "291.78: 1\n", "84.81: 1\n", "255.55: 1\n", "146.74: 2\n", "161.95: 1\n", "347.28: 1\n", "34.37: 2\n", "5.11: 1\n", "125.81: 1\n", "76.72: 1\n", "53.29: 1\n", "161.34: 1\n", "277.09: 1\n", "94.05: 2\n", "15.02: 1\n", "339.1: 1\n", "288.9: 1\n", "399.23: 1\n", "83.31: 1\n", "338.1: 1\n", "55.01: 2\n", "176.12: 1\n", "215.98: 1\n", "93.99: 2\n", "82.39: 1\n", "316.61: 1\n", "220.41: 1\n", "57.92: 1\n", "387.45: 1\n", "122.25: 1\n", "58.05: 1\n", "290.42: 1\n", "123.5: 1\n", "86.83: 2\n", "221.85: 1\n", "25.83: 1\n", "106.4: 1\n", "259.62: 1\n", "60.86: 1\n", "146.14: 1\n", "81.29: 1\n", "113.37: 1\n", "126.91: 2\n", "201.07: 1\n", "230.76: 1\n", "41.85: 1\n", "159.56: 1\n", "157.77: 1\n", "237.2: 2\n", "157.15: 1\n", "72.15: 2\n", "178.17: 1\n", "73.09: 1\n", "161.41: 1\n", "86.22: 1\n", "525.74: 1\n", "58.06: 2\n", "353.46: 1\n", "84.73: 1\n", "23.33: 3\n", "293.34: 1\n", "75.2: 2\n", "65.27: 1\n", "99.0: 1\n", "141.88: 1\n", "53.6: 1\n", "306.52: 1\n", "158.74: 1\n", "115.87: 1\n", "81.75: 1\n", "98.68: 2\n", "273.8: 1\n", "182.38: 1\n", "290.85: 1\n", "122.83: 2\n", "86.41: 2\n", "170.02: 1\n", "56.49: 2\n", "169.22: 1\n", "54.99: 1\n", "77.03: 1\n", "306.51: 1\n", "258.41: 1\n", "16.15: 2\n", "103.2: 1\n", "377.93: 1\n", "195.88: 1\n", "121.74: 1\n", "114.2: 1\n", "226.86: 1\n", "162.98: 1\n", "53.15: 1\n", "276.19: 1\n", "30.29: 1\n", "57.05: 2\n", "30.94: 3\n", "40.14: 1\n", "31.68: 1\n", "206.99: 1\n", "15.22: 1\n", "110.31: 1\n", "108.19: 1\n", "217.21: 1\n", "96.75: 1\n", "275.86: 1\n", "216.58: 1\n", "168.51: 1\n", "46.25: 2\n", "249.63: 1\n", "63.09: 1\n", "201.17: 1\n", "287.07: 1\n", "78.25: 1\n", "47.29: 2\n", "256.88: 1\n", "199.11: 1\n", "142.67: 1\n", "259.92: 1\n", "287.44: 1\n", "443.25: 1\n", "444.2: 1\n", "73.65: 1\n", "227.87: 1\n", "149.26: 2\n", "20.78: 1\n", "61.97: 2\n", "127.56: 1\n", "333.0: 1\n", "146.65: 1\n", "469.33: 1\n", "139.97: 2\n", "30.62: 1\n", "113.8: 1\n", "142.88: 1\n", "169.28: 1\n", "373.94: 1\n", "353.94: 1\n", "143.59: 1\n", "102.38: 1\n", "231.96: 1\n", "23.04: 2\n", "87.27: 1\n", "650.09: 1\n", "36.3: 1\n", "61.61: 1\n", "172.69: 1\n", "144.63: 1\n", "79.65: 1\n", "232.19: 1\n", "54.88: 1\n", "173.88: 1\n", "45.59: 1\n", "98.75: 1\n", "198.29: 1\n", "60.48: 1\n", "176.06: 1\n", "70.95: 1\n", "25.22: 1\n", "176.64: 1\n", "203.3: 1\n", "166.62: 1\n", "30.75: 2\n", "469.5: 1\n", "82.29: 1\n", "252.72: 1\n", "22.14: 1\n", "54.15: 1\n", "281.33: 1\n", "247.8: 1\n", "299.2: 1\n", "236.2: 1\n", "157.48: 1\n", "107.05: 1\n", "16.12: 3\n", "89.38: 4\n", "88.97: 1\n", "110.68: 1\n", "108.72: 1\n", "314.02: 1\n", "293.35: 1\n", "131.12: 1\n", "251.43: 1\n", "225.37: 1\n", "82.88: 1\n", "270.77: 1\n", "83.1: 2\n", "146.52: 1\n", "133.38: 1\n", "103.3: 1\n", "128.82: 2\n", "215.49: 1\n", "131.33: 1\n", "66.56: 1\n", "253.14: 1\n", "16.28: 1\n", "65.55: 2\n", "24.42: 2\n", "56.18: 1\n", "149.1: 1\n", "182.73: 1\n", "43.51: 2\n", "109.63: 3\n", "57.36: 1\n", "443.17: 1\n", "70.45: 1\n", "124.2: 1\n", "331.5: 1\n", "325.31: 1\n", "18.54: 2\n", "366.67: 1\n", "63.26: 1\n", "514.58: 1\n", "105.55: 1\n", "78.92: 1\n", "84.37: 1\n", "269.76: 1\n", "86.87: 2\n", "33.09: 2\n", "342.73: 1\n", "19.04: 2\n", "80.49: 1\n", "150.44: 1\n", "36.37: 1\n", "101.08: 1\n", "170.17: 1\n", "204.19: 1\n", "147.37: 1\n", "145.12: 1\n", "71.19: 1\n", "88.68: 1\n", "160.65: 2\n", "184.41: 1\n", "92.61: 2\n", "7.64: 1\n", "313.62: 1\n", "355.7: 1\n", "207.71: 1\n", "84.16: 1\n", "38.33: 1\n", "178.48: 1\n", "195.87: 1\n", "156.8: 1\n", "430.88: 1\n", "53.48: 1\n", "51.24: 1\n", "173.78: 1\n", "115.82: 2\n", "22.46: 1\n", "245.84: 2\n", "172.19: 1\n", "131.39: 1\n", "318.47: 1\n", "306.69: 1\n", "30.71: 1\n", "182.62: 1\n", "104.73: 1\n", "103.75: 1\n", "94.79: 1\n", "66.42: 1\n", "184.86: 1\n", "132.81: 1\n", "176.07: 1\n", "147.59: 1\n", "255.99: 1\n", "253.76: 1\n", "107.91: 1\n", "15.7: 1\n", "64.02: 1\n", "250.21: 1\n", "137.95: 1\n", "11.18: 1\n", "162.62: 1\n", "67.01: 1\n", "48.6: 2\n", "236.83: 1\n", "52.5: 1\n", "104.41: 1\n", "169.45: 1\n", "49.21: 1\n", "34.13: 1\n", "177.29: 1\n", "102.51: 1\n", "55.59: 2\n", "140.84: 1\n", "81.68: 1\n", "75.77: 2\n", "122.44: 1\n", "123.05: 1\n", "64.26: 1\n", "257.22: 1\n", "452.51: 1\n", "292.97: 1\n", "183.5: 1\n", "44.36: 1\n", "103.74: 1\n", "121.11: 1\n", "150.41: 1\n", "84.25: 1\n", "47.77: 1\n", "63.63: 1\n", "505.43: 1\n", "146.43: 1\n", "53.82: 2\n", "166.67: 1\n", "150.18: 1\n", "483.91: 1\n", "55.69: 1\n", "27.8: 2\n", "166.99: 1\n", "127.42: 1\n", "334.71: 1\n", "282.35: 1\n", "47.36: 1\n", "74.6: 1\n", "57.14: 1\n", "373.96: 1\n", "291.49: 1\n", "127.31: 1\n", "180.46: 1\n", "148.68: 1\n", "28.41: 1\n", "115.46: 1\n", "192.18: 1\n", "689.65: 1\n", "227.3: 1\n", "310.89: 1\n", "102.8: 1\n", "260.34: 1\n", "131.7: 1\n", "198.67: 2\n", "305.89: 1\n", "11.61: 1\n", "345.61: 1\n", "275.64: 1\n", "247.35: 1\n", "81.43: 1\n", "111.73: 1\n", "91.61: 1\n", "223.37: 1\n", "196.39: 1\n", "49.36: 4\n", "360.61: 1\n", "105.59: 2\n", "153.02: 1\n", "274.47: 1\n", "29.54: 1\n", "146.86: 1\n", "703.23: 1\n", "576.48: 1\n", "69.61: 3\n", "73.79: 2\n", "458.12: 1\n", "278.25: 1\n", "434.2: 1\n", "271.77: 1\n", "82.5: 2\n", "41.45: 1\n", "305.01: 1\n", "272.0: 1\n", "85.84: 1\n", "22.73: 1\n", "735.19: 1\n", "454.29: 1\n", "95.56: 1\n", "80.13: 2\n", "146.35: 1\n", "29.49: 1\n", "56.83: 1\n", "263.54: 1\n", "334.87: 1\n", "167.67: 1\n", "168.43: 1\n", "365.67: 1\n", "342.72: 1\n", "298.59: 1\n", "207.83: 1\n", "527.07: 1\n", "143.77: 1\n", "251.63: 1\n", "227.07: 2\n", "245.69: 1\n", "20.87: 1\n", "216.46: 1\n", "17.57: 1\n", "130.35: 1\n", "139.24: 1\n", "147.25: 1\n", "67.34: 2\n", "151.21: 1\n", "81.65: 1\n", "232.09: 1\n", "99.36: 1\n", "646.59: 1\n", "70.29: 1\n", "217.33: 1\n", "463.86: 1\n", "272.67: 1\n", "151.54: 1\n", "456.17: 1\n", "239.39: 1\n", "60.07: 1\n", "10.41: 1\n", "248.27: 1\n", "326.61: 1\n", "146.93: 1\n", "23.71: 1\n", "47.88: 2\n", "334.28: 1\n", "74.44: 1\n", "48.99: 1\n", "373.42: 1\n", "202.61: 1\n", "289.0: 1\n", "69.77: 1\n", "48.85: 1\n", "192.33: 1\n", "27.86: 2\n", "106.26: 1\n", "79.17: 1\n", "44.42: 1\n", "40.86: 1\n", "152.91: 1\n", "59.53: 4\n", "51.4: 1\n", "47.42: 1\n", "34.38: 1\n", "109.46: 1\n", "158.2: 1\n", "112.33: 2\n", "149.4: 1\n", "314.48: 1\n", "105.43: 2\n", "326.34: 2\n", "152.78: 1\n", "32.95: 1\n", "123.22: 1\n", "65.05: 1\n", "283.68: 1\n", "359.15: 1\n", "35.41: 2\n", "41.65: 1\n", "476.3: 1\n", "237.03: 2\n", "33.22: 1\n", "92.33: 1\n", "271.92: 1\n", "257.2: 1\n", "239.28: 1\n", "137.02: 1\n", "51.07: 1\n", "396.68: 1\n", "90.66: 1\n", "89.69: 2\n", "183.93: 1\n", "287.93: 1\n", "539.68: 1\n", "220.45: 1\n", "249.87: 1\n", "65.3: 3\n", "204.95: 1\n", "11.42: 1\n", "339.24: 1\n", "163.19: 2\n", "154.29: 1\n", "138.78: 1\n", "612.48: 1\n", "244.15: 1\n", "198.46: 1\n", "46.5: 1\n", "61.91: 1\n", "15.38: 1\n", "633.21: 1\n", "52.45: 1\n", "211.7: 1\n", "499.74: 1\n", "217.18: 1\n", "243.09: 1\n", "210.03: 1\n", "37.43: 2\n", "74.13: 2\n", "137.19: 1\n", "502.75: 1\n", "108.94: 1\n", "168.44: 1\n", "32.35: 1\n", "192.72: 1\n", "82.23: 1\n", "386.78: 1\n", "136.55: 1\n", "119.69: 1\n", "287.81: 1\n", "383.29: 1\n", "128.47: 1\n", "256.53: 1\n", "165.08: 1\n", "323.06: 1\n", "226.69: 1\n", "247.44: 1\n", "32.78: 2\n", "233.96: 1\n", "41.78: 1\n", "98.72: 1\n", "326.9: 1\n", "311.73: 1\n", "292.41: 1\n", "92.52: 1\n", "22.19: 1\n", "319.17: 1\n", "27.42: 1\n", "615.39: 1\n", "389.9: 1\n", "306.09: 1\n", "74.47: 1\n", "333.1: 1\n", "73.91: 2\n", "318.39: 1\n", "146.07: 1\n", "137.21: 1\n", "51.74: 2\n", "202.46: 2\n", "26.18: 1\n", "166.57: 1\n", "316.06: 1\n", "42.79: 2\n", "183.19: 1\n", "198.34: 1\n", "263.98: 1\n", "707.26: 1\n", "52.78: 1\n", "14.73: 4\n", "183.66: 1\n", "51.56: 1\n", "167.8: 1\n", "134.21: 1\n", "223.39: 1\n", "117.54: 1\n", "174.06: 1\n", "126.65: 1\n", "37.07: 2\n", "47.84: 1\n", "186.28: 1\n", "295.89: 1\n", "149.6: 1\n", "180.16: 1\n", "75.79: 1\n", "491.56: 1\n", "189.91: 1\n", "42.2: 2\n", "134.19: 1\n", "37.29: 2\n", "267.23: 1\n", "187.94: 1\n", "69.81: 1\n", "62.55: 1\n", "391.88: 1\n", "113.35: 1\n", "70.02: 1\n", "38.37: 1\n", "181.69: 2\n", "67.23: 2\n", "460.96: 1\n", "57.27: 1\n", "294.9: 1\n", "17.73: 1\n", "132.61: 2\n", "93.55: 2\n", "101.16: 1\n", "25.57: 1\n", "17.62: 1\n", "41.64: 1\n", "20.08: 1\n", "716.49: 1\n", "94.71: 1\n", "133.42: 1\n", "180.86: 1\n", "296.39: 1\n", "482.64: 1\n", "251.95: 1\n", "242.64: 1\n", "297.03: 1\n", "134.38: 1\n", "456.16: 2\n", "62.37: 1\n", "94.92: 1\n", "295.25: 1\n", "190.61: 1\n", "101.15: 4\n", "266.22: 1\n", "129.49: 1\n", "115.71: 1\n", "621.58: 1\n", "97.11: 1\n", "102.7: 3\n", "141.5: 1\n", "341.32: 1\n", "47.8: 2\n", "182.4: 1\n", "34.42: 1\n", "58.19: 2\n", "127.68: 1\n", "110.6: 1\n", "86.2: 1\n", "682.25: 1\n", "108.54: 1\n", "153.99: 1\n", "123.52: 2\n", "413.14: 1\n", "41.22: 3\n", "83.22: 1\n", "218.71: 1\n", "55.57: 1\n", "73.01: 1\n", "202.54: 1\n", "143.22: 1\n", "90.25: 1\n", "316.07: 1\n", "46.24: 1\n", "336.46: 1\n", "23.05: 2\n", "116.6: 1\n", "32.13: 1\n", "349.85: 1\n", "38.11: 2\n", "282.87: 1\n", "179.85: 1\n", "450.07: 1\n", "237.23: 2\n", "79.26: 4\n", "113.6: 2\n", "260.17: 1\n", "167.35: 2\n", "38.38: 1\n", "97.45: 1\n", "174.39: 2\n", "38.0: 1\n", "208.73: 1\n", "152.19: 1\n", "104.3: 1\n", "278.54: 1\n", "44.2: 1\n", "49.01: 1\n", "192.84: 2\n", "497.07: 1\n", "213.88: 1\n", "318.69: 1\n", "249.85: 3\n", "151.29: 1\n", "125.62: 1\n", "96.13: 1\n", "178.86: 1\n", "96.59: 2\n", "188.1: 1\n", "171.9: 1\n", "101.6: 1\n", "24.6: 1\n", "48.62: 2\n", "345.37: 1\n", "73.62: 2\n", "152.8: 1\n", "250.13: 1\n", "144.74: 2\n", "191.72: 1\n", "148.7: 1\n", "99.18: 1\n", "112.85: 1\n", "477.55: 1\n", "343.63: 1\n", "96.17: 1\n", "221.4: 2\n", "17.95: 2\n", "200.22: 1\n", "267.81: 1\n", "99.94: 2\n", "179.45: 1\n", "43.29: 1\n", "187.78: 1\n", "172.77: 1\n", "64.88: 1\n", "171.54: 2\n", "354.42: 1\n", "41.37: 2\n", "46.46: 2\n", "181.97: 1\n", "105.05: 1\n", "314.18: 1\n", "79.94: 1\n", "49.54: 2\n", "48.05: 3\n", "253.91: 1\n", "229.57: 1\n", "219.66: 1\n", "48.78: 1\n", "40.43: 1\n", "143.41: 2\n", "120.35: 1\n", "272.44: 1\n", "164.02: 1\n", "31.84: 1\n", "128.9: 2\n", "351.69: 1\n", "81.81: 1\n", "32.89: 1\n", "128.15: 1\n", "24.51: 2\n", "88.39: 1\n", "127.63: 1\n", "158.04: 2\n", "388.66: 1\n", "675.66: 1\n", "158.5: 1\n", "94.26: 2\n", "50.79: 1\n", "99.05: 1\n", "168.52: 2\n", "112.2: 2\n", "72.03: 2\n", "240.09: 1\n", "238.21: 1\n", "193.54: 1\n", "109.92: 1\n", "594.45: 1\n", "69.55: 2\n", "42.85: 2\n", "51.77: 2\n", "112.34: 1\n", "233.23: 1\n", "442.5: 1\n", "30.21: 1\n", "103.19: 2\n", "163.16: 1\n", "200.82: 1\n", "72.21: 2\n", "44.29: 1\n", "102.3: 1\n", "42.78: 2\n", "241.81: 1\n", "200.02: 1\n", "171.26: 1\n", "74.48: 2\n", "132.19: 1\n", "253.53: 1\n", "114.37: 1\n", "547.71: 1\n", "281.66: 1\n", "82.69: 2\n", "236.59: 1\n", "143.71: 1\n", "56.67: 2\n", "76.39: 3\n", "68.06: 1\n", "53.62: 1\n", "477.4: 1\n", "138.71: 1\n", "471.21: 1\n", "125.54: 1\n", "177.27: 1\n", "201.02: 1\n", "61.67: 1\n", "220.64: 1\n", "188.22: 1\n", "110.2: 2\n", "375.1: 1\n", "333.34: 1\n", "192.13: 1\n", "119.14: 1\n", "66.0: 1\n", "235.91: 1\n", "275.81: 1\n", "98.2: 1\n", "185.68: 1\n", "307.77: 1\n", "208.81: 2\n", "64.35: 1\n", "175.99: 2\n", "55.06: 1\n", "57.37: 2\n", "51.86: 1\n", "128.03: 1\n", "213.9: 2\n", "603.57: 1\n", "435.84: 1\n", "83.29: 2\n", "52.35: 1\n", "290.04: 1\n", "51.21: 2\n", "43.78: 4\n", "41.23: 1\n", "293.48: 1\n", "86.85: 1\n", "52.49: 1\n", "507.77: 1\n", "83.02: 1\n", "123.79: 1\n", "141.37: 2\n", "68.88: 3\n", "273.55: 1\n", "500.92: 1\n", "150.78: 2\n", "24.99: 1\n", "118.24: 1\n", "160.64: 1\n", "195.37: 1\n", "119.38: 1\n", "161.21: 1\n", "496.38: 1\n", "169.41: 1\n", "229.27: 1\n", "112.81: 1\n", "470.8: 1\n", "17.58: 1\n", "163.35: 1\n", "183.69: 1\n", "35.61: 1\n", "237.21: 1\n", "358.05: 1\n", "157.17: 1\n", "64.62: 1\n", "504.3: 1\n", "230.12: 1\n", "141.06: 1\n", "145.14: 1\n", "138.6: 1\n", "253.99: 1\n", "31.18: 1\n", "235.58: 1\n", "34.72: 2\n", "73.28: 1\n", "478.37: 1\n", "88.92: 1\n", "268.17: 1\n", "337.4: 1\n", "127.12: 3\n", "148.93: 1\n", "86.25: 1\n", "148.76: 1\n", "20.39: 1\n", "367.11: 1\n", "129.87: 1\n", "358.17: 1\n", "28.93: 1\n", "129.8: 1\n", "18.13: 2\n", "346.11: 1\n", "119.74: 1\n", "397.66: 1\n", "229.48: 1\n", "31.41: 1\n", "81.2: 1\n", "168.01: 1\n", "42.45: 2\n", "163.59: 1\n", "19.98: 2\n", "33.91: 1\n", "196.23: 1\n", "357.59: 1\n", "179.03: 1\n", "128.38: 1\n", "24.59: 1\n", "51.26: 1\n", "18.1: 3\n", "429.75: 1\n", "122.53: 1\n", "58.99: 1\n", "191.49: 1\n", "203.78: 1\n", "362.91: 1\n", "244.27: 1\n", "304.58: 1\n", "64.48: 1\n", "284.52: 1\n", "29.62: 2\n", "172.56: 3\n", "111.09: 1\n", "130.57: 1\n", "451.4: 1\n", "51.89: 1\n", "162.21: 1\n", "20.19: 1\n", "295.53: 1\n", "355.66: 1\n", "241.3: 1\n", "67.24: 1\n", "224.4: 1\n", "29.73: 1\n", "329.93: 1\n", "161.15: 1\n", "31.7: 1\n", "19.64: 1\n", "406.13: 1\n", "45.82: 1\n", "585.3: 1\n", "560.04: 1\n", "215.42: 1\n", "91.74: 1\n", "177.59: 1\n", "106.27: 1\n", "33.86: 1\n", "265.11: 1\n", "250.99: 1\n", "322.24: 1\n", "115.25: 2\n", "224.06: 1\n", "54.0: 1\n", "355.99: 1\n", "191.17: 1\n", "212.37: 1\n", "309.49: 1\n", "99.97: 1\n", "330.35: 1\n", "460.31: 1\n", "88.98: 2\n", "137.78: 1\n", "219.98: 1\n", "78.54: 2\n", "37.98: 1\n", "71.69: 1\n", "494.68: 1\n", "124.3: 1\n", "46.33: 2\n", "320.95: 1\n", "86.44: 1\n", "244.23: 2\n", "87.11: 1\n", "30.76: 3\n", "32.82: 2\n", "34.06: 1\n", "229.09: 1\n", "220.4: 1\n", "7.17: 1\n", "105.24: 2\n", "90.18: 1\n", "46.92: 1\n", "170.83: 1\n", "320.41: 1\n", "220.83: 2\n", "173.79: 3\n", "231.2: 1\n", "93.88: 1\n", "412.61: 1\n", "108.65: 1\n", "5.55: 1\n", "158.84: 1\n", "188.68: 1\n", "80.61: 2\n", "615.2: 1\n", "52.75: 1\n", "245.36: 1\n", "69.2: 1\n", "216.74: 1\n", "384.32: 2\n", "269.63: 1\n", "95.66: 1\n", "268.65: 1\n", "31.0: 1\n", "74.8: 1\n", "139.96: 1\n", "192.64: 1\n", "347.64: 1\n", "61.43: 2\n", "219.28: 1\n", "353.8: 1\n", "261.0: 2\n", "140.91: 2\n", "31.2: 1\n", "451.84: 1\n", "29.74: 1\n", "490.71: 1\n", "185.83: 1\n", "115.06: 1\n", "268.73: 1\n", "296.45: 1\n", "114.74: 1\n", "190.1: 1\n", "64.97: 1\n", "197.01: 1\n", "161.6: 2\n", "144.06: 1\n", "466.33: 1\n", "189.79: 1\n", "363.46: 1\n", "551.9: 1\n", "153.06: 2\n", "397.23: 1\n", "374.56: 2\n", "183.58: 1\n", "183.04: 1\n", "80.11: 1\n", "137.91: 1\n", "53.89: 1\n", "79.12: 1\n", "505.75: 1\n", "391.35: 1\n", "185.6: 2\n", "215.09: 1\n", "39.44: 2\n", "70.0: 1\n", "36.91: 2\n", "254.31: 1\n", "247.62: 1\n", "175.04: 1\n", "399.71: 1\n", "5.38: 2\n", "184.29: 1\n", "109.82: 2\n", "198.69: 2\n", "138.22: 1\n", "342.75: 1\n", "32.9: 1\n", "193.81: 1\n", "173.87: 1\n", "221.19: 1\n", "139.45: 2\n", "406.27: 1\n", "34.03: 2\n", "170.86: 1\n", "60.87: 1\n", "89.48: 3\n", "195.78: 1\n", "60.33: 1\n", "17.83: 1\n", "57.26: 1\n", "246.66: 1\n", "322.47: 1\n", "166.8: 1\n", "196.22: 1\n", "57.28: 1\n", "409.35: 1\n", "356.87: 1\n", "199.03: 1\n", "71.31: 1\n", "376.96: 1\n", "455.63: 1\n", "257.87: 1\n", "117.5: 2\n", "492.13: 1\n", "271.72: 1\n", "176.27: 1\n", "221.32: 1\n", "249.14: 1\n", "59.6: 1\n", "393.36: 1\n", "49.89: 1\n", "155.52: 1\n", "76.48: 1\n", "183.25: 1\n", "537.5: 1\n", "26.77: 1\n", "24.55: 1\n", "119.56: 1\n", "90.87: 1\n", "86.23: 1\n", "76.41: 1\n", "141.15: 1\n", "121.4: 1\n", "84.13: 1\n", "51.12: 1\n", "95.83: 1\n", "184.7: 1\n", "95.3: 1\n", "123.91: 1\n", "32.23: 1\n", "44.47: 1\n", "130.45: 1\n", "29.4: 1\n", "452.54: 1\n", "132.86: 1\n", "176.87: 2\n", "76.68: 1\n", "150.94: 1\n", "78.03: 1\n", "77.42: 1\n", "302.4: 1\n", "180.42: 1\n", "277.88: 1\n", "543.28: 1\n", "204.37: 1\n", "298.8: 1\n", "283.12: 1\n", "47.65: 2\n", "128.8: 1\n", "77.5: 1\n", "52.66: 1\n", "212.33: 1\n", "400.36: 1\n", "585.94: 1\n", "14.7: 1\n", "37.65: 1\n", "67.14: 1\n", "328.57: 1\n", "384.75: 1\n", "178.71: 1\n", "349.87: 1\n", "381.38: 1\n", "105.32: 1\n", "405.92: 1\n", "62.94: 2\n", "80.94: 1\n", "79.58: 2\n", "45.16: 1\n", "166.12: 1\n", "68.24: 1\n", "331.48: 1\n", "161.72: 1\n", "26.27: 1\n", "54.32: 1\n", "241.79: 1\n", "135.1: 1\n", "273.76: 1\n", "157.99: 1\n", "469.14: 1\n", "82.91: 1\n", "130.42: 1\n", "141.61: 1\n", "336.68: 1\n", "229.18: 1\n", "64.28: 1\n", "272.29: 1\n", "109.15: 1\n", "135.05: 2\n", "16.06: 1\n", "51.9: 1\n", "229.4: 1\n", "108.49: 1\n", "28.01: 1\n", "85.8: 1\n", "61.77: 2\n", "278.67: 2\n", "194.59: 1\n", "197.4: 1\n", "183.17: 1\n", "395.39: 1\n", "316.39: 1\n", "109.66: 1\n", "121.71: 1\n", "224.76: 2\n", "284.66: 1\n", "10.83: 1\n", "47.01: 1\n", "213.98: 1\n", "36.07: 1\n", "167.72: 1\n", "47.48: 1\n", "21.63: 1\n", "328.79: 1\n", "115.34: 2\n", "12.36: 1\n", "81.27: 1\n", "259.7: 2\n", "57.08: 1\n", "569.75: 1\n", "219.75: 1\n", "127.88: 1\n", "129.84: 1\n", "77.63: 1\n", "11.28: 1\n", "5.28: 1\n", "68.8: 1\n", "197.95: 1\n", "34.02: 1\n", "213.68: 1\n", "41.55: 2\n", "165.24: 1\n", "54.14: 1\n", "281.03: 1\n", "82.64: 1\n", "70.67: 1\n", "629.7: 1\n", "177.6: 1\n", "29.06: 1\n", "382.39: 1\n", "173.86: 1\n", "21.22: 1\n", "23.35: 1\n", "461.22: 1\n", "401.69: 1\n", "128.39: 1\n", "24.57: 1\n", "290.83: 1\n", "213.85: 1\n", "69.69: 2\n", "175.79: 1\n", "143.55: 1\n", "329.58: 1\n", "323.45: 1\n", "187.87: 1\n", "27.61: 1\n", "29.95: 1\n", "152.97: 1\n", "19.41: 1\n", "70.49: 1\n", "199.74: 1\n", "420.46: 1\n", "60.68: 1\n", "152.35: 3\n", "137.28: 1\n", "139.84: 1\n", "78.43: 1\n", "135.83: 1\n", "183.67: 1\n", "46.41: 1\n", "229.78: 1\n", "77.06: 1\n", "111.44: 1\n", "36.5: 1\n", "85.98: 2\n", "31.81: 1\n", "40.11: 2\n", "95.7: 2\n", "40.49: 2\n", "131.98: 1\n", "46.91: 1\n", "24.2: 1\n", "84.79: 2\n", "120.9: 1\n", "158.8: 1\n", "159.62: 1\n", "48.97: 2\n", "357.81: 1\n", "266.87: 1\n", "51.17: 1\n", "121.22: 2\n", "38.6: 1\n", "226.65: 1\n", "129.2: 2\n", "40.15: 1\n", "82.97: 1\n", "235.46: 1\n", "188.21: 1\n", "290.29: 1\n", "207.66: 1\n", "54.31: 1\n", "244.56: 1\n", "264.9: 1\n", "38.54: 1\n", "167.85: 1\n", "120.2: 1\n", "333.56: 1\n", "127.28: 1\n", "61.45: 3\n", "215.73: 1\n", "184.81: 1\n", "55.84: 2\n", "81.7: 1\n", "75.87: 1\n", "33.13: 1\n", "120.22: 1\n", "130.91: 1\n", "73.05: 2\n", "14.26: 1\n", "48.66: 2\n", "24.72: 1\n", "85.41: 2\n", "115.84: 1\n", "124.18: 1\n", "93.39: 2\n", "29.76: 1\n", "153.46: 1\n", "388.77: 1\n", "31.47: 1\n", "385.18: 1\n", "145.97: 2\n", "220.28: 1\n", "93.14: 1\n", "147.13: 1\n", "294.94: 1\n", "135.69: 1\n", "106.84: 1\n", "19.14: 1\n", "138.31: 1\n", "91.29: 1\n", "146.38: 1\n", "58.01: 1\n", "117.82: 1\n", "243.62: 2\n", "43.89: 1\n", "60.79: 1\n", "213.29: 1\n", "116.25: 3\n", "25.53: 2\n", "21.39: 1\n", "42.9: 1\n", "109.47: 1\n", "286.79: 1\n", "131.36: 2\n", "180.29: 1\n", "43.71: 2\n", "118.13: 2\n", "9.93: 2\n", "333.62: 1\n", "30.14: 2\n", "227.45: 1\n", "106.49: 1\n", "198.49: 1\n", "20.69: 1\n", "134.65: 2\n", "104.63: 2\n", "167.73: 1\n", "40.83: 1\n", "366.27: 1\n", "14.66: 1\n", "311.46: 1\n", "131.71: 1\n", "26.85: 2\n", "80.44: 1\n", "40.13: 1\n", "140.14: 1\n", "230.05: 1\n", "52.03: 1\n", "321.64: 1\n", "105.83: 1\n", "319.29: 1\n", "59.33: 1\n", "80.79: 1\n", "237.16: 1\n", "40.4: 1\n", "221.14: 1\n", "201.3: 1\n", "231.05: 1\n", "10.62: 1\n", "2.02: 1\n", "68.81: 2\n", "260.3: 1\n", "24.56: 3\n", "252.66: 1\n", "22.66: 1\n", "54.73: 2\n", "45.27: 2\n", "249.13: 1\n", "487.2: 1\n", "106.98: 1\n", "61.93: 2\n", "124.59: 1\n", "56.84: 1\n", "230.17: 1\n", "60.39: 1\n", "58.88: 2\n", "263.43: 1\n", "241.35: 1\n", "19.8: 1\n", "146.83: 1\n", "68.94: 1\n", "33.87: 1\n", "293.94: 1\n", "48.14: 1\n", "65.57: 1\n", "102.64: 1\n", "330.58: 1\n", "242.36: 1\n", "140.51: 1\n", "155.65: 1\n", "13.98: 1\n", "227.72: 1\n", "295.69: 1\n", "507.43: 1\n", "146.81: 2\n", "347.0: 1\n", "182.98: 1\n", "49.98: 3\n", "148.8: 1\n", "191.4: 1\n", "164.28: 1\n", "199.68: 3\n", "55.05: 2\n", "114.65: 1\n", "14.41: 1\n", "287.66: 1\n", "156.08: 2\n", "40.97: 1\n", "139.87: 1\n", "66.72: 1\n", "195.01: 1\n", "161.66: 1\n", "210.01: 1\n", "66.41: 1\n", "131.46: 1\n", "203.09: 1\n", "100.51: 2\n", "64.4: 2\n", "24.43: 1\n", "133.41: 1\n", "179.68: 1\n", "70.34: 1\n", "22.08: 2\n", "384.82: 1\n", "91.55: 1\n", "200.57: 1\n", "92.63: 1\n", "168.32: 1\n", "60.06: 2\n", "101.99: 2\n", "79.49: 1\n", "36.38: 1\n", "17.88: 1\n", "167.94: 1\n", "6.38: 1\n", "233.95: 1\n", "64.37: 2\n", "161.74: 1\n", "70.7: 1\n", "93.21: 1\n", "42.93: 1\n", "99.87: 1\n", "68.23: 1\n", "52.73: 1\n", "315.32: 1\n", "410.98: 1\n", "47.07: 2\n", "75.56: 1\n", "160.59: 2\n", "396.34: 2\n", "127.7: 1\n", "11.19: 1\n", "72.89: 1\n", "57.42: 1\n", "227.6: 1\n", "0.64: 1\n", "251.41: 1\n", "123.72: 2\n", "63.22: 2\n", "470.01: 1\n", "54.53: 1\n", "64.3: 2\n", "364.99: 1\n", "158.37: 1\n", "72.98: 1\n", "154.72: 1\n", "247.65: 1\n", "136.18: 1\n", "76.56: 2\n", "213.58: 1\n", "271.9: 2\n", "388.27: 1\n", "162.95: 1\n", "489.79: 1\n", "8.4: 1\n", "169.4: 1\n", "24.27: 1\n", "60.31: 1\n", "168.94: 1\n", "176.57: 1\n", "186.84: 1\n", "109.31: 1\n", "168.62: 1\n", "29.33: 1\n", "123.27: 1\n", "41.27: 1\n", "77.48: 2\n", "61.46: 1\n", "42.67: 2\n", "287.04: 1\n", "33.49: 2\n", "21.99: 2\n", "17.91: 1\n", "48.55: 3\n", "68.42: 1\n", "34.95: 1\n", "69.83: 1\n", "88.61: 1\n", "31.9: 1\n", "34.86: 1\n", "25.1: 2\n", "34.49: 1\n", "282.46: 1\n", "19.53: 1\n", "271.78: 1\n", "165.53: 1\n", "163.31: 1\n", "203.63: 1\n", "46.39: 1\n", "59.88: 1\n", "12.55: 1\n", "184.69: 1\n", "30.08: 1\n", "269.38: 1\n", "130.88: 1\n", "185.55: 1\n", "154.52: 1\n", "64.72: 1\n", "81.91: 1\n", "21.72: 1\n", "145.42: 1\n", "40.09: 1\n", "11.93: 1\n", "70.97: 1\n", "44.83: 1\n", "53.81: 1\n", "16.1: 1\n", "272.38: 1\n", "240.41: 1\n", "266.53: 1\n", "41.87: 1\n", "169.92: 2\n", "97.7: 1\n", "312.32: 1\n", "295.93: 1\n", "272.85: 1\n", "68.93: 1\n", "562.34: 1\n", "153.3: 1\n", "126.88: 1\n", "22.22: 2\n", "130.67: 1\n", "274.79: 1\n", "363.37: 1\n", "133.51: 1\n", "315.29: 1\n", "98.78: 1\n", "101.58: 1\n", "314.54: 1\n", "90.98: 1\n", "275.43: 1\n", "72.1: 1\n", "43.18: 2\n", "289.88: 1\n", "66.95: 2\n", "51.06: 1\n", "56.31: 2\n", "107.43: 2\n", "114.17: 1\n", "112.6: 1\n", "15.45: 1\n", "174.92: 1\n", "310.07: 1\n", "71.95: 1\n", "147.65: 2\n", "79.02: 1\n", "153.76: 1\n", "101.19: 1\n", "15.04: 1\n", "172.89: 2\n", "204.02: 1\n", "17.93: 2\n", "240.52: 1\n", "122.02: 1\n", "219.63: 1\n", "270.96: 1\n", "17.74: 1\n", "172.49: 1\n", "96.26: 2\n", "89.85: 2\n", "76.91: 2\n", "301.96: 1\n", "216.38: 1\n", "77.96: 2\n", "237.12: 1\n", "112.48: 2\n", "228.34: 1\n", "50.84: 1\n", "243.3: 1\n", "24.32: 1\n", "182.47: 1\n", "77.57: 1\n", "134.47: 2\n", "117.53: 1\n", "180.36: 1\n", "81.18: 1\n", "22.57: 1\n", "65.96: 1\n", "148.51: 1\n", "154.87: 1\n", "251.02: 1\n", "50.81: 1\n", "56.56: 1\n", "147.67: 1\n", "42.11: 1\n", "150.83: 1\n", "272.27: 1\n", "38.7: 2\n", "482.99: 1\n", "35.36: 2\n", "87.38: 1\n", "81.52: 1\n", "84.55: 3\n", "166.21: 1\n", "96.02: 1\n", "104.45: 2\n", "509.63: 1\n", "277.04: 1\n", "65.79: 1\n", "8.0: 1\n", "8.58: 1\n", "177.15: 2\n", "118.88: 1\n", "24.14: 1\n", "100.77: 1\n", "97.84: 2\n", "17.48: 2\n", "94.2: 1\n", "152.58: 1\n", "150.79: 1\n", "17.97: 1\n", "97.16: 1\n", "45.19: 1\n", "156.12: 1\n", "42.08: 1\n", "208.48: 2\n", "38.5: 1\n", "73.26: 2\n", "94.84: 2\n", "49.85: 1\n", "7.18: 1\n", "2.15: 1\n", "45.28: 1\n", "167.14: 1\n", "101.42: 1\n", "81.84: 1\n", "10.23: 1\n", "72.4: 1\n", "9.84: 1\n", "48.11: 1\n", "148.92: 2\n", "131.75: 2\n", "202.88: 1\n", "58.97: 1\n", "96.07: 1\n", "212.58: 2\n", "12.53: 1\n", "39.4: 1\n", "75.46: 1\n", "148.19: 1\n", "43.94: 1\n", "188.27: 1\n", "149.24: 1\n", "38.92: 2\n", "111.48: 2\n", "99.93: 1\n", "137.15: 1\n", "46.7: 1\n", "192.63: 1\n", "81.96: 2\n", "139.32: 1\n", "35.15: 1\n", "160.06: 1\n", "83.35: 1\n", "638.13: 1\n", "66.3: 1\n", "41.84: 1\n", "96.87: 1\n", "52.91: 1\n", "236.87: 1\n", "167.59: 1\n", "78.86: 1\n", "10.52: 1\n", "161.54: 1\n", "174.02: 1\n", "149.0: 1\n", "400.49: 1\n", "103.31: 1\n", "130.13: 1\n", "184.06: 1\n", "184.92: 1\n", "225.99: 1\n", "175.0: 1\n", "113.01: 1\n", "17.4: 2\n", "107.24: 1\n", "73.22: 2\n", "172.59: 1\n", "52.98: 1\n", "125.72: 1\n", "397.62: 2\n", "180.6: 1\n", "28.27: 1\n", "291.75: 1\n", "140.89: 1\n", "211.78: 1\n", "207.53: 1\n", "105.97: 2\n", "58.77: 1\n", "131.56: 2\n", "27.2: 2\n", "184.08: 1\n", "9.2: 1\n", "32.2: 3\n", "62.19: 1\n", "58.84: 1\n", "118.12: 1\n", "335.08: 1\n", "145.15: 1\n", "31.66: 2\n", "71.82: 1\n", "277.69: 1\n", "97.75: 2\n", "120.14: 1\n", "163.55: 1\n", "15.6: 2\n", "302.19: 1\n", "50.23: 1\n", "108.17: 1\n", "282.21: 1\n", "21.78: 1\n", "119.37: 1\n", "53.14: 2\n", "56.89: 1\n", "268.33: 1\n", "81.24: 1\n", "34.21: 1\n", "169.27: 1\n", "122.0: 2\n", "228.92: 1\n", "390.32: 1\n", "3.82: 1\n", "49.72: 1\n", "26.04: 1\n", "124.35: 1\n", "18.23: 2\n", "162.61: 1\n", "69.31: 1\n", "86.53: 1\n", "109.1: 3\n", "141.31: 1\n", "74.21: 2\n", "261.06: 1\n", "167.68: 1\n", "71.78: 1\n", "178.96: 1\n", "191.52: 1\n", "34.5: 1\n", "191.53: 1\n", "151.61: 1\n", "225.61: 1\n", "32.92: 1\n", "139.81: 1\n", "46.76: 1\n", "54.34: 1\n", "129.72: 1\n", "17.59: 1\n", "45.66: 2\n", "112.93: 1\n", "187.86: 1\n", "21.71: 1\n", "188.6: 2\n", "420.62: 1\n", "99.79: 1\n", "31.14: 1\n", "50.2: 3\n", "20.38: 1\n", "249.01: 1\n", "152.62: 1\n", "299.61: 1\n", "82.81: 1\n", "16.04: 1\n", "24.07: 2\n", "145.62: 2\n", "35.94: 1\n", "161.26: 1\n", "218.02: 1\n", "284.92: 1\n", "178.85: 1\n", "121.14: 1\n", "12.45: 1\n", "408.96: 1\n", "142.83: 1\n", "141.84: 1\n", "32.31: 1\n", "192.37: 1\n", "165.78: 1\n", "19.42: 1\n", "24.1: 1\n", "27.06: 1\n", "12.52: 1\n", "27.31: 1\n", "88.46: 1\n", "75.71: 1\n", "156.99: 3\n", "34.69: 1\n", "96.94: 1\n", "38.13: 1\n", "187.14: 1\n", "278.39: 1\n", "217.61: 1\n", "133.03: 2\n", "20.95: 1\n", "248.18: 1\n", "85.13: 1\n", "60.32: 2\n", "133.65: 1\n", "233.8: 1\n", "381.74: 1\n", "8.47: 1\n", "257.6: 2\n", "46.18: 1\n", "189.93: 1\n", "173.26: 2\n", "196.4: 1\n", "146.96: 1\n", "45.54: 1\n", "246.76: 1\n", "36.55: 2\n", "377.73: 1\n", "41.15: 1\n", "31.87: 1\n", "187.88: 1\n", "220.88: 1\n", "206.46: 1\n", "41.14: 2\n", "25.8: 1\n", "155.09: 1\n", "160.24: 1\n", "167.07: 1\n", "19.77: 1\n", "26.88: 1\n", "351.52: 1\n", "22.81: 1\n", "76.22: 1\n", "142.57: 1\n", "11.03: 2\n", "128.17: 1\n", "303.65: 1\n", "86.9: 1\n", "176.45: 1\n", "48.35: 1\n", "199.45: 1\n", "25.97: 1\n", "222.21: 1\n", "138.65: 1\n", "263.82: 1\n", "306.36: 1\n", "186.37: 1\n", "128.22: 1\n", "140.5: 2\n", "23.23: 2\n", "146.72: 1\n", "50.34: 3\n", "85.75: 1\n", "45.97: 2\n", "153.82: 1\n", "45.06: 4\n", "121.51: 1\n", "49.45: 1\n", "34.05: 1\n", "32.33: 1\n", "14.3: 1\n", "221.82: 1\n", "219.31: 1\n", "161.49: 1\n", "27.18: 3\n", "89.17: 1\n", "166.1: 1\n", "93.56: 1\n", "357.76: 1\n", "127.41: 1\n", "129.16: 1\n", "153.7: 2\n", "81.4: 1\n", "390.02: 1\n", "152.03: 1\n", "62.38: 2\n", "85.83: 1\n", "153.91: 1\n", "114.57: 1\n", "95.0: 1\n", "102.85: 1\n", "237.94: 1\n", "65.33: 1\n", "305.13: 1\n", "47.33: 1\n", "66.33: 2\n", "153.95: 2\n", "71.24: 1\n", "280.89: 1\n", "306.63: 1\n", "177.57: 1\n", "116.86: 1\n", "26.64: 1\n", "14.4: 1\n", "294.56: 1\n", "35.77: 1\n", "92.56: 1\n", "64.34: 1\n", "214.33: 1\n", "463.7: 1\n", "20.43: 1\n", "37.76: 2\n", "82.06: 1\n", "172.71: 1\n", "128.85: 1\n", "116.7: 1\n", "209.3: 1\n", "93.6: 1\n", "319.8: 1\n", "163.7: 2\n", "499.02: 1\n", "32.72: 1\n", "94.98: 1\n", "256.82: 1\n", "41.34: 2\n", "257.72: 1\n", "113.7: 1\n", "140.36: 1\n", "19.27: 2\n", "104.71: 1\n", "38.68: 1\n", "186.29: 1\n", "243.63: 1\n", "145.52: 2\n", "415.81: 1\n", "16.97: 2\n", "78.58: 2\n", "58.5: 2\n", "30.18: 1\n", "165.34: 1\n", "130.82: 1\n", "107.37: 1\n", "172.8: 1\n", "236.44: 2\n", "50.63: 1\n", "55.42: 1\n", "94.3: 1\n", "34.81: 1\n", "210.55: 1\n", "4.32: 1\n", "31.58: 1\n", "41.81: 1\n", "218.31: 1\n", "120.41: 1\n", "174.11: 1\n", "20.14: 1\n", "37.59: 1\n", "120.55: 2\n", "267.29: 1\n", "11.91: 1\n", "220.04: 1\n", "16.16: 1\n", "290.07: 1\n", "91.06: 1\n", "262.66: 1\n", "19.38: 1\n", "42.1: 1\n", "135.98: 2\n", "263.42: 1\n", "132.2: 1\n", "4.67: 1\n", "64.46: 1\n", "51.73: 1\n", "76.17: 1\n", "55.67: 1\n", "7.31: 1\n", "94.8: 1\n", "167.31: 1\n", "45.17: 1\n", "322.3: 1\n", "191.91: 2\n", "87.63: 1\n", "79.41: 1\n", "169.09: 1\n", "68.44: 2\n", "83.23: 1\n", "72.91: 2\n", "39.74: 1\n", "37.66: 3\n", "488.8: 1\n", "273.98: 1\n", "11.75: 1\n", "93.52: 1\n", "240.05: 1\n", "206.7: 1\n", "18.44: 1\n", "21.07: 1\n", "184.89: 1\n", "23.38: 1\n", "60.41: 2\n", "157.32: 1\n", "118.58: 1\n", "40.39: 2\n", "176.19: 1\n", "223.97: 1\n", "154.18: 1\n", "366.31: 1\n", "50.35: 1\n", "17.89: 2\n", "43.45: 1\n", "114.72: 1\n", "37.6: 1\n", "98.34: 1\n", "87.43: 1\n", "186.76: 2\n", "413.63: 1\n", "66.27: 1\n", "139.2: 1\n", "167.1: 1\n", "19.0: 1\n", "428.3: 1\n", "181.24: 1\n", "16.33: 3\n", "216.52: 3\n", "170.84: 2\n", "469.02: 1\n", "86.97: 2\n", "110.88: 2\n", "140.29: 1\n", "41.16: 2\n", "130.79: 1\n", "124.61: 3\n", "171.86: 1\n", "235.04: 1\n", "256.62: 1\n", "33.94: 1\n", "49.18: 1\n", "198.41: 1\n", "329.91: 1\n", "73.33: 1\n", "393.42: 1\n", "26.53: 1\n", "56.59: 1\n", "25.63: 2\n", "197.76: 1\n", "25.48: 1\n", "210.99: 1\n", "222.92: 1\n", "10.86: 1\n", "62.42: 1\n", "87.74: 2\n", "34.1: 1\n", "39.17: 1\n", "72.51: 1\n", "11.49: 1\n", "107.25: 2\n", "278.08: 1\n", "434.6: 1\n", "161.51: 2\n", "41.24: 1\n", "82.09: 1\n", "183.64: 1\n", "53.7: 2\n", "19.65: 2\n", "97.86: 1\n", "152.95: 1\n", "326.46: 1\n", "229.89: 1\n", "204.85: 1\n", "47.72: 1\n", "34.74: 1\n", "268.04: 1\n", "240.65: 1\n", "83.71: 1\n", "27.45: 1\n", "66.99: 1\n", "106.09: 1\n", "122.23: 1\n", "14.37: 2\n", "50.17: 1\n", "19.19: 1\n", "382.36: 1\n", "179.35: 1\n", "61.73: 1\n", "243.97: 1\n", "93.71: 1\n", "131.05: 1\n", "30.56: 1\n", "57.58: 1\n", "111.77: 1\n", "449.0: 1\n", "54.71: 1\n", "288.66: 1\n", "274.93: 1\n", "106.77: 1\n", "53.58: 1\n", "48.19: 1\n", "312.22: 1\n", "257.39: 1\n", "65.92: 1\n", "233.02: 1\n", "36.39: 1\n", "40.27: 1\n", "239.01: 1\n", "412.73: 1\n", "24.76: 1\n", "40.54: 2\n", "9.14: 1\n", "231.29: 1\n", "357.07: 1\n", "184.79: 1\n", "23.67: 1\n", "84.96: 1\n", "350.32: 1\n", "124.48: 1\n", "165.52: 1\n", "148.46: 1\n", "33.5: 1\n", "134.56: 1\n", "114.3: 1\n", "26.09: 1\n", "55.26: 2\n", "22.3: 1\n", "146.8: 1\n", "36.06: 1\n", "236.9: 1\n", "85.92: 1\n", "16.05: 1\n", "164.21: 1\n", "267.35: 2\n", "293.1: 1\n", "154.06: 1\n", "65.65: 1\n", "19.49: 1\n", "50.68: 1\n", "357.3: 1\n", "278.7: 1\n", "44.06: 1\n", "231.45: 1\n", "123.78: 1\n", "85.21: 1\n", "34.54: 1\n", "265.9: 2\n", "135.97: 1\n", "22.12: 1\n", "137.51: 1\n", "244.31: 1\n", "481.88: 1\n", "82.46: 1\n", "83.14: 1\n", "164.8: 2\n", "15.69: 1\n", "209.04: 1\n", "120.48: 1\n", "37.73: 1\n", "188.97: 1\n", "42.34: 1\n", "109.62: 1\n", "119.97: 1\n", "366.74: 1\n", "77.02: 1\n", "352.36: 1\n", "136.09: 2\n", "115.65: 1\n", "68.04: 1\n", "311.37: 1\n", "10.51: 1\n", "261.95: 1\n", "126.13: 1\n", "271.06: 1\n", "126.7: 1\n", "50.65: 2\n", "147.34: 1\n", "54.83: 1\n", "144.19: 1\n", "116.39: 1\n", "235.59: 1\n", "39.84: 1\n", "223.38: 1\n", "239.21: 1\n", "211.89: 1\n", "190.71: 1\n", "18.31: 2\n", "57.17: 2\n", "158.17: 1\n", "3.99: 1\n", "33.02: 3\n", "17.75: 1\n", "140.71: 1\n", "42.83: 1\n", "314.69: 1\n", "380.47: 1\n", "132.71: 1\n", "123.66: 2\n", "218.06: 2\n", "62.72: 1\n", "39.56: 1\n", "136.69: 1\n", "122.42: 2\n", "44.23: 1\n", "346.46: 1\n", "68.72: 2\n", "62.17: 1\n", "213.14: 1\n", "60.66: 1\n", "28.71: 2\n", "112.27: 1\n", "36.53: 1\n", "198.51: 2\n", "28.12: 1\n", "173.05: 1\n", "56.44: 1\n", "15.26: 1\n", "346.72: 1\n", "71.74: 1\n", "48.33: 1\n", "58.95: 1\n", "107.34: 1\n", "15.07: 1\n", "190.83: 1\n", "52.43: 1\n", "58.9: 1\n", "28.06: 1\n", "200.6: 2\n", "62.21: 1\n", "179.44: 1\n", "272.43: 1\n", "212.98: 1\n", "32.97: 2\n", "37.33: 1\n", "199.08: 1\n", "60.11: 1\n", "28.4: 1\n", "14.52: 1\n", "290.17: 1\n", "125.32: 1\n", "213.39: 2\n", "53.26: 1\n", "154.51: 2\n", "151.46: 1\n", "15.15: 1\n", "13.9: 1\n", "20.85: 1\n", "111.31: 1\n", "100.61: 1\n", "98.8: 1\n", "83.81: 1\n", "146.48: 1\n", "22.75: 1\n", "56.71: 1\n", "55.23: 3\n", "52.71: 1\n", "119.51: 2\n", "116.17: 1\n", "213.52: 1\n", "191.08: 1\n", "130.53: 1\n", "51.72: 1\n", "42.86: 1\n", "143.0: 1\n", "353.92: 1\n", "286.21: 2\n", "223.65: 1\n", "112.3: 1\n", "213.89: 1\n", "9.55: 1\n", "43.86: 2\n", "28.79: 1\n", "25.61: 1\n", "134.12: 1\n", "88.12: 1\n", "358.78: 1\n", "32.27: 1\n", "60.6: 1\n", "308.34: 1\n", "48.74: 1\n", "420.98: 1\n", "107.47: 1\n", "173.45: 1\n", "282.15: 1\n", "82.73: 1\n", "20.49: 2\n", "241.22: 1\n", "126.99: 1\n", "8.62: 1\n", "25.79: 1\n", "481.96: 1\n", "97.14: 2\n", "282.42: 1\n", "86.19: 1\n", "295.35: 1\n", "119.9: 1\n", "127.1: 1\n", "170.85: 1\n", "58.16: 2\n", "45.89: 1\n", "36.65: 2\n", "18.09: 1\n", "113.61: 2\n", "98.63: 2\n", "23.41: 1\n", "123.0: 1\n", "255.33: 1\n", "69.53: 1\n", "72.61: 1\n", "124.43: 1\n", "15.92: 1\n", "44.88: 1\n", "10.61: 1\n", "69.67: 1\n", "223.0: 2\n", "137.27: 1\n", "204.22: 1\n", "41.51: 1\n", "167.95: 1\n", "106.32: 1\n", "70.61: 1\n", "45.83: 1\n", "481.9: 1\n", "211.05: 1\n", "108.42: 1\n", "98.09: 1\n", "113.66: 1\n", "254.14: 1\n", "26.49: 1\n", "24.09: 1\n", "79.32: 1\n", "326.06: 1\n", "278.48: 1\n", "405.2: 1\n", "60.49: 1\n", "136.65: 1\n", "149.25: 1\n", "89.52: 2\n", "122.18: 1\n", "238.16: 1\n", "249.18: 1\n", "82.33: 2\n", "22.27: 1\n", "78.09: 1\n", "15.12: 1\n", "25.85: 1\n", "68.13: 1\n", "204.28: 1\n", "167.66: 1\n", "281.89: 1\n", "235.11: 1\n", "40.96: 1\n", "34.52: 1\n", "18.43: 1\n", "112.68: 1\n", "47.37: 1\n", "197.38: 1\n", "140.65: 1\n", "6.3: 1\n", "190.23: 1\n", "110.03: 1\n", "54.47: 1\n", "16.84: 1\n", "39.27: 1\n", "269.17: 1\n", "39.12: 1\n", "107.9: 1\n", "101.91: 1\n", "166.41: 1\n", "158.65: 2\n", "201.16: 1\n", "17.78: 1\n", "38.36: 2\n", "18.19: 1\n", "25.99: 1\n", "37.93: 1\n", "34.33: 1\n", "216.05: 1\n", "33.17: 1\n", "229.99: 1\n", "189.43: 1\n", "67.85: 1\n", "119.65: 1\n", "260.73: 1\n", "51.71: 1\n", "162.24: 1\n", "151.18: 1\n", "73.21: 1\n", "97.44: 1\n", "34.36: 1\n", "14.18: 1\n", "297.79: 1\n", "39.24: 1\n", "58.38: 2\n", "136.39: 1\n", "135.66: 1\n", "141.91: 1\n", "92.71: 1\n", "31.02: 1\n", "98.07: 1\n", "232.73: 1\n", "102.9: 2\n", "85.45: 1\n", "215.5: 1\n", "209.18: 1\n", "91.49: 1\n", "39.67: 1\n", "135.89: 1\n", "186.6: 1\n", "25.34: 1\n", "144.49: 1\n", "16.89: 1\n", "323.87: 1\n", "146.97: 1\n", "263.67: 1\n", "85.73: 2\n", "35.85: 3\n", "113.15: 1\n", "49.91: 2\n", "211.56: 1\n", "17.69: 1\n", "135.39: 1\n", "41.43: 1\n", "180.25: 1\n", "94.6: 1\n", "228.83: 1\n", "207.01: 1\n", "116.37: 1\n", "143.32: 1\n", "162.55: 1\n", "171.46: 1\n", "25.7: 2\n", "305.18: 1\n", "111.86: 1\n", "27.0: 1\n", "43.11: 1\n", "94.88: 1\n", "259.28: 1\n", "214.64: 1\n", "197.14: 1\n", "28.14: 1\n", "287.06: 1\n", "11.94: 2\n", "235.01: 1\n", "80.18: 1\n", "150.81: 1\n", "36.29: 1\n", "58.45: 2\n", "176.78: 1\n", "132.27: 1\n", "208.91: 2\n", "47.11: 1\n", "47.3: 1\n", "25.25: 1\n", "150.37: 1\n", "28.69: 1\n", "86.94: 1\n", "23.83: 1\n", "116.18: 2\n", "57.41: 1\n", "100.76: 1\n", "50.77: 2\n", "155.01: 1\n", "60.28: 1\n", "260.7: 1\n", "7.27: 1\n", "16.51: 1\n", "100.98: 1\n", "81.8: 1\n", "197.49: 1\n", "56.22: 1\n", "84.74: 2\n", "23.66: 2\n", "293.36: 1\n", "150.32: 1\n", "278.72: 2\n", "19.34: 1\n", "145.38: 1\n", "96.3: 2\n", "313.47: 1\n", "235.18: 1\n", "20.29: 1\n", "66.46: 1\n", "90.99: 2\n", "313.27: 1\n", "135.73: 1\n", "88.72: 1\n", "33.07: 1\n", "177.4: 1\n", "282.8: 1\n", "37.8: 1\n", "70.26: 2\n", "38.42: 2\n", "10.88: 2\n", "209.12: 1\n", "11.57: 1\n", "167.65: 1\n", "54.74: 1\n", "31.12: 1\n", "18.84: 3\n", "16.78: 1\n", "82.37: 3\n", "211.76: 1\n", "45.45: 1\n", "225.65: 1\n", "12.84: 1\n", "89.51: 1\n", "143.58: 1\n", "381.95: 1\n", "151.11: 1\n", "159.74: 2\n", "216.12: 1\n", "27.58: 1\n", "163.99: 1\n", "33.44: 1\n", "6.53: 1\n", "29.12: 2\n", "91.31: 1\n", "5.63: 1\n", "171.73: 1\n", "47.21: 1\n", "23.94: 1\n", "103.66: 2\n", "228.45: 1\n", "68.12: 1\n", "19.11: 1\n", "117.93: 1\n", "58.7: 1\n", "259.13: 1\n", "126.5: 2\n", "55.18: 2\n", "364.95: 1\n", "162.26: 1\n", "224.39: 1\n", "11.33: 1\n", "181.51: 1\n", "122.97: 1\n", "123.47: 1\n", "112.22: 1\n", "21.08: 1\n", "128.2: 1\n", "14.32: 1\n", "42.03: 1\n", "327.96: 1\n", "73.2: 1\n", "32.48: 1\n", "300.48: 1\n", "103.35: 1\n", "39.25: 1\n", "14.03: 1\n", "18.11: 1\n", "28.29: 1\n", "188.55: 1\n", "28.88: 1\n", "31.3: 1\n", "121.5: 1\n", "22.18: 1\n", "32.32: 1\n", "26.68: 1\n", "95.22: 1\n", "155.49: 1\n", "205.06: 2\n", "204.63: 1\n", "117.51: 1\n", "66.79: 4\n", "47.82: 1\n", "365.48: 1\n", "254.69: 1\n", "180.13: 1\n", "129.45: 1\n", "57.68: 1\n", "21.83: 1\n", "94.29: 1\n", "93.95: 4\n", "221.27: 1\n", "220.91: 1\n", "76.31: 1\n", "66.49: 1\n", "123.6: 1\n", "19.08: 1\n", "158.81: 1\n", "24.64: 1\n", "163.13: 1\n", "147.07: 1\n", "102.47: 1\n", "102.89: 1\n", "364.91: 1\n", "77.56: 2\n", "240.96: 1\n", "87.71: 2\n", "31.63: 1\n", "39.7: 1\n", "199.41: 2\n", "357.78: 1\n", "39.79: 1\n", "53.36: 1\n", "35.55: 1\n", "81.46: 1\n", "82.8: 1\n", "188.38: 1\n", "161.76: 1\n", "74.58: 1\n", "122.17: 2\n", "134.88: 1\n", "122.43: 1\n", "335.49: 1\n", "71.43: 1\n", "65.88: 1\n", "59.43: 1\n", "305.94: 1\n", "109.98: 2\n", "118.79: 2\n", "96.15: 1\n", "27.03: 1\n", "351.23: 1\n", "148.29: 1\n", "37.22: 1\n", "28.05: 1\n", "294.32: 1\n", "37.89: 1\n", "134.6: 1\n", "212.16: 1\n", "292.61: 1\n", "221.96: 1\n", "57.84: 1\n", "235.05: 1\n", "18.74: 1\n", "27.47: 1\n", "16.27: 1\n", "161.99: 1\n", "13.89: 1\n", "103.84: 1\n", "121.81: 1\n", "100.03: 1\n", "150.26: 1\n", "84.02: 1\n", "154.35: 1\n", "8.92: 1\n", "216.4: 1\n", "30.04: 1\n", "89.63: 2\n", "218.1: 1\n", "71.09: 1\n", "238.86: 1\n", "509.24: 1\n", "172.72: 2\n", "42.72: 1\n", "195.46: 1\n", "60.03: 1\n", "132.32: 1\n", "140.26: 1\n", "162.17: 1\n", "48.5: 1\n", "135.59: 1\n", "158.6: 1\n", "23.07: 1\n", "103.51: 2\n", "261.93: 1\n", "221.3: 1\n", "71.08: 1\n", "114.23: 1\n", "110.01: 1\n", "58.15: 1\n", "114.87: 1\n", "19.89: 1\n", "232.83: 1\n", "122.95: 1\n", "80.36: 1\n", "94.35: 1\n", "55.1: 2\n", "21.82: 1\n", "99.88: 1\n", "23.15: 1\n", "175.5: 1\n", "84.27: 1\n", "139.27: 2\n", "143.24: 1\n", "43.66: 1\n", "142.64: 1\n", "217.42: 2\n", "38.57: 1\n", "149.49: 2\n", "230.51: 1\n", "61.35: 1\n", "352.72: 1\n", "260.77: 1\n", "20.46: 1\n", "15.71: 1\n", "5.05: 1\n", "78.96: 1\n", "124.64: 1\n", "169.43: 1\n", "33.33: 1\n", "76.69: 1\n", "62.2: 1\n", "93.17: 1\n", "271.64: 1\n", "83.38: 1\n", "278.91: 1\n", "22.95: 1\n", "18.93: 1\n", "210.46: 1\n", "81.37: 1\n", "144.18: 1\n", "106.16: 1\n", "132.02: 1\n", "29.11: 1\n", "9.12: 2\n", "122.6: 1\n", "34.8: 1\n", "61.9: 2\n", "334.4: 1\n", "23.37: 1\n", "152.1: 1\n", "164.89: 1\n", "26.93: 1\n", "17.63: 1\n", "26.2: 1\n", "163.82: 2\n", "170.8: 1\n", "214.59: 1\n", "78.24: 1\n", "310.28: 1\n", "13.2: 1\n", "42.23: 1\n", "75.85: 1\n", "150.97: 1\n", "18.97: 1\n", "116.62: 1\n", "88.23: 1\n", "32.06: 1\n", "267.94: 1\n", "209.23: 1\n", "122.77: 1\n", "129.43: 1\n", "81.15: 1\n", "10.43: 1\n", "194.93: 1\n", "80.43: 1\n", "109.56: 1\n", "30.45: 2\n", "142.89: 2\n", "306.27: 1\n", "7.48: 1\n", "235.49: 1\n", "42.43: 1\n", "68.97: 1\n", "51.55: 1\n", "80.88: 1\n", "31.38: 2\n", "91.77: 1\n", "28.31: 1\n", "88.15: 1\n", "32.51: 2\n", "44.8: 1\n", "81.32: 1\n", "231.95: 1\n", "117.71: 1\n", "11.43: 2\n", "110.69: 1\n", "99.69: 2\n", "42.37: 1\n", "335.52: 1\n", "198.27: 1\n", "131.04: 1\n", "18.65: 1\n", "30.81: 1\n", "13.47: 1\n", "9.15: 1\n", "215.66: 1\n", "85.55: 1\n", "17.44: 1\n", "20.88: 1\n", "54.75: 1\n", "38.98: 1\n", "30.98: 1\n", "63.71: 1\n", "21.7: 1\n", "101.49: 1\n", "70.15: 1\n", "28.57: 1\n", "141.19: 1\n", "9.4: 2\n", "14.62: 1\n", "99.13: 1\n", "18.29: 1\n", "185.94: 1\n", "147.88: 1\n", "205.28: 1\n", "430.03: 1\n", "15.68: 1\n", "55.48: 2\n", "18.05: 2\n", "48.73: 1\n", "67.67: 1\n", "90.5: 1\n", "118.77: 1\n", "335.32: 1\n", "32.84: 1\n", "42.59: 1\n", "17.53: 1\n", "26.92: 1\n", "39.16: 1\n", "125.15: 1\n", "228.9: 1\n", "15.48: 1\n", "216.99: 1\n", "22.7: 1\n", "68.02: 1\n", "472.66: 1\n", "74.77: 2\n", "258.62: 1\n", "38.46: 1\n", "122.16: 1\n", "15.19: 1\n", "334.09: 1\n", "86.58: 1\n", "15.96: 2\n", "61.06: 1\n", "48.98: 1\n", "113.07: 1\n", "155.92: 1\n", "147.55: 1\n", "81.26: 1\n", "327.71: 1\n", "72.2: 1\n", "125.98: 1\n", "25.16: 1\n", "122.55: 1\n", "225.39: 1\n", "15.0: 1\n", "285.85: 1\n", "80.31: 2\n", "284.61: 1\n", "270.47: 1\n", "134.31: 1\n", "34.61: 1\n", "210.56: 2\n", "216.9: 1\n", "137.56: 1\n", "130.76: 1\n", "120.68: 1\n", "42.15: 1\n", "43.01: 1\n", "232.9: 1\n", "47.98: 1\n", "366.7: 1\n", "308.02: 1\n", "209.35: 1\n", "40.61: 1\n", "380.3: 1\n", "36.62: 1\n", "282.64: 2\n", "105.08: 1\n", "55.36: 1\n", "146.56: 1\n", "27.53: 1\n", "90.73: 1\n", "8.66: 1\n", "201.51: 1\n", "174.45: 1\n", "224.05: 1\n", "45.29: 1\n", "27.73: 1\n", "74.83: 1\n", "24.05: 1\n", "121.67: 1\n", "33.25: 1\n", "358.02: 1\n", "3.73: 1\n", "101.07: 2\n", "184.11: 1\n", "16.55: 1\n", "232.85: 1\n", "110.09: 1\n", "115.28: 1\n", "179.95: 1\n", "58.32: 1\n", "205.14: 1\n", "31.06: 1\n", "109.0: 3\n", "320.52: 1\n", "14.6: 2\n", "155.94: 1\n", "293.39: 1\n", "342.29: 1\n", "389.34: 1\n", "16.71: 1\n", "152.77: 1\n", "100.56: 1\n", "115.31: 1\n", "165.5: 1\n", "128.35: 1\n", "83.43: 2\n", "28.66: 1\n", "137.29: 1\n", "208.82: 1\n", "174.68: 2\n", "102.81: 1\n", "287.63: 1\n", "119.28: 1\n", "100.7: 1\n", "20.7: 1\n", "7.02: 1\n", "173.1: 1\n", "28.77: 1\n", "18.32: 1\n", "243.61: 1\n", "147.73: 1\n", "136.24: 1\n", "14.95: 1\n", "144.71: 1\n", "75.64: 1\n", "144.78: 1\n", "127.75: 1\n", "174.52: 1\n", "133.78: 1\n", "145.96: 1\n", "103.81: 1\n", "25.28: 1\n", "19.2: 1\n", "22.09: 1\n", "29.66: 2\n", "66.07: 1\n", "117.05: 1\n", "22.91: 1\n", "21.81: 1\n", "20.48: 1\n", "19.24: 1\n", "89.39: 1\n", "13.32: 1\n", "54.97: 1\n", "111.54: 2\n", "151.9: 1\n", "36.17: 1\n", "205.54: 1\n", "57.44: 1\n", "37.67: 1\n", "197.45: 1\n", "57.67: 2\n", "36.78: 1\n", "325.56: 1\n", "188.0: 1\n", "47.44: 1\n", "94.33: 1\n", "28.16: 1\n", "11.01: 1\n", "60.82: 1\n", "87.31: 1\n", "39.0: 1\n", "146.7: 2\n", "7.32: 1\n", "30.85: 1\n", "171.13: 1\n", "128.84: 1\n", "68.32: 1\n", "176.01: 1\n", "58.33: 1\n", "67.26: 2\n", "206.31: 1\n", "25.09: 1\n", "157.21: 1\n", "65.2: 1\n", "62.78: 1\n", "137.89: 1\n", "75.32: 1\n", "27.32: 1\n", "36.85: 2\n", "16.24: 1\n", "113.67: 1\n", "18.39: 1\n", "1.61: 1\n", "3.76: 1\n", "452.64: 1\n", "6.72: 1\n", "21.36: 1\n", "135.53: 1\n", "87.55: 1\n", "23.45: 1\n", "19.39: 1\n", "179.58: 1\n", "340.63: 1\n", "265.31: 1\n", "33.03: 1\n", "131.67: 1\n", "64.71: 1\n", "18.78: 1\n", "112.23: 1\n", "65.17: 1\n", "42.14: 2\n", "330.59: 1\n", "147.61: 1\n", "18.03: 1\n", "142.78: 1\n", "19.86: 1\n", "28.39: 1\n", "25.23: 2\n", "134.25: 1\n", "128.32: 1\n", "23.82: 1\n", "129.34: 1\n", "39.39: 1\n", "95.95: 1\n", "79.73: 1\n", "69.5: 1\n", "34.62: 1\n", "263.37: 1\n", "89.25: 1\n", "154.01: 1\n", "94.31: 1\n", "10.95: 1\n", "9.42: 1\n", "21.46: 2\n", "89.97: 1\n", "22.44: 1\n", "192.51: 1\n", "6.89: 1\n", "174.73: 1\n", "31.44: 1\n", "116.71: 1\n", "196.01: 1\n", "2.69: 1\n", "137.09: 1\n", "157.0: 1\n", "116.36: 1\n", "153.0: 1\n", "204.69: 1\n", "132.26: 1\n", "162.15: 1\n", "71.26: 1\n", "216.36: 1\n", "357.77: 1\n", "40.91: 1\n", "27.13: 1\n", "137.13: 1\n", "92.35: 2\n", "350.55: 1\n", "119.4: 1\n", "164.27: 1\n", "191.51: 1\n", "89.84: 1\n", "258.82: 1\n", "116.38: 1\n", "115.1: 1\n", "383.62: 1\n", "48.61: 2\n", "133.32: 1\n", "212.22: 1\n", "124.02: 1\n", "263.7: 1\n", "22.79: 1\n", "86.34: 1\n", "165.2: 1\n", "28.99: 2\n", "44.58: 1\n", "240.44: 1\n", "19.4: 1\n", "218.82: 1\n", "163.23: 1\n", "138.33: 1\n", "316.41: 1\n", "14.29: 1\n", "30.47: 1\n", "41.68: 2\n", "19.43: 1\n", "107.42: 2\n", "200.83: 1\n", "53.23: 1\n", "264.72: 1\n", "33.8: 2\n", "83.58: 1\n", "280.03: 1\n", "152.46: 1\n", "49.19: 2\n", "58.13: 1\n", "67.59: 1\n", "67.44: 1\n", "11.81: 2\n", "120.71: 1\n", "116.41: 1\n", "264.07: 1\n", "77.97: 1\n", "140.18: 1\n", "255.38: 1\n", "172.26: 1\n", "160.15: 1\n", "51.51: 3\n", "256.51: 1\n", "137.83: 1\n", "56.25: 1\n", "382.71: 1\n", "43.56: 1\n", "113.12: 2\n", "325.6: 1\n", "18.16: 1\n", "318.75: 1\n", "42.77: 1\n", "19.01: 1\n", "14.53: 1\n", "193.9: 1\n", "279.28: 1\n", "4.09: 1\n", "22.47: 2\n", "12.06: 1\n", "196.51: 1\n", "288.93: 1\n", "5.4: 2\n", "99.53: 1\n", "177.46: 1\n", "43.57: 2\n", "216.6: 1\n", "93.5: 1\n", "118.01: 2\n", "280.52: 1\n", "273.61: 1\n", "137.54: 1\n", "62.92: 1\n", "146.24: 1\n", "90.69: 1\n", "95.14: 1\n", "90.37: 1\n", "155.15: 1\n", "16.31: 1\n", "84.86: 1\n", "169.5: 1\n", "88.8: 1\n", "30.28: 1\n", "49.03: 1\n", "91.16: 1\n", "22.65: 1\n", "191.93: 2\n", "16.75: 1\n", "20.04: 1\n", "65.51: 1\n", "343.25: 1\n", "90.53: 1\n", "215.96: 1\n", "102.06: 1\n", "181.57: 1\n", "240.4: 1\n", "106.37: 1\n", "141.86: 1\n", "96.42: 1\n", "20.65: 1\n", "178.35: 1\n", "116.98: 1\n", "92.07: 1\n", "104.49: 1\n", "17.43: 2\n", "26.39: 1\n", "5.19: 2\n", "21.76: 1\n", "33.3: 1\n", "143.21: 1\n", "95.63: 1\n", "31.29: 1\n", "200.94: 1\n", "24.96: 3\n", "91.47: 1\n", "141.46: 2\n", "19.21: 2\n", "163.95: 1\n", "16.76: 1\n", "173.44: 1\n", "225.04: 1\n", "38.63: 1\n", "67.43: 1\n", "53.43: 2\n", "204.74: 1\n", "20.68: 1\n", "131.83: 1\n", "109.51: 1\n", "73.97: 2\n", "19.93: 1\n", "175.49: 1\n", "268.26: 1\n", "185.92: 1\n", "72.43: 1\n", "16.47: 1\n", "4.77: 1\n", "70.94: 1\n", "34.73: 1\n", "139.26: 1\n", "136.32: 1\n", "283.88: 1\n", "24.12: 1\n", "79.48: 1\n", "35.32: 1\n", "153.93: 1\n", "128.1: 1\n", "324.38: 1\n", "106.17: 1\n", "6.31: 1\n", "398.09: 1\n", "59.87: 1\n", "235.14: 1\n", "183.18: 1\n", "231.81: 1\n", "129.35: 1\n", "98.95: 1\n", "22.26: 1\n", "162.57: 1\n", "184.33: 1\n", "149.78: 1\n", "14.08: 1\n", "233.16: 1\n", "76.6: 2\n", "46.95: 1\n", "89.98: 1\n", "279.09: 1\n", "23.06: 1\n", "42.7: 1\n", "83.7: 2\n", "184.91: 1\n", "8.09: 1\n", "69.45: 1\n", "70.9: 1\n", "93.73: 2\n", "147.93: 1\n", "172.53: 1\n", "77.31: 1\n", "202.35: 1\n", "374.95: 1\n", "30.5: 1\n", "32.85: 1\n", "31.11: 1\n", "19.66: 1\n", "215.85: 1\n", "53.47: 1\n", "14.89: 1\n", "183.88: 1\n", "109.87: 1\n", "11.66: 1\n", "14.58: 1\n", "67.03: 1\n", "448.08: 1\n", "59.57: 1\n", "5.82: 1\n", "15.81: 1\n", "93.57: 1\n", "81.11: 2\n", "85.2: 1\n", "12.65: 1\n", "110.33: 1\n", "63.45: 1\n", "11.95: 1\n", "71.57: 1\n", "337.26: 1\n", "50.61: 1\n", "66.86: 1\n", "172.95: 1\n", "15.85: 1\n", "102.65: 1\n", "229.39: 1\n", "159.15: 1\n", "11.76: 1\n", "13.91: 1\n", "79.03: 1\n", "25.3: 1\n", "394.41: 1\n", "84.35: 1\n", "30.89: 1\n", "73.78: 2\n", "102.37: 2\n", "246.07: 1\n", "39.09: 1\n", "83.59: 1\n", "172.02: 1\n", "16.34: 1\n", "199.35: 1\n", "344.72: 1\n", "70.48: 1\n", "477.54: 1\n", "219.33: 1\n", "85.1: 1\n", "323.63: 1\n", "353.06: 1\n", "52.9: 1\n", "159.83: 1\n", "118.32: 1\n", "320.58: 1\n", "315.61: 1\n", "45.81: 1\n", "400.73: 1\n", "42.56: 1\n", "67.47: 1\n", "239.88: 1\n", "112.5: 1\n", "172.21: 1\n", "100.02: 1\n", "168.67: 2\n", "265.84: 1\n", "197.03: 1\n", "51.58: 1\n", "26.02: 1\n", "96.62: 1\n", "197.31: 1\n", "698.77: 1\n", "585.38: 1\n", "168.61: 1\n", "296.04: 1\n", "296.32: 1\n", "94.72: 1\n", "187.45: 1\n", "118.31: 1\n", "203.07: 1\n", "241.25: 1\n", "65.49: 1\n", "144.87: 1\n", "9.29: 1\n", "296.22: 1\n", "103.15: 2\n", "166.09: 1\n", "350.36: 1\n", "198.21: 1\n", "271.48: 1\n", "162.44: 1\n", "331.89: 1\n", "270.44: 1\n", "338.5: 1\n", "107.68: 2\n", "162.22: 1\n", "97.08: 1\n", "51.98: 1\n", "76.2: 1\n", "41.47: 1\n", "13.23: 1\n", "25.31: 1\n", "181.82: 1\n", "11.09: 1\n", "92.42: 1\n", "111.79: 1\n", "403.83: 1\n", "72.14: 2\n", "302.25: 1\n", "110.85: 1\n", "45.13: 1\n", "49.48: 2\n", "348.76: 1\n", "157.35: 1\n", "187.4: 1\n", "51.76: 1\n", "258.57: 1\n", "301.08: 1\n", "37.24: 1\n", "190.78: 1\n", "274.15: 1\n", "41.67: 1\n", "138.34: 1\n", "153.17: 1\n", "116.83: 1\n", "93.05: 1\n", "217.37: 1\n", "331.29: 1\n", "172.44: 1\n", "170.78: 1\n", "87.9: 1\n", "133.39: 2\n", "112.97: 1\n", "136.79: 1\n", "62.35: 2\n", "70.1: 1\n", "253.16: 1\n", "30.16: 1\n", "319.21: 1\n", "358.7: 1\n", "147.16: 1\n", "544.78: 1\n", "23.54: 1\n", "383.66: 1\n", "573.72: 1\n", "55.64: 1\n", "25.56: 1\n", "106.7: 1\n", "223.51: 2\n", "195.13: 1\n", "96.18: 1\n", "59.81: 1\n", "375.23: 1\n", "281.17: 1\n", "75.42: 1\n", "102.59: 1\n", "60.19: 1\n", "23.85: 1\n", "225.47: 1\n", "103.16: 1\n", "145.36: 1\n", "195.26: 1\n", "621.87: 1\n", "366.57: 1\n", "105.64: 1\n", "360.52: 1\n", "131.86: 1\n", "173.96: 1\n", "101.45: 2\n", "98.93: 1\n", "84.1: 1\n", "243.94: 1\n", "240.14: 1\n", "156.39: 1\n", "226.12: 1\n", "182.66: 1\n", "270.68: 1\n", "308.18: 1\n", "217.35: 1\n", "28.76: 1\n", "94.66: 1\n", "142.04: 1\n", "254.52: 1\n", "169.42: 1\n", "69.41: 1\n", "182.55: 2\n", "129.66: 1\n", "442.36: 1\n", "71.46: 1\n", "276.5: 1\n", "234.12: 1\n", "201.28: 1\n", "117.69: 1\n", "253.58: 2\n", "513.79: 1\n", "50.19: 1\n", "48.09: 1\n", "54.72: 1\n", "178.01: 2\n", "365.44: 1\n", "176.52: 1\n", "543.79: 1\n", "171.78: 1\n", "30.95: 1\n", "74.69: 1\n", "290.45: 1\n", "284.63: 1\n", "76.57: 1\n", "77.18: 2\n", "42.3: 1\n", "101.89: 1\n", "196.84: 1\n", "144.33: 1\n", "345.63: 1\n", "145.49: 2\n", "150.77: 1\n", "420.01: 1\n", "235.16: 1\n", "270.65: 1\n", "65.25: 1\n", "293.91: 1\n", "169.18: 1\n", "65.48: 1\n", "214.39: 1\n", "347.81: 1\n", "236.77: 1\n", "146.55: 2\n", "49.25: 1\n", "177.8: 1\n", "178.41: 1\n", "647.17: 1\n", "71.76: 2\n", "197.62: 1\n", "124.01: 1\n", "546.29: 1\n", "397.13: 1\n", "128.48: 1\n", "351.29: 2\n", "174.16: 3\n", "462.62: 1\n", "302.21: 1\n", "27.44: 1\n", "352.7: 1\n", "83.15: 1\n", "82.43: 1\n", "79.59: 1\n", "112.84: 1\n", "195.53: 1\n", "131.62: 1\n", "271.3: 1\n", "143.17: 1\n", "417.39: 1\n", "363.47: 1\n", "317.68: 1\n", "177.83: 1\n", "232.37: 1\n", "54.66: 1\n", "50.13: 1\n", "235.44: 1\n", "208.55: 1\n", "347.67: 1\n", "111.33: 1\n", "31.76: 1\n", "290.03: 1\n", "46.31: 1\n", "154.45: 2\n", "401.05: 1\n", "158.77: 2\n", "192.68: 1\n", "399.89: 1\n", "289.52: 1\n", "217.3: 1\n", "196.97: 1\n", "406.67: 1\n", "129.14: 1\n", "84.82: 2\n", "50.08: 1\n", "358.16: 1\n", "278.85: 3\n", "155.58: 1\n", "113.62: 1\n", "153.21: 1\n", "138.21: 1\n", "180.65: 2\n", "54.29: 1\n", "136.03: 1\n", "116.67: 1\n", "44.81: 1\n", "353.47: 1\n", "219.03: 1\n", "74.15: 1\n", "64.42: 1\n", "406.18: 1\n", "28.98: 1\n", "269.22: 1\n", "91.68: 1\n", "55.65: 1\n", "328.83: 2\n", "421.96: 1\n", "233.48: 1\n", "70.04: 1\n", "62.7: 2\n", "211.2: 2\n", "170.19: 1\n", "429.35: 2\n", "109.79: 1\n", "94.67: 1\n", "158.62: 1\n", "341.16: 1\n", "171.96: 2\n", "154.73: 2\n", "58.27: 2\n", "185.97: 1\n", "187.39: 1\n", "195.24: 1\n", "45.87: 1\n", "47.16: 1\n", "45.98: 1\n", "270.45: 1\n", "176.91: 1\n", "40.81: 1\n", "99.57: 1\n", "46.19: 1\n", "587.66: 1\n", "42.96: 1\n", "227.25: 1\n", "174.27: 2\n", "189.29: 2\n", "88.3: 2\n", "90.17: 1\n", "238.58: 1\n", "382.81: 1\n", "151.84: 1\n", "434.48: 1\n", "30.12: 1\n", "196.81: 1\n", "79.52: 2\n", "394.2: 1\n", "485.39: 1\n", "15.52: 1\n", "88.56: 1\n", "78.77: 2\n", "183.47: 1\n", "128.63: 2\n", "266.72: 1\n", "271.74: 1\n", "69.99: 1\n", "371.14: 1\n", "175.45: 1\n", "76.5: 1\n", "184.3: 1\n", "71.4: 2\n", "295.12: 1\n", "117.4: 1\n", "484.62: 1\n", "130.32: 1\n", "369.33: 1\n", "518.92: 1\n", "123.86: 1\n", "20.52: 1\n", "496.43: 1\n", "54.41: 1\n", "127.02: 1\n", "9.79: 1\n", "99.12: 1\n", "20.37: 1\n", "211.19: 1\n", "280.61: 1\n", "54.54: 1\n", "136.73: 1\n", "80.97: 1\n", "231.65: 2\n", "193.91: 1\n", "162.25: 1\n", "629.59: 1\n", "237.0: 1\n", "190.04: 1\n", "33.18: 1\n", "227.44: 1\n", "18.25: 1\n", "202.19: 1\n", "153.49: 1\n", "88.38: 1\n", "218.26: 1\n", "54.35: 1\n", "110.86: 2\n", "278.64: 1\n", "370.74: 1\n", "140.92: 1\n", "109.59: 1\n", "97.1: 1\n", "124.04: 1\n", "120.23: 2\n", "185.44: 1\n", "55.81: 1\n", "99.92: 1\n", "344.12: 1\n", "106.15: 3\n", "66.78: 1\n", "177.12: 1\n", "236.66: 1\n", "172.03: 2\n", "152.3: 2\n", "29.52: 2\n", "163.73: 1\n", "283.26: 1\n", "162.51: 1\n", "152.66: 1\n", "178.74: 1\n", "290.68: 1\n", "57.01: 2\n", "71.12: 1\n", "163.58: 1\n", "280.74: 1\n", "12.54: 1\n", "117.11: 2\n", "172.73: 1\n", "394.25: 1\n", "91.19: 1\n", "634.24: 1\n", "13.4: 3\n", "147.76: 1\n", "154.96: 2\n", "209.59: 1\n", "49.46: 1\n", "452.17: 1\n", "505.51: 1\n", "129.38: 1\n", "107.57: 1\n", "182.39: 2\n", "300.91: 1\n", "43.49: 1\n", "159.4: 1\n", "145.23: 1\n", "378.79: 1\n", "148.56: 1\n", "245.17: 1\n", "187.44: 1\n", "155.98: 1\n", "307.16: 1\n", "207.55: 1\n", "375.78: 1\n", "48.57: 1\n", "568.6: 1\n", "107.3: 2\n", "334.72: 1\n", "432.76: 1\n", "140.8: 2\n", "186.31: 1\n", "29.48: 1\n", "465.08: 1\n", "320.36: 1\n", "126.74: 1\n", "769.4: 1\n", "13.37: 1\n", "439.52: 1\n", "261.8: 1\n", "203.66: 1\n", "243.76: 1\n", "152.08: 1\n", "217.0: 1\n", "245.79: 1\n", "208.64: 1\n", "424.19: 1\n", "176.63: 1\n", "160.32: 1\n", "124.46: 1\n", "105.22: 1\n", "77.35: 2\n", "326.35: 1\n", "269.35: 1\n", "379.92: 1\n", "175.34: 1\n", "79.84: 1\n", "298.03: 1\n", "64.39: 2\n", "33.83: 1\n", "45.6: 1\n", "87.91: 1\n", "184.38: 1\n", "279.22: 1\n", "224.37: 1\n", "25.68: 1\n", "65.66: 1\n", "23.19: 1\n", "71.18: 2\n", "233.67: 1\n", "769.05: 1\n", "171.99: 1\n", "57.7: 1\n", "97.56: 1\n", "272.32: 1\n", "58.12: 1\n", "298.06: 1\n", "157.08: 1\n", "13.82: 1\n", "232.53: 1\n", "39.14: 2\n", "190.77: 1\n", "271.5: 2\n", "270.56: 2\n", "120.8: 1\n", "159.71: 1\n", "200.32: 1\n", "122.19: 1\n", "220.8: 1\n", "128.26: 1\n", "81.94: 2\n", "114.35: 1\n", "354.34: 1\n", "87.0: 1\n", "609.87: 1\n", "282.92: 1\n", "400.35: 1\n", "85.02: 2\n", "476.57: 1\n", "504.1: 1\n", "89.76: 1\n", "109.44: 1\n", "383.56: 1\n", "45.52: 1\n", "828.49: 1\n", "34.87: 1\n", "399.11: 1\n", "597.34: 1\n", "129.12: 1\n", "170.51: 1\n", "21.05: 1\n", "379.77: 1\n", "21.74: 1\n", "125.75: 2\n", "272.94: 1\n", "59.18: 1\n", "192.2: 1\n", "77.22: 2\n", "139.08: 2\n", "145.18: 1\n", "335.84: 1\n", "56.61: 2\n", "237.72: 1\n", "197.27: 1\n", "127.94: 1\n", "158.59: 1\n", "299.54: 1\n", "84.67: 1\n", "345.01: 1\n", "724.57: 1\n", "181.01: 1\n", "261.82: 1\n", "31.75: 1\n", "31.43: 1\n", "189.38: 1\n", "206.47: 1\n", "149.51: 1\n", "122.07: 1\n", "60.59: 1\n", "17.86: 1\n", "155.14: 1\n", "242.4: 1\n", "87.49: 1\n", "170.73: 1\n", "99.21: 1\n", "410.22: 1\n", "156.81: 1\n", "165.86: 1\n", "344.0: 1\n", "45.88: 1\n", "105.27: 1\n", "103.32: 1\n", "103.36: 3\n", "15.93: 1\n", "244.29: 1\n", "237.74: 1\n", "240.64: 1\n", "260.66: 1\n", "229.33: 1\n", "69.72: 1\n", "25.75: 1\n", "11.53: 1\n", "117.08: 1\n", "6.14: 1\n", "129.95: 1\n", "119.42: 1\n", "781.05: 1\n", "157.19: 1\n", "231.56: 1\n", "372.62: 1\n", "109.11: 1\n", "289.75: 1\n", "165.12: 1\n", "124.77: 1\n", "151.27: 1\n", "44.65: 2\n", "82.03: 1\n", "167.52: 1\n", "108.76: 1\n", "160.75: 1\n", "112.4: 1\n", "133.45: 1\n", "237.28: 1\n", "221.13: 1\n", "263.06: 1\n", "67.22: 1\n", "28.15: 2\n", "436.92: 1\n", "98.33: 1\n", "320.16: 1\n", "55.0: 1\n", "475.13: 1\n", "547.16: 1\n", "316.0: 1\n", "27.25: 1\n", "180.73: 2\n", "66.19: 1\n", "236.62: 1\n", "119.43: 2\n", "333.57: 1\n", "344.01: 1\n", "34.67: 1\n", "98.73: 1\n", "106.81: 1\n", "247.25: 1\n", "42.97: 1\n", "75.78: 1\n", "455.53: 1\n", "47.83: 1\n", "171.17: 1\n", "43.69: 2\n", "331.75: 1\n", "55.45: 1\n", "17.25: 1\n", "241.4: 1\n", "143.19: 1\n", "392.21: 1\n", "110.72: 1\n", "130.41: 1\n", "351.37: 1\n", "27.7: 1\n", "145.79: 1\n", "429.04: 1\n", "269.66: 1\n", "64.64: 1\n", "61.49: 1\n", "55.12: 1\n", "195.11: 1\n", "33.26: 1\n", "155.66: 1\n", "104.15: 1\n", "26.1: 1\n", "112.59: 2\n", "210.34: 1\n", "95.82: 2\n", "39.45: 3\n", "205.01: 1\n", "82.58: 1\n", "325.05: 1\n", "127.49: 1\n", "162.86: 1\n", "71.81: 1\n", "44.35: 1\n", "306.75: 1\n", "29.22: 1\n", "56.21: 1\n", "463.15: 2\n", "167.81: 2\n", "533.18: 1\n", "269.18: 1\n", "236.22: 1\n", "466.59: 1\n", "391.03: 1\n", "119.15: 1\n", "49.59: 1\n", "342.39: 1\n", "129.94: 2\n", "394.14: 1\n", "113.32: 1\n", "259.38: 1\n", "166.07: 1\n", "27.84: 1\n", "185.43: 1\n", "64.43: 1\n", "113.09: 1\n", "150.9: 1\n", "232.77: 1\n", "133.21: 1\n", "105.28: 1\n", "67.94: 1\n", "83.98: 1\n", "172.84: 1\n", "279.63: 1\n", "63.41: 2\n", "40.71: 1\n", "160.19: 1\n", "268.1: 1\n", "128.52: 1\n", "152.63: 1\n", "257.53: 2\n", "165.6: 1\n", "38.74: 1\n", "564.24: 1\n", "333.45: 1\n", "225.69: 1\n", "228.4: 1\n", "346.24: 1\n", "120.17: 1\n", "147.57: 1\n", "168.4: 1\n", "329.64: 1\n", "148.71: 1\n", "204.36: 1\n", "273.29: 1\n", "50.99: 1\n", "394.79: 1\n", "75.35: 2\n", "52.53: 1\n", "21.15: 1\n", "208.25: 1\n", "32.71: 1\n", "475.05: 1\n", "298.5: 1\n", "247.84: 1\n", "317.03: 1\n", "99.26: 1\n", "240.1: 1\n", "43.28: 1\n", "209.41: 1\n", "220.99: 1\n", "554.8: 1\n", "344.32: 1\n", "171.85: 1\n", "550.84: 1\n", "95.53: 1\n", "156.74: 1\n", "199.12: 1\n", "215.88: 1\n", "218.65: 1\n", "167.11: 1\n", "513.22: 1\n", "127.03: 2\n", "569.22: 1\n", "131.78: 1\n", "498.03: 1\n", "152.93: 1\n", "238.43: 1\n", "200.11: 1\n", "259.81: 1\n", "73.25: 1\n", "194.25: 1\n", "98.41: 1\n", "208.47: 1\n", "163.11: 1\n", "196.0: 1\n", "61.39: 1\n", "212.66: 1\n", "560.86: 1\n", "350.1: 1\n", "209.89: 1\n", "196.16: 1\n", "86.59: 1\n", "367.9: 1\n", "116.23: 1\n", "23.29: 1\n", "9.1: 1\n", "133.7: 1\n", "367.55: 1\n", "249.42: 1\n", "79.42: 1\n", "250.66: 1\n", "303.1: 1\n", "333.75: 1\n", "586.77: 1\n", "257.76: 1\n", "481.27: 1\n", "208.44: 1\n", "14.21: 2\n", "102.17: 1\n", "322.55: 1\n", "152.4: 1\n", "43.26: 1\n", "371.92: 1\n", "41.62: 1\n", "163.71: 1\n", "284.27: 1\n", "93.77: 1\n", "277.87: 1\n", "86.81: 1\n", "67.99: 1\n", "439.31: 1\n", "236.65: 1\n", "765.83: 1\n", "473.45: 1\n", "562.23: 1\n", "74.54: 1\n", "418.51: 1\n", "63.24: 1\n", "77.54: 1\n", "183.73: 1\n", "38.77: 1\n", "142.56: 1\n", "321.08: 1\n", "75.65: 1\n", "263.14: 1\n", "15.56: 1\n", "173.91: 1\n", "161.8: 1\n", "519.8: 1\n", "481.92: 1\n", "297.61: 1\n", "295.75: 1\n", "97.43: 1\n", "31.92: 1\n", "923.4: 1\n", "60.38: 1\n", "127.86: 1\n", "189.22: 1\n", "259.66: 1\n", "113.25: 1\n", "428.52: 1\n", "241.08: 1\n", "111.07: 1\n", "429.33: 1\n", "100.89: 1\n", "56.4: 1\n", "343.75: 1\n", "130.55: 1\n", "132.38: 1\n", "165.07: 1\n", "143.14: 1\n", "78.67: 1\n", "228.73: 1\n", "135.42: 1\n", "151.89: 1\n", "636.56: 1\n", "311.1: 1\n", "328.19: 1\n", "245.39: 1\n", "364.33: 1\n", "63.21: 2\n", "172.17: 1\n", "399.67: 1\n", "180.21: 1\n", "174.03: 1\n", "221.33: 1\n", "81.97: 1\n", "358.76: 1\n", "195.2: 1\n", "467.25: 2\n", "44.76: 1\n", "269.06: 1\n", "219.72: 1\n", "34.2: 1\n", "370.94: 1\n", "577.66: 1\n", "30.26: 1\n", "223.27: 1\n", "53.09: 1\n", "238.36: 1\n", "479.74: 1\n", "124.86: 1\n", "304.24: 1\n", "254.19: 1\n", "45.94: 1\n", "87.61: 1\n", "375.38: 1\n", "171.41: 1\n", "168.84: 1\n", "64.69: 1\n", "69.47: 2\n", "334.8: 1\n", "106.79: 2\n", "87.8: 1\n", "33.21: 1\n", "395.76: 1\n", "44.85: 1\n", "823.69: 1\n", "305.85: 1\n", "159.87: 1\n", "289.15: 1\n", "150.25: 1\n", "305.87: 1\n", "99.98: 1\n", "395.23: 1\n", "389.87: 1\n", "279.98: 1\n", "181.56: 1\n", "142.69: 1\n", "218.0: 1\n", "173.17: 1\n", "12.87: 1\n", "101.53: 1\n", "211.13: 1\n", "60.56: 1\n", "217.28: 1\n", "206.4: 1\n", "315.97: 1\n", "262.92: 2\n", "439.96: 1\n", "144.8: 1\n", "248.09: 1\n", "499.63: 1\n", "49.32: 1\n", "208.01: 1\n", "136.38: 1\n", "192.24: 1\n", "198.75: 1\n", "376.42: 1\n", "105.17: 1\n", "32.44: 1\n", "41.69: 1\n", "702.7: 1\n", "54.92: 3\n", "226.9: 1\n", "328.8: 1\n", "169.15: 1\n", "12.17: 1\n", "230.63: 1\n", "55.5: 1\n", "142.42: 1\n", "324.0: 1\n", "422.46: 1\n", "47.97: 3\n", "97.3: 2\n", "280.67: 1\n", "95.41: 1\n", "241.13: 1\n", "322.65: 1\n", "74.05: 1\n", "204.6: 1\n", "677.49: 1\n", "33.74: 1\n", "44.69: 1\n", "156.96: 1\n", "197.56: 1\n", "16.4: 1\n", "62.81: 1\n", "180.49: 1\n", "244.0: 2\n", "116.78: 1\n", "80.48: 1\n", "51.54: 1\n", "114.18: 1\n", "549.63: 1\n", "209.11: 1\n", "73.43: 1\n", "120.04: 1\n", "110.51: 1\n", "92.54: 1\n", "66.8: 1\n", "154.19: 1\n", "85.52: 1\n", "349.06: 1\n", "240.7: 1\n", "331.82: 1\n", "189.06: 1\n", "272.15: 1\n", "225.17: 1\n", "60.83: 1\n", "215.04: 1\n", "688.94: 1\n", "393.51: 1\n", "54.02: 1\n", "38.51: 1\n", "310.24: 1\n", "253.96: 1\n", "101.97: 1\n", "372.87: 1\n", "188.65: 1\n", "501.43: 1\n", "502.77: 1\n", "665.06: 1\n", "181.07: 1\n", "253.5: 1\n", "242.43: 1\n", "37.34: 1\n", "149.55: 1\n", "266.19: 1\n", "459.02: 1\n", "35.03: 1\n", "96.93: 1\n", "95.84: 1\n", "310.57: 1\n", "167.19: 1\n", "133.68: 2\n", "107.32: 1\n", "46.28: 1\n", "327.17: 1\n", "221.37: 1\n", "117.45: 1\n", "267.58: 1\n", "656.8: 1\n", "40.89: 1\n", "142.58: 1\n", "182.95: 1\n", "581.66: 1\n", "156.94: 1\n", "123.82: 2\n", "97.17: 1\n", "469.88: 1\n", "259.97: 2\n", "19.47: 1\n", "129.86: 1\n", "688.96: 1\n", "288.28: 1\n", "121.76: 1\n", "84.14: 1\n", "246.6: 1\n", "65.16: 1\n", "206.82: 1\n", "22.35: 1\n", "794.41: 1\n", "893.63: 1\n", "153.92: 1\n", "225.77: 1\n", "278.45: 1\n", "43.73: 1\n", "196.54: 1\n", "50.1: 1\n", "450.3: 1\n", "695.52: 1\n", "88.78: 1\n", "305.43: 1\n", "45.39: 1\n", "262.22: 1\n", "184.25: 1\n", "448.28: 1\n", "108.87: 1\n", "110.84: 1\n", "358.56: 1\n", "71.21: 1\n", "77.15: 2\n", "131.68: 1\n", "141.97: 1\n", "315.91: 1\n", "136.29: 1\n", "355.53: 1\n", "74.35: 1\n", "65.89: 1\n", "72.13: 1\n", "41.12: 1\n", "275.27: 1\n", "390.46: 1\n", "47.47: 1\n", "239.25: 1\n", "43.99: 1\n", "193.84: 1\n", "80.29: 1\n", "511.98: 1\n", "238.52: 1\n", "435.64: 1\n", "42.32: 1\n", "602.19: 1\n", "52.24: 1\n", "334.11: 1\n", "134.0: 1\n", "128.34: 1\n", "631.19: 1\n", "341.59: 1\n", "437.83: 1\n", "274.21: 1\n", "71.5: 1\n", "718.12: 1\n", "254.42: 1\n", "182.26: 1\n", "41.56: 1\n", "32.25: 1\n", "186.9: 1\n", "115.69: 1\n", "291.16: 1\n", "33.84: 1\n", "565.31: 1\n", "133.11: 1\n", "63.13: 1\n", "450.43: 1\n", "298.68: 1\n", "286.12: 1\n", "392.46: 1\n", "645.63: 1\n", "943.94: 1\n", "45.67: 1\n", "246.74: 1\n", "438.07: 1\n", "373.78: 1\n", "60.78: 1\n", "501.25: 1\n", "394.78: 1\n", "378.62: 1\n", "145.64: 1\n", "60.97: 1\n", "232.34: 1\n", "140.52: 2\n", "219.56: 1\n", "89.1: 1\n", "46.66: 1\n", "401.44: 1\n", "112.86: 1\n", "147.71: 1\n", "394.56: 1\n", "332.87: 1\n", "306.6: 1\n", "57.77: 1\n", "529.74: 1\n", "345.04: 1\n", "446.73: 1\n", "106.63: 1\n", "106.73: 1\n", "45.08: 1\n", "461.7: 1\n", "269.01: 1\n", "410.05: 1\n", "99.32: 2\n", "465.06: 1\n", "274.51: 1\n", "423.24: 1\n", "183.3: 1\n", "41.9: 1\n", "172.81: 1\n", "600.47: 1\n", "39.62: 1\n", "241.26: 1\n", "113.98: 1\n", "211.26: 1\n", "139.83: 1\n", "755.76: 1\n", "63.19: 1\n", "297.38: 1\n", "355.76: 1\n", "102.58: 1\n", "84.77: 1\n", "37.42: 2\n", "194.69: 1\n", "48.02: 1\n", "210.14: 1\n", "89.7: 1\n", "151.86: 1\n", "234.95: 1\n", "178.7: 1\n", "121.7: 1\n", "165.62: 1\n", "143.63: 1\n", "410.24: 1\n", "56.26: 1\n", "430.45: 1\n", "163.51: 1\n", "130.25: 1\n", "253.57: 1\n", "325.07: 1\n", "94.58: 1\n", "149.18: 1\n", "343.54: 1\n", "195.91: 1\n", "43.92: 1\n", "240.59: 1\n", "239.44: 2\n", "110.55: 1\n", "38.39: 1\n", "534.51: 1\n", "34.43: 1\n", "327.72: 1\n", "81.01: 1\n", "237.68: 1\n", "167.29: 1\n", "115.42: 1\n", "372.35: 1\n", "102.66: 1\n", "233.98: 1\n", "416.89: 1\n", "60.94: 1\n", "277.64: 1\n", "575.58: 1\n", "147.9: 1\n", "147.94: 1\n", "893.05: 1\n", "103.9: 1\n", "255.44: 1\n", "229.2: 1\n", "124.62: 1\n", "53.97: 1\n", "303.9: 1\n", "131.57: 1\n", "356.18: 1\n", "44.38: 1\n", "111.52: 1\n", "807.96: 1\n", "168.75: 1\n", "108.35: 2\n", "135.43: 1\n", "55.8: 1\n", "194.99: 1\n", "69.17: 1\n", "70.2: 1\n", "549.44: 1\n", "212.92: 1\n", "66.65: 1\n", "437.53: 1\n", "642.11: 1\n", "64.06: 2\n", "102.83: 1\n", "51.6: 1\n", "819.45: 1\n", "239.7: 1\n", "488.58: 1\n", "145.39: 1\n", "297.08: 1\n", "278.24: 1\n", "201.92: 1\n", "79.54: 1\n", "242.35: 1\n", "168.04: 1\n", "160.45: 1\n", "297.7: 1\n", "395.91: 1\n", "117.12: 1\n", "189.3: 1\n", "81.05: 1\n", "117.52: 1\n", "218.14: 1\n", "370.66: 1\n", "369.43: 1\n", "423.57: 1\n", "137.16: 1\n", "360.28: 1\n", "60.85: 1\n", "304.1: 1\n", "45.38: 1\n", "440.33: 1\n", "196.95: 2\n", "243.15: 1\n", "258.8: 1\n", "187.47: 1\n", "237.89: 1\n", "137.05: 1\n", "505.05: 1\n", "87.26: 1\n", "41.25: 1\n", "215.1: 1\n", "348.22: 1\n", "215.72: 1\n", "150.46: 1\n", "197.64: 1\n", "517.74: 1\n", "229.54: 1\n", "257.56: 1\n", "260.2: 1\n", "191.35: 1\n", "390.34: 1\n", "272.13: 1\n", "88.55: 1\n", "72.22: 1\n", "160.97: 1\n", "288.01: 1\n", "211.51: 1\n", "344.69: 1\n", "737.65: 1\n", "287.13: 1\n", "742.51: 1\n", "502.72: 1\n", "188.99: 1\n", "36.87: 1\n", "282.13: 1\n", "194.92: 2\n", "112.51: 1\n", "302.03: 1\n", "550.65: 2\n", "313.41: 1\n", "57.88: 1\n", "304.61: 1\n", "361.28: 1\n", "230.73: 1\n", "417.95: 1\n", "157.54: 1\n", "487.15: 1\n", "117.87: 1\n", "136.27: 1\n", "469.34: 1\n", "382.91: 1\n", "123.2: 1\n", "19.6: 1\n", "124.03: 1\n", "151.4: 1\n", "197.23: 1\n", "85.53: 1\n", "205.22: 1\n", "32.12: 1\n", "53.99: 1\n", "334.89: 1\n", "75.76: 2\n", "198.45: 1\n", "550.83: 1\n", "102.63: 1\n", "46.1: 1\n", "37.05: 1\n", "316.52: 1\n", "54.42: 1\n", "31.65: 1\n", "34.71: 2\n", "110.28: 2\n", "407.37: 1\n", "102.74: 2\n", "658.18: 1\n", "190.4: 1\n", "167.47: 1\n", "454.3: 1\n", "121.47: 1\n", "82.16: 1\n", "82.47: 1\n", "106.87: 1\n", "199.16: 1\n", "136.74: 1\n", "366.12: 1\n", "352.58: 2\n", "279.36: 1\n", "46.58: 1\n", "640.32: 1\n", "188.76: 1\n", "326.75: 1\n", "182.23: 1\n", "396.27: 1\n", "392.1: 2\n", "280.77: 1\n", "216.14: 1\n", "550.31: 1\n", "170.06: 1\n", "358.31: 1\n", "303.12: 1\n", "43.95: 1\n", "229.75: 1\n", "57.16: 2\n", "87.66: 1\n", "392.23: 1\n", "191.02: 1\n", "183.86: 1\n", "516.22: 1\n", "200.61: 1\n", "114.31: 1\n", "257.71: 1\n", "238.57: 1\n", "44.24: 1\n", "216.18: 1\n", "285.6: 1\n", "53.61: 1\n", "87.68: 1\n", "349.2: 1\n", "241.24: 1\n", "358.51: 1\n", "74.41: 2\n", "97.07: 1\n", "531.41: 1\n", "214.7: 1\n", "43.23: 1\n", "327.03: 1\n", "172.75: 1\n", "38.41: 1\n", "256.49: 1\n", "218.77: 1\n", "369.14: 2\n", "211.48: 1\n", "114.88: 2\n", "315.31: 1\n", "343.05: 1\n", "109.8: 1\n", "171.29: 1\n", "404.57: 1\n", "57.18: 1\n", "421.03: 1\n", "448.89: 1\n", "294.61: 1\n", "158.41: 1\n", "83.27: 2\n", "70.13: 1\n", "150.38: 1\n", "360.9: 1\n", "115.03: 1\n", "255.97: 1\n", "34.44: 1\n", "56.77: 1\n", "267.98: 1\n", "206.6: 1\n", "119.06: 1\n", "440.49: 1\n", "286.51: 1\n", "85.12: 1\n", "41.48: 1\n", "369.75: 1\n", "397.3: 1\n", "54.78: 1\n", "475.31: 1\n", "23.17: 1\n", "549.68: 1\n", "116.24: 1\n", "153.71: 1\n", "63.6: 1\n", "391.04: 1\n", "37.26: 2\n", "272.51: 1\n", "409.43: 1\n", "146.09: 1\n", "265.25: 1\n", "168.91: 1\n", "114.89: 1\n", "51.8: 1\n", "327.31: 1\n", "198.1: 1\n", "74.79: 1\n", "296.48: 1\n", "226.84: 1\n", "43.63: 1\n", "87.83: 1\n", "426.63: 1\n", "156.97: 1\n", "29.91: 2\n", "279.71: 1\n", "147.7: 1\n", "196.94: 1\n", "75.23: 1\n", "97.34: 1\n", "195.98: 1\n", "51.63: 1\n", "128.69: 1\n", "456.07: 1\n", "276.48: 1\n", "106.07: 1\n", "202.41: 1\n", "157.43: 1\n", "404.58: 1\n", "24.87: 1\n", "205.25: 1\n", "115.14: 1\n", "32.21: 1\n", "697.35: 1\n", "268.87: 1\n", "456.93: 1\n", "79.45: 1\n", "218.44: 1\n", "304.41: 1\n", "436.7: 1\n", "231.08: 1\n", "259.9: 1\n", "233.78: 1\n", "129.71: 1\n", "112.29: 1\n", "248.06: 1\n", "135.17: 1\n", "207.49: 1\n", "77.49: 1\n", "81.56: 1\n", "70.19: 1\n", "165.38: 1\n", "315.08: 1\n", "180.96: 1\n", "444.64: 1\n", "487.4: 1\n", "57.52: 1\n", "601.62: 1\n", "177.23: 1\n", "125.0: 1\n", "140.75: 1\n", "98.97: 1\n", "332.32: 1\n", "84.89: 1\n", "79.63: 1\n", "145.75: 1\n", "163.26: 1\n", "302.85: 1\n", "401.36: 1\n", "38.93: 1\n", "385.71: 1\n", "139.05: 1\n", "19.5: 1\n", "67.89: 1\n", "51.97: 1\n", "80.59: 1\n", "135.38: 1\n", "254.75: 1\n", "194.57: 1\n", "159.0: 1\n", "107.65: 1\n", "109.39: 1\n", "61.48: 1\n", "51.99: 1\n", "333.26: 1\n", "381.23: 1\n", "149.57: 1\n", "94.04: 1\n", "209.71: 1\n", "162.13: 1\n", "36.96: 1\n", "158.13: 1\n", "58.71: 1\n", "436.26: 1\n", "261.46: 1\n", "36.47: 1\n", "178.62: 1\n", "49.14: 1\n", "109.74: 1\n", "773.46: 1\n", "198.22: 1\n", "21.0: 1\n", "549.03: 1\n", "124.42: 1\n", "65.78: 2\n", "170.18: 1\n", "464.56: 1\n", "461.11: 1\n", "438.37: 1\n", "65.03: 1\n", "50.11: 1\n", "139.91: 2\n", "276.62: 1\n", "35.93: 1\n", "102.57: 1\n", "84.07: 1\n", "222.44: 1\n", "143.46: 1\n", "84.42: 1\n", "152.13: 1\n", "509.37: 1\n", "578.8: 1\n", "336.08: 1\n", "53.63: 1\n", "281.53: 1\n", "262.52: 1\n", "193.57: 1\n", "266.66: 1\n", "490.57: 1\n", "354.04: 1\n", "24.74: 1\n", "277.71: 1\n", "17.94: 1\n", "228.96: 1\n", "336.56: 1\n", "432.81: 1\n", "299.12: 1\n", "384.83: 1\n", "253.83: 1\n", "157.22: 1\n", "754.03: 1\n", "163.25: 1\n", "327.55: 1\n", "84.11: 1\n", "423.93: 1\n", "100.14: 1\n", "64.83: 1\n", "15.82: 1\n", "197.83: 1\n", "49.65: 1\n", "222.26: 1\n", "212.56: 1\n", "631.68: 1\n", "43.03: 1\n", "303.28: 1\n", "279.79: 1\n", "57.74: 1\n", "565.83: 1\n", "494.35: 1\n", "191.76: 1\n", "181.14: 1\n", "301.89: 1\n", "312.5: 1\n", "283.03: 1\n", "461.82: 1\n", "153.07: 1\n", "99.91: 1\n", "388.91: 1\n", "99.45: 1\n", "168.3: 1\n", "307.41: 1\n", "147.23: 1\n", "449.59: 1\n", "305.24: 1\n", "233.58: 1\n", "468.13: 1\n", "339.13: 1\n", "143.03: 1\n", "231.03: 1\n", "193.41: 1\n", "130.71: 1\n", "164.61: 1\n", "66.67: 1\n", "194.13: 1\n", "141.24: 1\n", "297.92: 1\n", "479.81: 1\n", "200.72: 1\n", "218.33: 1\n", "89.67: 1\n", "57.75: 1\n", "47.93: 1\n", "336.02: 1\n", "127.15: 1\n", "117.95: 1\n", "76.76: 1\n", "256.36: 1\n", "171.67: 1\n", "17.71: 1\n", "327.53: 1\n", "35.63: 1\n", "196.6: 1\n", "274.24: 1\n", "383.32: 1\n", "162.41: 1\n", "441.28: 1\n", "443.63: 1\n", "68.89: 1\n", "145.66: 1\n", "161.17: 1\n", "61.41: 1\n", "536.81: 1\n", "120.69: 1\n", "157.8: 1\n", "183.49: 1\n", "259.82: 1\n", "591.73: 1\n", "224.96: 2\n", "59.0: 1\n", "386.03: 1\n", "160.82: 1\n", "44.99: 2\n", "42.76: 1\n", "257.59: 1\n", "273.21: 1\n", "186.08: 1\n", "142.35: 1\n", "84.29: 1\n", "66.69: 2\n", "253.55: 1\n", "41.44: 1\n", "243.19: 1\n", "362.2: 1\n", "255.41: 1\n", "484.9: 1\n", "130.58: 1\n", "71.61: 1\n", "43.88: 1\n", "171.15: 1\n", "16.44: 1\n", "19.51: 1\n", "75.48: 1\n", "209.73: 1\n", "17.76: 1\n", "98.7: 1\n", "36.59: 1\n", "506.71: 1\n", "173.85: 1\n", "196.79: 1\n", "206.59: 1\n", "291.27: 1\n", "98.57: 1\n", "170.48: 1\n", "212.24: 1\n", "434.47: 1\n", "10.68: 1\n", "98.12: 1\n", "80.12: 1\n", "196.98: 1\n", "71.13: 1\n", "108.07: 1\n", "86.88: 1\n", "265.72: 1\n", "439.47: 1\n", "6.25: 1\n", "59.95: 1\n", "97.41: 1\n", "200.73: 1\n", "325.06: 1\n", "312.21: 1\n", "113.9: 1\n", "227.32: 1\n", "159.57: 1\n", "56.74: 1\n", "154.61: 1\n", "49.39: 1\n", "259.29: 1\n", "548.16: 1\n", "206.2: 1\n", "332.65: 1\n", "276.68: 1\n", "479.11: 1\n", "47.02: 1\n", "74.74: 1\n", "279.46: 1\n", "29.31: 1\n", "43.33: 1\n", "204.34: 1\n", "67.96: 1\n", "153.73: 1\n", "303.66: 1\n", "129.24: 1\n", "191.7: 1\n", "64.95: 1\n", "434.4: 1\n", "195.64: 1\n", "121.73: 1\n", "690.38: 1\n", "238.31: 1\n", "140.67: 1\n", "37.52: 1\n", "221.71: 1\n", "225.6: 1\n", "219.74: 1\n", "451.11: 1\n", "217.44: 1\n", "39.52: 1\n", "93.7: 1\n", "46.63: 1\n", "222.67: 1\n", "86.67: 1\n", "256.89: 2\n", "61.85: 1\n", "242.77: 1\n", "104.0: 1\n", "218.23: 1\n", "286.01: 1\n", "57.66: 1\n", "157.31: 1\n", "459.79: 1\n", "286.76: 1\n", "36.81: 1\n", "307.66: 1\n", "360.82: 1\n", "71.16: 1\n", "303.4: 1\n", "140.33: 1\n", "322.45: 1\n", "385.07: 1\n", "298.45: 1\n", "93.97: 1\n", "122.01: 1\n", "333.14: 1\n", "128.24: 1\n", "139.21: 1\n", "74.49: 1\n", "180.95: 1\n", "226.03: 1\n", "61.23: 1\n", "319.13: 1\n", "218.59: 1\n", "278.15: 1\n", "77.91: 1\n", "261.32: 1\n", "264.28: 1\n", "93.31: 1\n", "74.59: 1\n", "131.4: 1\n", "368.88: 1\n", "56.16: 1\n", "616.59: 1\n", "20.55: 1\n", "392.98: 1\n", "92.48: 1\n", "225.84: 1\n", "153.54: 1\n", "38.18: 1\n", "450.39: 1\n", "94.11: 1\n", "340.9: 1\n", "281.21: 1\n", "371.6: 1\n", "178.57: 1\n", "80.21: 1\n", "216.64: 1\n", "153.09: 1\n", "254.56: 1\n", "278.69: 1\n", "84.98: 1\n", "200.81: 1\n", "96.27: 1\n", "586.54: 1\n", "360.63: 1\n", "187.19: 1\n", "55.41: 1\n", "299.98: 1\n", "136.4: 1\n", "61.87: 1\n", "252.35: 1\n", "303.13: 1\n", "59.55: 1\n", "252.25: 1\n", "111.08: 1\n", "290.62: 1\n", "202.78: 1\n", "109.58: 1\n", "455.56: 1\n", "452.04: 1\n", "102.21: 1\n", "227.93: 1\n", "62.1: 1\n", "513.91: 1\n", "503.76: 1\n", "105.9: 1\n", "85.57: 1\n", "219.14: 1\n", "94.57: 1\n", "45.57: 1\n", "383.06: 1\n", "71.47: 1\n", "128.79: 1\n", "189.1: 1\n", "85.49: 1\n", "69.15: 1\n", "314.97: 1\n", "205.13: 1\n", "60.12: 1\n", "634.61: 1\n", "675.98: 1\n", "136.95: 1\n", "534.03: 1\n", "289.59: 1\n", "251.75: 1\n", "273.59: 1\n", "107.78: 1\n", "146.42: 1\n", "323.78: 1\n", "152.74: 1\n", "55.17: 1\n", "159.78: 1\n", "107.46: 1\n", "451.9: 1\n", "37.49: 1\n", "187.69: 1\n", "252.21: 2\n", "252.88: 1\n", "248.2: 1\n", "92.84: 1\n", "332.63: 1\n", "208.23: 1\n", "303.78: 1\n", "509.48: 1\n", "101.03: 1\n", "105.46: 1\n", "423.73: 1\n", "502.55: 1\n", "765.86: 1\n", "273.89: 1\n", "36.09: 1\n", "277.97: 1\n", "162.64: 1\n", "125.94: 2\n", "35.2: 1\n", "301.94: 1\n", "178.19: 1\n", "88.16: 1\n", "443.22: 1\n", "79.55: 1\n", "174.01: 1\n", "275.88: 1\n", "198.84: 1\n", "160.17: 1\n", "163.27: 1\n", "99.38: 1\n", "252.59: 1\n", "610.36: 1\n", "89.03: 2\n", "69.48: 1\n", "52.0: 1\n", "619.24: 1\n", "89.58: 1\n", "180.72: 1\n", "244.6: 1\n", "61.58: 1\n", "285.13: 1\n", "699.59: 1\n", "363.14: 1\n", "71.48: 1\n", "177.56: 1\n", "78.23: 1\n", "193.12: 1\n", "296.63: 1\n", "293.93: 1\n", "123.54: 1\n", "163.72: 1\n", "329.45: 1\n", "3.58: 1\n", "62.68: 1\n", "291.35: 1\n", "357.15: 1\n", "274.91: 1\n", "218.9: 1\n", "163.69: 1\n", "198.0: 1\n", "564.7: 1\n", "53.24: 1\n", "403.75: 1\n", "363.42: 1\n", "448.9: 1\n", "88.09: 1\n", "313.02: 1\n", "449.98: 1\n", "337.35: 1\n", "144.16: 1\n", "243.11: 1\n", "688.02: 1\n", "171.77: 1\n", "233.18: 1\n", "803.55: 1\n", "34.63: 1\n", "492.84: 1\n", "154.03: 1\n", "215.03: 1\n", "148.18: 2\n", "105.88: 1\n", "139.14: 1\n", "115.24: 2\n", "67.16: 1\n", "80.47: 1\n", "50.62: 1\n", "157.76: 1\n", "77.94: 1\n", "160.4: 1\n", "155.46: 1\n", "151.31: 1\n", "69.16: 1\n", "123.69: 1\n", "136.57: 1\n", "49.3: 1\n", "109.33: 1\n", "87.89: 2\n", "88.13: 1\n", "143.36: 1\n", "177.24: 1\n", "69.86: 1\n", "106.31: 1\n", "137.2: 1\n", "54.17: 1\n", "70.83: 1\n", "247.4: 1\n", "82.63: 1\n", "45.79: 1\n", "140.32: 1\n", "75.15: 1\n", "40.03: 1\n", "75.44: 1\n", "44.14: 1\n", "151.99: 1\n", "123.58: 1\n", "38.81: 1\n", "115.96: 1\n", "81.04: 1\n", "112.44: 1\n", "80.07: 1\n", "103.71: 1\n", "50.76: 1\n", "87.57: 1\n", "127.87: 1\n", "119.5: 1\n", "80.35: 1\n", "141.66: 1\n", "33.6: 1\n", "77.88: 1\n", "65.95: 1\n", "35.33: 1\n", "36.56: 1\n", "73.4: 1\n", "31.78: 1\n", "30.22: 1\n", "30.51: 1\n", "154.71: 2\n", "37.03: 1\n", "73.18: 1\n", "17.04: 1\n", "69.62: 1\n", "92.96: 1\n", "72.48: 1\n", "66.18: 1\n", "140.74: 1\n", "118.16: 1\n", "240.49: 2\n", "39.18: 1\n", "169.69: 1\n", "111.04: 1\n", "96.36: 1\n", "119.91: 1\n", "164.75: 1\n", "125.46: 2\n", "31.03: 2\n", "109.2: 1\n", "13.03: 1\n", "245.67: 1\n", "52.74: 1\n", "122.72: 1\n", "353.96: 1\n", "211.43: 1\n", "46.79: 1\n", "48.07: 1\n", "245.73: 1\n", "8.31: 1\n", "271.41: 1\n", "189.26: 1\n", "163.97: 1\n", "243.7: 1\n", "93.72: 1\n", "40.25: 1\n", "31.85: 1\n", "10.97: 1\n", "135.91: 1\n", "58.02: 1\n", "135.49: 1\n", "39.31: 1\n", "19.69: 1\n", "66.22: 1\n", "80.46: 1\n", "114.54: 1\n", "14.63: 1\n", "17.03: 1\n", "147.69: 1\n", "239.68: 1\n", "19.99: 1\n", "50.18: 1\n", "127.34: 1\n", "25.95: 1\n", "20.86: 1\n", "138.28: 1\n", "23.56: 1\n", "72.78: 1\n", "212.83: 1\n", "139.49: 1\n", "10.8: 1\n", "16.11: 1\n", "58.52: 1\n", "93.78: 1\n", "158.12: 1\n", "133.53: 1\n", "152.14: 1\n", "242.91: 1\n", "121.23: 1\n", "207.47: 1\n", "232.96: 1\n", "30.32: 1\n", "67.27: 1\n", "174.21: 1\n", "14.72: 1\n", "7.51: 1\n", "26.65: 1\n", "12.62: 1\n", "73.11: 1\n", "12.66: 1\n", "245.96: 1\n", "123.59: 1\n", "16.99: 1\n", "69.93: 1\n", "6.57: 1\n", "127.24: 1\n", "43.43: 1\n", "97.18: 1\n", "9.46: 1\n", "153.43: 1\n", "314.53: 1\n", "22.78: 1\n", "23.92: 1\n", "130.39: 1\n", "17.41: 1\n", "12.22: 1\n", "72.16: 1\n", "175.97: 1\n", "59.52: 1\n", "184.0: 1\n", "61.8: 1\n", "71.34: 1\n", "8.87: 1\n", "72.44: 1\n", "87.42: 1\n", "382.34: 1\n", "270.66: 1\n", "44.67: 1\n", "38.87: 1\n", "115.17: 1\n", "9.39: 1\n", "102.55: 2\n", "289.85: 1\n", "143.28: 1\n", "75.9: 1\n", "93.94: 1\n", "287.6: 1\n", "156.01: 1\n", "1.47: 1\n", "79.11: 1\n", "159.34: 1\n", "97.61: 1\n", "5.48: 1\n", "13.15: 1\n", "187.62: 1\n", "230.61: 1\n", "123.8: 1\n", "126.0: 1\n", "194.41: 1\n", "151.34: 1\n", "37.9: 1\n", "119.49: 1\n", "11.48: 1\n", "344.24: 1\n", "58.94: 1\n", "179.28: 1\n", "225.48: 1\n", "46.83: 1\n", "128.46: 1\n", "201.35: 1\n", "173.02: 1\n", "186.16: 1\n", "4.55: 1\n", "11.83: 1\n", "6.69: 1\n", "189.84: 1\n", "86.0: 1\n", "287.38: 1\n", "136.76: 1\n", "101.55: 1\n", "195.31: 1\n", "244.64: 1\n", "46.48: 1\n", "14.88: 1\n", "54.58: 1\n", "153.32: 1\n", "92.14: 1\n", "81.83: 1\n", "5.68: 1\n", "96.58: 1\n", "136.11: 1\n", "95.93: 1\n", "294.7: 1\n", "11.82: 1\n", "237.64: 1\n", "261.91: 1\n", "99.73: 1\n", "183.85: 1\n", "93.69: 1\n", "130.21: 1\n", "164.79: 1\n", "285.0: 1\n", "262.33: 1\n", "357.45: 1\n", "169.38: 1\n", "106.34: 1\n", "147.41: 1\n", "99.95: 1\n", "156.56: 1\n", "90.49: 1\n", "168.85: 1\n", "206.77: 1\n", "228.23: 1\n", "198.95: 1\n", "137.64: 1\n", "75.25: 1\n", "95.37: 1\n", "275.38: 1\n", "171.63: 1\n", "205.98: 1\n", "212.1: 1\n", "251.49: 1\n", "105.99: 1\n", "65.01: 1\n", "177.49: 1\n", "173.76: 1\n", "79.61: 1\n", "104.07: 1\n", "192.49: 1\n", "97.85: 1\n", "43.44: 1\n", "172.91: 1\n", "98.02: 1\n", "114.12: 1\n", "109.68: 1\n", "70.82: 1\n", "219.05: 1\n", "330.81: 1\n", "103.06: 1\n", "158.69: 1\n", "87.12: 1\n", "144.35: 1\n", "47.57: 1\n", "211.33: 1\n", "133.22: 1\n", "90.8: 1\n", "183.46: 1\n", "55.2: 1\n", "63.7: 1\n", "76.51: 1\n", "51.29: 1\n", "314.89: 1\n", "105.71: 1\n", "74.19: 1\n", "266.26: 1\n", "75.7: 1\n", "182.48: 1\n", "41.11: 1\n", "187.54: 1\n", "58.68: 1\n", "125.8: 1\n", "228.81: 1\n", "130.5: 1\n", "109.7: 1\n", "136.43: 1\n", "300.24: 1\n", "195.7: 1\n", "83.3: 1\n", "311.72: 1\n", "34.76: 1\n", "49.34: 1\n", "170.31: 1\n", "76.62: 1\n", "163.8: 1\n", "230.98: 1\n", "276.4: 1\n", "343.7: 1\n", "324.04: 1\n", "133.5: 1\n", "146.75: 1\n", "160.11: 1\n", "67.5: 1\n", "227.99: 1\n", "165.69: 1\n", "97.36: 1\n", "59.85: 1\n", "95.61: 1\n", "140.78: 1\n", "164.72: 1\n", "65.77: 1\n", "75.13: 1\n", "28.8: 1\n", "20.6: 1\n", "84.24: 1\n", "266.46: 1\n", "105.21: 1\n", "253.39: 1\n", "219.59: 1\n", "87.39: 1\n", "214.83: 1\n", "55.63: 1\n", "76.38: 1\n", "91.54: 1\n", "96.4: 1\n", "252.38: 1\n", "95.67: 1\n", "137.47: 1\n", "35.11: 1\n", "259.11: 1\n", "147.3: 1\n", "100.95: 1\n", "168.26: 1\n", "73.58: 1\n", "132.67: 1\n", "58.28: 1\n", "106.78: 1\n", "68.16: 1\n", "180.53: 1\n", "110.89: 1\n", "103.43: 1\n", "120.38: 1\n", "80.67: 1\n", "199.1: 1\n", "104.66: 1\n", "211.65: 1\n", "119.73: 1\n", "88.87: 2\n", "22.4: 1\n", "145.99: 1\n", "160.04: 1\n", "140.96: 1\n", "50.24: 1\n", "154.33: 1\n", "188.94: 1\n", "38.32: 1\n", "163.94: 1\n", "260.12: 1\n", "93.89: 1\n", "99.44: 1\n", "57.0: 1\n", "274.33: 1\n", "34.31: 1\n", "114.62: 1\n", "53.93: 1\n", "96.7: 1\n", "53.57: 1\n", "66.66: 1\n", "113.87: 1\n", "211.4: 1\n", "113.0: 1\n", "301.55: 1\n", "343.17: 1\n", "79.91: 1\n", "88.71: 1\n", "83.05: 1\n", "83.24: 1\n", "150.66: 1\n", "190.48: 1\n", "191.04: 1\n", "297.02: 1\n", "115.6: 1\n", "139.44: 1\n", "62.57: 1\n", "77.19: 1\n", "71.15: 1\n", "63.05: 1\n", "33.99: 1\n", "108.03: 1\n", "204.12: 1\n", "162.92: 1\n", "125.58: 1\n", "123.89: 1\n", "53.13: 1\n", "132.4: 1\n", "25.06: 1\n", "76.0: 1\n", "282.17: 1\n", "77.05: 1\n", "238.44: 1\n", "136.75: 1\n", "131.89: 1\n", "76.8: 1\n", "147.97: 1\n", "74.82: 1\n", "316.73: 1\n", "448.14: 2\n", "231.73: 1\n", "304.48: 1\n", "323.6: 1\n", "444.75: 1\n", "513.5: 1\n", "487.56: 1\n", "279.33: 1\n", "193.29: 1\n", "478.33: 1\n", "173.32: 1\n", "150.63: 1\n", "236.33: 1\n", "303.08: 1\n", "114.76: 1\n", "283.54: 1\n", "76.19: 1\n", "243.73: 1\n", "62.77: 1\n", "290.22: 1\n", "214.14: 1\n", "84.7: 1\n", "23.59: 1\n", "334.85: 1\n", "170.1: 1\n", "178.12: 1\n", "324.57: 1\n", "316.69: 1\n", "75.58: 1\n", "81.55: 1\n", "230.64: 1\n", "317.01: 1\n", "180.77: 1\n", "282.11: 1\n", "38.76: 1\n", "161.4: 1\n", "329.19: 1\n", "390.39: 1\n", "461.34: 1\n", "370.45: 1\n", "492.72: 1\n", "311.71: 1\n", "218.35: 1\n", "143.07: 1\n", "435.83: 1\n", "48.03: 1\n", "38.58: 1\n", "34.91: 1\n", "287.83: 1\n", "82.82: 1\n", "331.33: 1\n", "146.62: 1\n", "189.27: 1\n", "76.79: 1\n", "263.77: 1\n", "208.17: 1\n", "172.85: 1\n", "131.76: 1\n", "279.84: 1\n", "10.54: 1\n", "287.56: 1\n", "377.66: 1\n", "389.02: 1\n", "22.17: 1\n", "90.39: 1\n", "63.83: 1\n", "307.69: 1\n", "139.75: 1\n", "62.01: 1\n", "53.77: 1\n", "185.15: 1\n", "288.07: 1\n", "435.67: 1\n", "64.74: 1\n", "629.82: 1\n", "573.74: 1\n", "347.93: 1\n", "298.92: 1\n", "286.42: 1\n", "109.03: 1\n", "218.18: 1\n", "181.4: 1\n", "144.11: 1\n", "474.84: 1\n", "148.9: 1\n", "169.98: 1\n", "61.7: 1\n", "354.72: 1\n", "539.63: 1\n", "92.31: 1\n", "507.88: 1\n", "79.01: 1\n", "50.15: 1\n", "245.41: 1\n", "226.73: 1\n", "340.99: 1\n", "353.82: 1\n", "209.36: 1\n", "235.94: 1\n", "84.62: 1\n", "324.89: 1\n", "367.58: 1\n", "18.59: 1\n", "44.22: 1\n", "451.19: 1\n", "253.22: 1\n", "45.75: 1\n", "194.85: 1\n", "277.15: 1\n", "42.69: 1\n", "277.21: 1\n", "106.47: 1\n", "288.43: 1\n", "169.72: 1\n", "93.1: 1\n", "140.82: 1\n", "271.94: 1\n", "253.9: 1\n", "256.6: 1\n", "477.25: 1\n", "179.07: 1\n", "134.98: 1\n", "778.49: 1\n", "221.15: 1\n", "104.54: 1\n", "489.72: 1\n", "474.78: 1\n", "183.98: 1\n", "131.95: 1\n", "228.54: 1\n", "305.91: 1\n", "308.21: 1\n", "38.27: 1\n", "206.02: 1\n", "317.23: 1\n", "273.11: 1\n", "276.37: 1\n", "52.79: 2\n", "328.02: 1\n", "78.19: 1\n", "125.93: 1\n", "343.48: 1\n", "129.83: 1\n", "257.54: 1\n", "217.88: 1\n", "112.96: 1\n", "91.3: 1\n", "361.93: 1\n", "92.59: 1\n", "833.67: 1\n", "148.83: 1\n", "178.93: 1\n", "151.52: 1\n", "144.03: 1\n", "74.24: 1\n", "384.08: 1\n", "527.94: 1\n", "68.91: 1\n", "178.3: 1\n", "39.86: 1\n", "175.67: 1\n", "217.79: 1\n", "117.34: 1\n", "225.53: 1\n", "132.69: 1\n", "470.66: 1\n", "49.63: 1\n", "174.95: 1\n", "251.11: 1\n", "281.49: 1\n", "143.57: 1\n", "219.23: 1\n", "37.83: 1\n", "296.12: 1\n", "122.51: 1\n", "220.61: 1\n", "60.02: 1\n", "135.77: 1\n", "143.31: 1\n", "153.16: 1\n", "117.25: 1\n", "314.35: 1\n", "278.37: 1\n", "84.68: 1\n", "364.3: 1\n", "263.5: 1\n", "137.8: 1\n", "23.13: 1\n", "154.8: 1\n", "55.7: 1\n", "132.59: 1\n", "518.8: 1\n", "108.22: 1\n", "250.69: 1\n", "48.32: 1\n", "26.87: 1\n", "106.19: 1\n", "205.2: 1\n", "270.4: 1\n", "263.58: 1\n", "141.38: 1\n", "180.18: 1\n", "203.8: 1\n", "194.09: 1\n", "113.5: 1\n", "145.81: 1\n", "389.48: 1\n", "153.64: 1\n", "146.85: 1\n", "163.39: 1\n", "116.73: 1\n", "412.05: 1\n", "218.64: 1\n", "569.58: 1\n", "227.21: 1\n", "113.59: 1\n", "181.84: 1\n", "615.49: 1\n", "307.83: 1\n", "166.47: 1\n", "185.54: 1\n", "549.17: 1\n", "491.5: 1\n", "388.99: 1\n", "28.49: 1\n", "318.07: 1\n", "170.5: 1\n", "150.61: 1\n", "157.98: 1\n", "368.42: 1\n", "171.87: 1\n", "235.87: 1\n", "363.86: 1\n", "359.29: 1\n", "131.14: 1\n", "158.45: 1\n", "257.42: 1\n", "248.01: 1\n", "333.33: 1\n", "316.08: 1\n", "102.19: 1\n", "485.47: 1\n", "34.17: 1\n", "556.3: 1\n", "35.25: 1\n", "390.87: 1\n", "117.44: 1\n", "134.35: 1\n", "71.66: 1\n", "134.1: 1\n", "11.2: 1\n", "88.07: 1\n", "94.96: 1\n", "592.32: 1\n", "59.41: 1\n", "768.81: 1\n", "6.84: 1\n", "277.56: 1\n", "28.13: 1\n", "154.65: 1\n", "337.36: 1\n", "205.26: 1\n", "347.78: 1\n", "501.0: 1\n", "240.66: 1\n", "173.43: 1\n", "825.57: 1\n", "222.63: 1\n", "171.97: 1\n", "243.13: 1\n", "168.79: 1\n", "286.41: 1\n", "168.98: 1\n", "216.13: 1\n", "71.35: 1\n", "133.66: 1\n", "770.21: 1\n", "86.29: 1\n", "639.13: 1\n", "62.66: 1\n", "110.27: 1\n", "424.61: 1\n", "93.96: 1\n", "294.21: 1\n", "45.71: 1\n", "408.52: 1\n", "253.26: 1\n", "109.76: 1\n", "359.18: 1\n", "108.1: 1\n", "42.07: 1\n", "344.71: 1\n", "251.18: 1\n", "65.39: 1\n", "70.76: 1\n", "335.59: 1\n", "109.91: 1\n", "210.59: 1\n", "295.13: 1\n", "+==================================================+\n", "Number of unique values: 27\n", "Unique values in 'hardship_start_date': [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Sep-2018' 'Jan-2019' 'Dec-2018'\n", " 'Aug-2018' 'Jul-2018' 'May-2018' 'Sep-2017' 'Feb-2018' 'Dec-2017'\n", " 'Apr-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Jun-2018' 'Oct-2017'\n", " 'Nov-2017' 'Jul-2017' 'Jun-2017' 'May-2017' 'Feb-2017' 'Apr-2017'\n", " 'Jan-2017' 'Mar-2017']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Feb-2019: 297\n", "Oct-2018: 594\n", "Nov-2018: 420\n", "Sep-2018: 422\n", "Jan-2019: 431\n", "Dec-2018: 339\n", "Aug-2018: 463\n", "Jul-2018: 359\n", "May-2018: 216\n", "Sep-2017: 2444\n", "Feb-2018: 242\n", "Dec-2017: 359\n", "Apr-2018: 205\n", "Aug-2017: 360\n", "Jan-2018: 348\n", "Mar-2018: 238\n", "Jun-2018: 260\n", "Oct-2017: 1077\n", "Nov-2017: 466\n", "Jul-2017: 271\n", "Jun-2017: 400\n", "May-2017: 373\n", "Feb-2017: 11\n", "Apr-2017: 9\n", "Jan-2017: 3\n", "Mar-2017: 6\n", "+==================================================+\n", "Number of unique values: 28\n", "Unique values in 'hardship_end_date': [nan 'Apr-2019' 'Dec-2018' 'Jan-2019' 'Feb-2019' 'Oct-2018' 'May-2019'\n", " 'Mar-2019' 'Nov-2018' 'Aug-2018' 'Dec-2017' 'May-2018' 'Nov-2017'\n", " 'Mar-2018' 'Jul-2018' 'Apr-2018' 'Jun-2018' 'Sep-2017' 'Sep-2018'\n", " 'Oct-2017' 'Jan-2018' 'Feb-2018' 'Aug-2017' 'Jul-2017' 'Jun-2017'\n", " 'May-2017' 'Apr-2017' 'Mar-2017']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Apr-2019: 367\n", "Dec-2018: 509\n", "Jan-2019: 518\n", "Feb-2019: 471\n", "Oct-2018: 397\n", "May-2019: 101\n", "Mar-2019: 315\n", "Nov-2018: 413\n", "Aug-2018: 266\n", "Dec-2017: 1756\n", "May-2018: 253\n", "Nov-2017: 1325\n", "Mar-2018: 356\n", "Jul-2018: 212\n", "Apr-2018: 295\n", "Jun-2018: 210\n", "Sep-2017: 368\n", "Sep-2018: 299\n", "Oct-2017: 396\n", "Jan-2018: 749\n", "Feb-2018: 401\n", "Aug-2017: 386\n", "Jul-2017: 174\n", "Jun-2017: 50\n", "May-2017: 16\n", "Apr-2017: 6\n", "Mar-2017: 4\n", "+==================================================+\n", "Number of unique values: 27\n", "Unique values in 'payment_plan_start_date': [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Dec-2018' 'Jan-2019' 'Sep-2018'\n", " 'Mar-2019' 'Aug-2018' 'Jun-2018' 'Sep-2017' 'Oct-2017' 'Feb-2018'\n", " 'Dec-2017' 'May-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Apr-2018'\n", " 'Nov-2017' 'Jul-2018' 'Jul-2017' 'Jun-2017' 'May-2017' 'Mar-2017'\n", " 'Feb-2017' 'Apr-2017']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Feb-2019: 387\n", "Oct-2018: 538\n", "Nov-2018: 481\n", "Dec-2018: 379\n", "Jan-2019: 368\n", "Sep-2018: 416\n", "Mar-2019: 102\n", "Aug-2018: 456\n", "Jun-2018: 231\n", "Sep-2017: 1715\n", "Oct-2017: 1629\n", "Feb-2018: 307\n", "Dec-2017: 413\n", "May-2018: 216\n", "Aug-2017: 294\n", "Jan-2018: 329\n", "Mar-2018: 218\n", "Apr-2018: 218\n", "Nov-2017: 640\n", "Jul-2018: 297\n", "Jul-2017: 343\n", "Jun-2017: 394\n", "May-2017: 217\n", "Mar-2017: 11\n", "Feb-2017: 9\n", "Apr-2017: 5\n", "+==================================================+\n", "Number of unique values: 2\n", "Unique values in 'hardship_length': [nan  3.]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "3.0: 10613\n", "+==================================================+\n", "Number of unique values: 35\n", "Unique values in 'hardship_dpd': [nan 22.  0. 18. 23. 28. 29.  7. 16. 11.  6. 13.  2. 14. 10. 19.  9.  5.\n", " 21. 26. 24. 12.  3. 17.  4.  8. 20. 27. 15.  1. 25. 32. 30. 37. 31.]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "22.0: 357\n", "0.0: 2402\n", "18.0: 337\n", "23.0: 410\n", "28.0: 349\n", "29.0: 268\n", "7.0: 312\n", "16.0: 372\n", "11.0: 359\n", "6.0: 152\n", "13.0: 328\n", "2.0: 49\n", "14.0: 273\n", "10.0: 293\n", "19.0: 335\n", "9.0: 269\n", "5.0: 117\n", "21.0: 350\n", "26.0: 409\n", "24.0: 330\n", "12.0: 306\n", "3.0: 61\n", "17.0: 359\n", "4.0: 78\n", "8.0: 229\n", "20.0: 380\n", "27.0: 361\n", "15.0: 284\n", "1.0: 43\n", "25.0: 405\n", "32.0: 4\n", "30.0: 29\n", "37.0: 1\n", "31.0: 2\n", "+==================================================+\n", "Number of unique values: 6\n", "Unique values in 'hardship_loan_status': [nan 'Late (16-30 days)' 'Issued' 'Current' 'Late (31-120 days)'\n", " 'In Grace Period']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Late (16-30 days): 4622\n", "Issued: 15\n", "Current: 2737\n", "Late (31-120 days): 433\n", "In Grace Period: 2806\n", "+==================================================+\n"]}], "source": ["# print(df[\"hardship_start_date\"].unique())\n", "# print(df[\"hardship_end_date\"].unique())\n", "\n", "columns = [\"hardship_flag\", \"hardship_type\", \"hardship_reason\", \"hardship_status\", \"deferral_term\", \"hardship_amount\", \"hardship_start_date\", \"hardship_end_date\", \"payment_plan_start_date\", \"hardship_length\", \"hardship_dpd\", \"hardship_loan_status\"]\n", "\n", "for col in columns:\n", "    unique_values = df[col].unique()\n", "    print(f\"Number of unique values: {len(unique_values)}\") \n", "    print(f\"Unique values in '{col}': {unique_values}\")\n", "    print('-'*50)\n", "    print(f\"Count of each unique value:\")\n", "    for unique_value in unique_values:\n", "        count = (df[col] == unique_value).sum()\n", "        print(f\"{unique_value}: {count}\")\n", "\n", "    print(\"+\" + \"=\"*50 + \"+\")"]}, {"cell_type": "code", "execution_count": 51, "id": "0ac01664", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Current', 'Fully Paid', 'Late (31-120 days)', 'In Grace Period',\n", "       'Charged Off', 'Late (16-30 days)', 'Default',\n", "       'Does not meet the credit policy. Status:Fully Paid',\n", "       'Does not meet the credit policy. Status:Charged Off'],\n", "      dtype=object)"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_status\"].unique()"]}, {"cell_type": "code", "execution_count": 48, "id": "24849959", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total columns: 145\n", "\n", "Column names at problematic indices:\n", "Column 123: hardship_type\n", "Column 124: hardship_reason\n", "Column 125: hardship_status\n", "Column 128: hardship_start_date\n", "Column 129: hardship_end_date\n", "Column 130: payment_plan_start_date\n", "Column 133: hardship_loan_status\n", "Column 139: debt_settlement_flag_date\n", "Column 140: settlement_status\n", "Column 141: settlement_date\n", "\n", "==================================================\n", "EXAMINING <PERSON>XED TYPE COLUMNS\n", "==================================================\n", "\n", "Sample data shape: (1000, 145)\n", "\n", "Column 123 - 'hardship_type':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 124 - 'hardship_reason':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 125 - 'hardship_status':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 128 - 'hardship_start_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 129 - 'hardship_end_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 130 - 'payment_plan_start_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 133 - 'hardship_loan_status':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 139 - 'debt_settlement_flag_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 140 - 'settlement_status':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 141 - 'settlement_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n"]}], "source": ["# Let's first examine the problematic columns mentioned in the warning\n", "# Columns (123,124,125,128,129,130,133,139,140,141) have mixed types\n", "\n", "# First, let's read just the header to see column names\n", "# header_df = pd.DataFrame(df.iloc[0, :])\n", "header_df = pd.read_csv(DATA_PATH, nrows=0)\n", "print(f\"Total columns: {len(header_df.columns)}\")\n", "print(\"\\nColumn names at problematic indices:\")\n", "problematic_indices = [123, 124, 125, 128, 129, 130, 133, 139, 140, 141]\n", "for idx in problematic_indices:\n", "    if idx < len(header_df.columns):\n", "        print(f\"Column {idx}: {header_df.columns[idx]}\")\n", "    else:\n", "        print(f\"Column {idx}: Index out of range\")\n", "\n", "# Now let's examine a small sample to see the mixed types\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"EXAMINING MIXED TYPE COLUMNS\")\n", "print(\"=\"*50)\n", "\n", "# Read a small sample to inspect the data types\n", "sample_df = pd.read_csv(DATA_PATH, nrows=1000, low_memory=False)\n", "print(f\"\\nSample data shape: {sample_df.shape}\")\n", "\n", "# Check the problematic columns\n", "for idx in problematic_indices:\n", "    if idx < len(sample_df.columns):\n", "        col_name = sample_df.columns[idx]\n", "        print(f\"\\nColumn {idx} - '{col_name}':\")\n", "        print(f\"  Data type: {sample_df[col_name].dtype}\")\n", "        print(f\"  Unique values (first 10): {sample_df[col_name].unique()[:10]}\")\n", "        print(f\"  Non-null count: {sample_df[col_name].count()}/{len(sample_df)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c4a560aa", "metadata": {}, "outputs": [], "source": ["features = [\n", "    'loan_amnt',\n", "    'int_rate',\n", "    'installment',\n", "    'grade',\n", "    'sub_grade',\n", "    'annual_inc',\n", "    'dti',\n", "    'fico_range_low',\n", "    'fico_range_high',\n", "    'revol_util',\n", "    'revol_bal',\n", "    'emp_length',\n", "    'home_ownership',\n", "    'verification_status',\n", "    'purpose',\n", "    'term',\n", "    'open_acc',\n", "    'total_acc',\n", "    'inq_last_6mths',\n", "    'delinq_2yrs',\n", "    'pub_rec',\n", "    'pub_rec_bankruptcies',\n", "    'mths_since_last_delinq',\n", "    'acc_open_past_24mths',\n", "    'mort_acc',\n", "    'avg_cur_bal',\n", "    'bc_util',\n", "    'tot_hi_cred_lim',\n", "    'mo_sin_old_rev_tl_op',\n", "    'num_actv_rev_tl'\n", "]"]}, {"cell_type": "code", "execution_count": 3, "id": "9409665d", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Current', 'Fully Paid', 'Late (31-120 days)', 'In Grace Period',\n", "       'Charged Off', 'Late (16-30 days)', 'Default',\n", "       'Does not meet the credit policy. Status:Fully Paid',\n", "       'Does not meet the credit policy. Status:Charged Off'],\n", "      dtype=object)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_status\"].unique()"]}, {"cell_type": "markdown", "id": "a9d95e69", "metadata": {}, "source": ["## structure with loan attributes (loan_data.py)"]}, {"cell_type": "markdown", "id": "be114b5e", "metadata": {}, "source": ["### LoanG<PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "04ce6094", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['C', 'D', 'B', 'A', 'E', 'F', 'G'], dtype=object)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"grade\"].unique()"]}, {"cell_type": "code", "execution_count": 14, "id": "a089f22c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'B': 663557,\n", " 'C': 650053,\n", " 'A': 433027,\n", " 'D': 324424,\n", " 'E': 135639,\n", " 'F': 41800,\n", " 'G': 12168}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"grade\"].value_counts())"]}, {"cell_type": "markdown", "id": "9fb4e03f", "metadata": {}, "source": ["### home Ownership"]}, {"cell_type": "code", "execution_count": null, "id": "dafa2252", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['RENT', '<PERSON>OR<PERSON><PERSON><PERSON>', 'OWN', 'ANY', 'NONE', 'OTHER'], dtype=object)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"home_ownership\"].unique()"]}, {"cell_type": "code", "execution_count": 15, "id": "0d857277", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'MORTGAGE': 1111450,\n", " 'RENT': 894929,\n", " 'OWN': 253057,\n", " 'ANY': 996,\n", " 'OTHER': 182,\n", " 'NONE': 54}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"home_ownership\"].value_counts())"]}, {"cell_type": "markdown", "id": "43c912cf", "metadata": {}, "source": ["### Verification Status"]}, {"cell_type": "code", "execution_count": null, "id": "1ef8d3b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Not Verified', 'Source Verified', 'Verified'], dtype=object)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"verification_status\"].unique()"]}, {"cell_type": "code", "execution_count": 21, "id": "ab0568d7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Source Verified': 886231, 'Not Verified': 744806, 'Verified': 629631}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"verification_status\"].value_counts())"]}, {"cell_type": "markdown", "id": "33ebe331", "metadata": {}, "source": ["### <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 23, "id": "72ce949c", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['debt_consolidation', 'credit_card', 'house', 'car', 'other',\n", "       'vacation', 'home_improvement', 'small_business', 'major_purchase',\n", "       'medical', 'renewable_energy', 'moving', 'wedding', 'educational'],\n", "      dtype=object)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"purpose\"].unique()"]}, {"cell_type": "code", "execution_count": 24, "id": "b426fd17", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'debt_consolidation': 1277877,\n", " 'credit_card': 516971,\n", " 'home_improvement': 150457,\n", " 'other': 139440,\n", " 'major_purchase': 50445,\n", " 'medical': 27488,\n", " 'small_business': 24689,\n", " 'car': 24013,\n", " 'vacation': 15525,\n", " 'moving': 15403,\n", " 'house': 14136,\n", " 'wedding': 2355,\n", " 'renewable_energy': 1445,\n", " 'educational': 424}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"purpose\"].value_counts())"]}, {"cell_type": "markdown", "id": "04b2803f", "metadata": {}, "source": ["### Loan Status"]}, {"cell_type": "code", "execution_count": 26, "id": "c76a8a93", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Current', 'Fully Paid', 'Late (31-120 days)', 'In Grace Period',\n", "       'Charged Off', 'Late (16-30 days)', 'Default',\n", "       'Does not meet the credit policy. Status:Fully Paid',\n", "       'Does not meet the credit policy. Status:Charged Off'],\n", "      dtype=object)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_status\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "099c1ba9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Fully Paid': 1041952,\n", " 'Current': 919695,\n", " 'Charged Off': 261655,\n", " 'Late (31-120 days)': 21897,\n", " 'In Grace Period': 8952,\n", " 'Late (16-30 days)': 3737,\n", " 'Does not meet the credit policy. Status:Fully Paid': 1988,\n", " 'Does not meet the credit policy. Status:Charged Off': 761,\n", " 'Default': 31}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"loan_status\"].value_counts())"]}, {"cell_type": "markdown", "id": "64f572f2", "metadata": {}, "source": ["### Load Data"]}, {"cell_type": "markdown", "id": "71ef3ba9", "metadata": {}, "source": ["#### Loan amount"]}, {"cell_type": "code", "execution_count": 30, "id": "dd770930", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 2500, 30000,  5000, ...,   925,   550,   850], dtype=int64)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_amnt\"].unique()"]}, {"cell_type": "code", "execution_count": 29, "id": "057b1aa6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{10000: 187236,\n", " 20000: 131006,\n", " 15000: 123226,\n", " 12000: 121681,\n", " 35000: 86285,\n", " 5000: 84765,\n", " 8000: 75033,\n", " 6000: 72089,\n", " 25000: 66453,\n", " 16000: 66418,\n", " 30000: 61010,\n", " 24000: 56302,\n", " 18000: 50271,\n", " 7000: 37867,\n", " 14000: 34556,\n", " 40000: 33368,\n", " 4000: 32375,\n", " 28000: 30460,\n", " 3000: 29668,\n", " 21000: 26324,\n", " 9000: 26254,\n", " 13000: 19890,\n", " 11000: 19715,\n", " 9600: 17131,\n", " 2000: 16142,\n", " 7200: 15136,\n", " 17000: 14741,\n", " 32000: 14204,\n", " 7500: 14137,\n", " 14400: 13213,\n", " 4800: 13071,\n", " 22000: 12909,\n", " 11200: 11334,\n", " 8400: 10914,\n", " 3500: 10643,\n", " 6500: 10152,\n", " 2500: 9872,\n", " 1000: 9816,\n", " 3600: 9190,\n", " 5500: 8441,\n", " 19200: 8386,\n", " 8500: 8103,\n", " 5600: 7497,\n", " 6400: 7479,\n", " 4500: 7263,\n", " 19000: 6926,\n", " 23000: 6856,\n", " 10500: 6724,\n", " 16800: 6694,\n", " 4200: 6278,\n", " 1500: 6176,\n", " 2400: 6158,\n", " 12800: 5931,\n", " 36000: 5904,\n", " 12500: 5828,\n", " 10800: 5804,\n", " 27000: 5080,\n", " 26000: 5079,\n", " 11500: 4554,\n", " 3200: 4452,\n", " 9500: 4307,\n", " 21600: 4180,\n", " 17500: 3949,\n", " 13500: 3861,\n", " 13200: 3761,\n", " 1200: 3691,\n", " 15600: 3535,\n", " 6025: 3531,\n", " 10400: 3413,\n", " 7800: 3290,\n", " 2800: 3092,\n", " 16500: 3013,\n", " 12600: 2913,\n", " 5400: 2818,\n", " 10200: 2759,\n", " 22400: 2676,\n", " 9800: 2662,\n", " 1800: 2590,\n", " 14500: 2582,\n", " 33000: 2573,\n", " 17600: 2550,\n", " 6600: 2493,\n", " 15500: 2414,\n", " 1600: 2410,\n", " 20400: 2336,\n", " 8800: 2282,\n", " 29000: 2075,\n", " 18500: 2072,\n", " 5200: 1937,\n", " 22500: 1913,\n", " 13600: 1859,\n", " 26400: 1822,\n", " 2200: 1819,\n", " 25200: 1808,\n", " 28800: 1787,\n", " 19500: 1690,\n", " 34000: 1614,\n", " 3025: 1577,\n", " 6800: 1549,\n", " 11400: 1510,\n", " 31000: 1490,\n", " 20800: 1472,\n", " 6300: 1453,\n", " 25600: 1405,\n", " 15400: 1337,\n", " 19800: 1332,\n", " 2100: 1298,\n", " 33600: 1294,\n", " 1400: 1265,\n", " 22800: 1256,\n", " 9100: 1235,\n", " 20500: 1216,\n", " 18200: 1180,\n", " 16200: 1175,\n", " 7700: 1166,\n", " 23500: 1158,\n", " 19600: 1157,\n", " 27500: 1153,\n", " 8875: 1153,\n", " 18400: 1150,\n", " 13800: 1146,\n", " 26500: 1136,\n", " 4400: 1129,\n", " 15200: 1123,\n", " 6250: 1093,\n", " 14700: 1079,\n", " 9750: 1043,\n", " 6200: 1041,\n", " 24500: 1025,\n", " 5300: 1013,\n", " 27600: 1011,\n", " 10625: 978,\n", " 11700: 930,\n", " 4900: 929,\n", " 2700: 918,\n", " 9450: 913,\n", " 21500: 912,\n", " 9200: 911,\n", " 19750: 911,\n", " 5800: 908,\n", " 12250: 900,\n", " 3300: 893,\n", " 27200: 887,\n", " 7600: 877,\n", " 18900: 874,\n", " 8200: 869,\n", " 11100: 864,\n", " 8325: 859,\n", " 6700: 847,\n", " 28500: 844,\n", " 5700: 841,\n", " 3800: 831,\n", " 7750: 823,\n", " 13250: 815,\n", " 38000: 807,\n", " 9250: 805,\n", " 10075: 805,\n", " 5375: 790,\n", " 10750: 788,\n", " 5100: 786,\n", " 8600: 777,\n", " 6075: 768,\n", " 4750: 766,\n", " 4300: 765,\n", " 31200: 764,\n", " 32500: 763,\n", " 2600: 760,\n", " 17625: 757,\n", " 11900: 743,\n", " 5750: 742,\n", " 25500: 716,\n", " 4600: 712,\n", " 12300: 697,\n", " 16750: 694,\n", " 7900: 691,\n", " 31500: 690,\n", " 7125: 686,\n", " 3250: 684,\n", " 10600: 684,\n", " 9900: 682,\n", " 23800: 681,\n", " 12375: 678,\n", " 8700: 677,\n", " 21200: 670,\n", " 11600: 668,\n", " 7300: 667,\n", " 7550: 657,\n", " 6100: 657,\n", " 3700: 652,\n", " 15250: 647,\n", " 3900: 643,\n", " 7400: 641,\n", " 18225: 638,\n", " 15850: 636,\n", " 13300: 629,\n", " 4950: 618,\n", " 12175: 616,\n", " 22750: 610,\n", " 11875: 608,\n", " 8750: 607,\n", " 24250: 606,\n", " 18825: 604,\n", " 14125: 602,\n", " 14800: 601,\n", " 14300: 598,\n", " 14075: 597,\n", " 37000: 592,\n", " 13750: 591,\n", " 16100: 590,\n", " 6625: 589,\n", " 8300: 583,\n", " 9700: 581,\n", " 3400: 579,\n", " 14600: 578,\n", " 32400: 576,\n", " 10850: 574,\n", " 18250: 573,\n", " 10550: 566,\n", " 6900: 565,\n", " 7350: 564,\n", " 11850: 558,\n", " 15350: 554,\n", " 14975: 554,\n", " 11300: 553,\n", " 4700: 552,\n", " 2300: 551,\n", " 22250: 550,\n", " 12400: 549,\n", " 20700: 549,\n", " 17475: 547,\n", " 9300: 542,\n", " 8100: 540,\n", " 20950: 540,\n", " 7475: 532,\n", " 9350: 531,\n", " 24800: 529,\n", " 12950: 528,\n", " 13475: 525,\n", " 18600: 524,\n", " 17100: 522,\n", " 11325: 521,\n", " 30800: 521,\n", " 17050: 521,\n", " 8950: 519,\n", " 10050: 518,\n", " 11975: 516,\n", " 9950: 514,\n", " 11750: 510,\n", " 7100: 507,\n", " 8250: 498,\n", " 13700: 498,\n", " 10300: 497,\n", " 5250: 497,\n", " 16700: 496,\n", " 6350: 494,\n", " 17400: 494,\n", " 4375: 494,\n", " 3625: 493,\n", " 7225: 493,\n", " 9325: 492,\n", " 20150: 489,\n", " 11950: 488,\n", " 12700: 487,\n", " 14375: 486,\n", " 19300: 485,\n", " 5050: 482,\n", " 9400: 482,\n", " 29900: 482,\n", " 26375: 481,\n", " 16950: 480,\n", " 14900: 479,\n", " 21850: 478,\n", " 20125: 477,\n", " 8975: 475,\n", " 8450: 473,\n", " 10700: 471,\n", " 24925: 468,\n", " 5950: 467,\n", " 15950: 467,\n", " 15100: 466,\n", " 20050: 465,\n", " 11625: 465,\n", " 24375: 462,\n", " 14825: 462,\n", " 16450: 461,\n", " 23850: 461,\n", " 14675: 457,\n", " 3100: 455,\n", " 10575: 452,\n", " 16550: 451,\n", " 8125: 450,\n", " 29700: 450,\n", " 5875: 449,\n", " 6050: 449,\n", " 11250: 447,\n", " 11800: 446,\n", " 10975: 445,\n", " 3850: 444,\n", " 9975: 443,\n", " 27575: 441,\n", " 21250: 440,\n", " 20675: 439,\n", " 9175: 437,\n", " 19075: 437,\n", " 6850: 436,\n", " 6650: 435,\n", " 23400: 435,\n", " 9650: 435,\n", " 17200: 435,\n", " 10375: 434,\n", " 17975: 433,\n", " 14350: 430,\n", " 15825: 430,\n", " 11050: 429,\n", " 12200: 427,\n", " 4100: 426,\n", " 15750: 426,\n", " 16425: 426,\n", " 23200: 425,\n", " 18700: 425,\n", " 12075: 423,\n", " 14950: 423,\n", " 8725: 422,\n", " 8900: 422,\n", " 16300: 421,\n", " 9925: 421,\n", " 19125: 418,\n", " 8650: 417,\n", " 8150: 417,\n", " 23100: 414,\n", " 19350: 413,\n", " 6725: 412,\n", " 8575: 412,\n", " 12750: 410,\n", " 9125: 409,\n", " 18450: 409,\n", " 15875: 407,\n", " 29500: 407,\n", " 10100: 407,\n", " 18550: 407,\n", " 7925: 406,\n", " 27300: 406,\n", " 4250: 405,\n", " 13150: 405,\n", " 6475: 403,\n", " 15300: 402,\n", " 7250: 401,\n", " 19950: 401,\n", " 2750: 401,\n", " 5125: 401,\n", " 12150: 399,\n", " 15125: 399,\n", " 8050: 398,\n", " 17275: 398,\n", " 4450: 398,\n", " 13100: 396,\n", " 14250: 394,\n", " 9850: 393,\n", " 14750: 393,\n", " 5900: 392,\n", " 21100: 391,\n", " 29400: 389,\n", " 7675: 389,\n", " 2875: 389,\n", " 10450: 389,\n", " 19425: 388,\n", " 12875: 383,\n", " 2650: 383,\n", " 7450: 381,\n", " 14100: 381,\n", " 15800: 377,\n", " 10875: 377,\n", " 12050: 376,\n", " 10250: 376,\n", " 11150: 376,\n", " 13675: 375,\n", " 1300: 375,\n", " 18350: 375,\n", " 29975: 374,\n", " 9550: 373,\n", " 13375: 373,\n", " 10900: 372,\n", " 8275: 371,\n", " 4550: 369,\n", " 6950: 368,\n", " 1700: 368,\n", " 11525: 367,\n", " 5450: 367,\n", " 34500: 366,\n", " 4325: 365,\n", " 7975: 364,\n", " 13400: 363,\n", " 29175: 363,\n", " 22200: 363,\n", " 13925: 363,\n", " 6150: 362,\n", " 18475: 361,\n", " 3125: 361,\n", " 10650: 359,\n", " 32200: 358,\n", " 22950: 357,\n", " 7150: 357,\n", " 22875: 356,\n", " 12900: 356,\n", " 16150: 354,\n", " 9875: 354,\n", " 18800: 354,\n", " 8625: 354,\n", " 24575: 353,\n", " 12225: 351,\n", " 5275: 351,\n", " 12450: 351,\n", " 11550: 349,\n", " 17325: 349,\n", " 11075: 349,\n", " 12100: 347,\n", " 6450: 347,\n", " 23325: 347,\n", " 13900: 347,\n", " 1450: 346,\n", " 26600: 346,\n", " 3350: 346,\n", " 7375: 345,\n", " 3750: 342,\n", " 19400: 342,\n", " 3825: 342,\n", " 23900: 341,\n", " 33425: 341,\n", " 14575: 341,\n", " 24200: 340,\n", " 10325: 339,\n", " 10725: 339,\n", " 10950: 338,\n", " 15050: 338,\n", " 10525: 337,\n", " 25975: 333,\n", " 17875: 333,\n", " 12725: 332,\n", " 15550: 332,\n", " 8425: 332,\n", " 12575: 331,\n", " 39000: 331,\n", " 22075: 331,\n", " 15625: 329,\n", " 31300: 328,\n", " 7050: 328,\n", " 18300: 326,\n", " 5825: 326,\n", " 13625: 326,\n", " 8475: 324,\n", " 10425: 324,\n", " 4475: 324,\n", " 14200: 324,\n", " 3950: 323,\n", " 15900: 322,\n", " 16900: 322,\n", " 14025: 320,\n", " 13850: 318,\n", " 6750: 317,\n", " 4850: 317,\n", " 23675: 315,\n", " 4225: 315,\n", " 21725: 315,\n", " 18150: 315,\n", " 7025: 314,\n", " 5550: 314,\n", " 16400: 313,\n", " 20225: 313,\n", " 10150: 313,\n", " 38400: 310,\n", " 6325: 310,\n", " 13225: 309,\n", " 9525: 308,\n", " 7875: 308,\n", " 10475: 307,\n", " 5975: 307,\n", " 10350: 307,\n", " 25450: 306,\n", " 6825: 305,\n", " 8225: 305,\n", " 2250: 304,\n", " 5325: 303,\n", " 13425: 303,\n", " 13450: 302,\n", " 13650: 301,\n", " 15450: 301,\n", " 11925: 300,\n", " 30225: 299,\n", " 17550: 299,\n", " 7850: 299,\n", " 7650: 299,\n", " 18075: 298,\n", " 10775: 298,\n", " 9050: 297,\n", " 8350: 296,\n", " 33500: 296,\n", " 21075: 295,\n", " 11650: 295,\n", " 5650: 294,\n", " 5150: 294,\n", " 20250: 294,\n", " 10925: 294,\n", " 7625: 293,\n", " 15700: 292,\n", " 28100: 290,\n", " 4625: 290,\n", " 19725: 290,\n", " 11675: 290,\n", " 17800: 289,\n", " 5525: 289,\n", " 14850: 288,\n", " 12325: 287,\n", " 5850: 287,\n", " 16375: 286,\n", " 23975: 285,\n", " 6925: 285,\n", " 12550: 283,\n", " 9725: 283,\n", " 8825: 283,\n", " 14275: 283,\n", " 15175: 283,\n", " 11575: 282,\n", " 4675: 281,\n", " 4350: 281,\n", " 12350: 281,\n", " 4725: 280,\n", " 12275: 280,\n", " 7575: 279,\n", " 21350: 278,\n", " 30750: 278,\n", " 29100: 278,\n", " 30500: 278,\n", " 24600: 278,\n", " 11175: 277,\n", " 4075: 277,\n", " 2900: 277,\n", " 4150: 277,\n", " 6225: 276,\n", " 32350: 275,\n", " 15075: 275,\n", " 24625: 275,\n", " 24125: 275,\n", " 11725: 275,\n", " 17700: 275,\n", " 17850: 275,\n", " 12475: 274,\n", " 11775: 274,\n", " 9225: 273,\n", " 11425: 273,\n", " 14050: 273,\n", " 7950: 272,\n", " 11350: 272,\n", " 19700: 271,\n", " 11225: 271,\n", " 8675: 271,\n", " 23750: 270,\n", " 23925: 270,\n", " 17350: 269,\n", " 5175: 268,\n", " 12825: 268,\n", " 14725: 268,\n", " 7075: 268,\n", " 24950: 268,\n", " 5225: 267,\n", " 13050: 267,\n", " 12650: 266,\n", " 11475: 265,\n", " 9150: 264,\n", " 9025: 264,\n", " 9775: 263,\n", " 11450: 263,\n", " 4575: 263,\n", " 25475: 262,\n", " 6125: 262,\n", " 4125: 262,\n", " 14450: 262,\n", " 4650: 261,\n", " 10225: 260,\n", " 27050: 260,\n", " 5350: 260,\n", " 16600: 260,\n", " 24700: 259,\n", " 7775: 259,\n", " 5075: 259,\n", " 6375: 259,\n", " 13350: 259,\n", " 34800: 258,\n", " 18725: 257,\n", " 11125: 257,\n", " 7325: 257,\n", " 3975: 256,\n", " 15775: 255,\n", " 15475: 255,\n", " 5725: 255,\n", " 5925: 255,\n", " 17125: 254,\n", " 13550: 254,\n", " 12125: 253,\n", " 19575: 253,\n", " 25300: 252,\n", " 21125: 252,\n", " 6550: 252,\n", " 17750: 252,\n", " 1100: 252,\n", " 5675: 252,\n", " 8850: 251,\n", " 28200: 250,\n", " 28625: 250,\n", " 17225: 250,\n", " 11025: 250,\n", " 4875: 249,\n", " 7275: 248,\n", " 14475: 248,\n", " 5025: 247,\n", " 7425: 247,\n", " 2150: 246,\n", " 14875: 246,\n", " 28775: 245,\n", " 23600: 245,\n", " 13325: 245,\n", " 19250: 245,\n", " 17150: 244,\n", " 34475: 244,\n", " 19375: 244,\n", " 19775: 243,\n", " 23050: 243,\n", " 14225: 243,\n", " 13825: 242,\n", " 21825: 241,\n", " 8550: 241,\n", " 16825: 241,\n", " 14550: 241,\n", " 7525: 240,\n", " 18950: 240,\n", " 17250: 239,\n", " 9575: 239,\n", " 10175: 239,\n", " 17425: 238,\n", " 19650: 238,\n", " 3050: 236,\n", " 19150: 236,\n", " 18375: 235,\n", " 18750: 235,\n", " 22675: 235,\n", " 6425: 235,\n", " 23450: 234,\n", " 6575: 234,\n", " 33950: 234,\n", " 30400: 233,\n", " 2975: 233,\n", " 14325: 233,\n", " 9375: 233,\n", " 3150: 233,\n", " 12850: 232,\n", " 4825: 232,\n", " 19475: 232,\n", " 6275: 232,\n", " 18675: 232,\n", " 4525: 232,\n", " 11375: 231,\n", " 1750: 230,\n", " 5625: 230,\n", " 7825: 229,\n", " 13025: 229,\n", " 9475: 229,\n", " 17900: 228,\n", " 12525: 228,\n", " 17925: 228,\n", " 15225: 228,\n", " 13975: 228,\n", " 16525: 228,\n", " 28600: 226,\n", " 5575: 226,\n", " 14150: 226,\n", " 16225: 226,\n", " 17950: 225,\n", " 10125: 225,\n", " 16650: 224,\n", " 13950: 224,\n", " 12975: 224,\n", " 16850: 224,\n", " 19100: 224,\n", " 7175: 224,\n", " 13175: 223,\n", " 8075: 222,\n", " 11275: 220,\n", " 10675: 219,\n", " 20750: 218,\n", " 15925: 218,\n", " 24450: 217,\n", " 18625: 217,\n", " 29250: 217,\n", " 17300: 216,\n", " 16325: 216,\n", " 9625: 216,\n", " 10025: 216,\n", " 15325: 216,\n", " 9275: 216,\n", " 12925: 215,\n", " 18775: 215,\n", " 22475: 215,\n", " 23350: 215,\n", " 24750: 214,\n", " 13125: 214,\n", " 6775: 213,\n", " 21400: 213,\n", " 16575: 213,\n", " 21550: 213,\n", " 21150: 213,\n", " 18050: 213,\n", " 24350: 212,\n", " 24650: 212,\n", " 15275: 212,\n", " 19850: 212,\n", " 19050: 211,\n", " 1900: 211,\n", " 3650: 211,\n", " 20300: 211,\n", " 8775: 211,\n", " 24175: 211,\n", " 14175: 211,\n", " 1925: 210,\n", " 14525: 210,\n", " 8925: 210,\n", " 3075: 209,\n", " 3550: 209,\n", " 19875: 209,\n", " 21700: 209,\n", " 20375: 208,\n", " 17450: 208,\n", " 12625: 208,\n", " 9075: 208,\n", " 4925: 208,\n", " 24825: 207,\n", " 13775: 207,\n", " 19675: 207,\n", " 20900: 206,\n", " 13575: 206,\n", " 29850: 206,\n", " 5425: 206,\n", " 18425: 205,\n", " 16250: 205,\n", " 32875: 205,\n", " 5475: 205,\n", " 33575: 204,\n", " 7725: 204,\n", " 9675: 203,\n", " 17650: 203,\n", " 8375: 203,\n", " 22300: 203,\n", " 16675: 202,\n", " 14425: 202,\n", " 16625: 202,\n", " 29325: 202,\n", " 31825: 202,\n", " 20975: 202,\n", " 24300: 201,\n", " 20600: 201,\n", " 8525: 201,\n", " 15425: 201,\n", " 12675: 201,\n", " 16975: 201,\n", " 21300: 200,\n", " 20200: 200,\n", " 15675: 199,\n", " 3525: 198,\n", " 20875: 198,\n", " 14775: 198,\n", " 23875: 198,\n", " 21275: 198,\n", " 27450: 197,\n", " 6525: 197,\n", " 21750: 197,\n", " 22100: 196,\n", " 12025: 195,\n", " 4175: 195,\n", " 9825: 195,\n", " 12775: 193,\n", " 1675: 193,\n", " 16475: 193,\n", " 6675: 193,\n", " 22525: 193,\n", " 17675: 192,\n", " 2525: 192,\n", " 4050: 192,\n", " 13275: 192,\n", " 12425: 191,\n", " 15975: 191,\n", " 11825: 190,\n", " 17775: 189,\n", " 24975: 189,\n", " 3675: 188,\n", " 6875: 188,\n", " 25900: 188,\n", " 3725: 188,\n", " 28975: 188,\n", " 4975: 187,\n", " 3450: 187,\n", " 4775: 187,\n", " 20475: 187,\n", " 15575: 187,\n", " 22425: 187,\n", " 6975: 186,\n", " 2850: 186,\n", " 19900: 186,\n", " 5775: 186,\n", " 6175: 186,\n", " 13725: 186,\n", " 3775: 185,\n", " 37500: 185,\n", " 18850: 185,\n", " 23700: 184,\n", " 25750: 184,\n", " 18975: 184,\n", " 17375: 184,\n", " 22050: 184,\n", " 27975: 184,\n", " 23025: 184,\n", " 23300: 183,\n", " 20275: 183,\n", " 15375: 183,\n", " 3475: 183,\n", " 14650: 183,\n", " 21625: 182,\n", " 2550: 182,\n", " 19975: 182,\n", " 18125: 182,\n", " 15150: 182,\n", " 20575: 181,\n", " 10825: 181,\n", " 16275: 181,\n", " 18875: 181,\n", " 20350: 180,\n", " 25075: 180,\n", " 32100: 180,\n", " 23475: 179,\n", " 19225: 178,\n", " 18025: 177,\n", " 3175: 177,\n", " 24475: 177,\n", " 15725: 177,\n", " 19175: 177,\n", " 14625: 177,\n", " 9425: 176,\n", " 4425: 175,\n", " 17825: 175,\n", " 26200: 174,\n", " 15650: 174,\n", " 23725: 173,\n", " 16925: 173,\n", " 22325: 173,\n", " 18275: 172,\n", " 16075: 172,\n", " 4275: 172,\n", " 16875: 171,\n", " 13075: 171,\n", " 19450: 170,\n", " 22375: 170,\n", " 3275: 169,\n", " 22550: 169,\n", " 20825: 169,\n", " 26250: 169,\n", " 20550: 169,\n", " 25725: 169,\n", " 20100: 169,\n", " 16025: 169,\n", " 29600: 168,\n", " 22850: 168,\n", " 22600: 168,\n", " 14925: 168,\n", " 21975: 167,\n", " 18650: 167,\n", " 8025: 167,\n", " 18175: 167,\n", " 21650: 166,\n", " 3925: 166,\n", " 16125: 166,\n", " 18100: 165,\n", " 21525: 165,\n", " 29750: 165,\n", " 16775: 164,\n", " 10275: 164,\n", " 21800: 163,\n", " 22125: 162,\n", " 21475: 161,\n", " 20325: 161,\n", " 20725: 161,\n", " 20775: 160,\n", " 2950: 160,\n", " 23275: 160,\n", " 26750: 160,\n", " 17575: 160,\n", " 26875: 159,\n", " 19275: 159,\n", " 24525: 159,\n", " 18525: 159,\n", " 24725: 158,\n", " 3875: 158,\n", " 23075: 158,\n", " 3325: 158,\n", " 19825: 158,\n", " 23550: 157,\n", " 13525: 157,\n", " 20425: 157,\n", " 16050: 156,\n", " 26850: 156,\n", " 21450: 156,\n", " 25850: 156,\n", " 24900: 156,\n", " 20850: 156,\n", " 4025: 155,\n", " 19325: 155,\n", " 26800: 155,\n", " 3225: 154,\n", " 34550: 154,\n", " 15525: 154,\n", " 29450: 154,\n", " 20175: 154,\n", " 15025: 153,\n", " 3425: 153,\n", " 32625: 153,\n", " 21175: 151,\n", " 16350: 151,\n", " 3575: 151,\n", " 13875: 150,\n", " 23225: 150,\n", " 21900: 150,\n", " 17525: 150,\n", " 3375: 149,\n", " 20625: 149,\n", " 32750: 149,\n", " 21050: 149,\n", " 31175: 147,\n", " 18325: 147,\n", " 26675: 147,\n", " 21950: 147,\n", " 21325: 147,\n", " 20650: 145,\n", " 27650: 145,\n", " 25800: 145,\n", " 31450: 145,\n", " 19025: 144,\n", " 19625: 144,\n", " 22450: 144,\n", " 22900: 142,\n", " 23425: 142,\n", " 19550: 142,\n", " 25150: 141,\n", " 2725: 141,\n", " 24400: 141,\n", " 20450: 141,\n", " 26650: 138,\n", " 17175: 138,\n", " 27275: 138,\n", " 17025: 138,\n", " 24550: 138,\n", " 17725: 137,\n", " 23175: 137,\n", " 26575: 137,\n", " 1550: 137,\n", " 23125: 137,\n", " 18575: 136,\n", " 8175: 135,\n", " 16175: 135,\n", " 22350: 135,\n", " 25250: 135,\n", " 22650: 135,\n", " 25325: 133,\n", " 2050: 132,\n", " 34200: 131,\n", " 16725: 131,\n", " 21875: 131,\n", " 23950: 130,\n", " 28550: 130,\n", " 17075: 130,\n", " 19925: 130,\n", " 23650: 129,\n", " 27250: 129,\n", " 25375: 128,\n", " 21925: 128,\n", " 22025: 127,\n", " 25925: 127,\n", " 1950: 127,\n", " 27325: 126,\n", " 25525: 126,\n", " 27700: 126,\n", " 26300: 125,\n", " 23250: 125,\n", " 25700: 124,\n", " 22700: 124,\n", " 27850: 124,\n", " 24150: 123,\n", " 26050: 123,\n", " 27525: 123,\n", " 31575: 123,\n", " 23525: 123,\n", " 30600: 123,\n", " 24850: 123,\n", " 21375: 122,\n", " 22150: 121,\n", " ...}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "markdown", "id": "ef9e74d4", "metadata": {}, "source": ["#### Interest rate"]}, {"cell_type": "code", "execution_count": 31, "id": "a5c46c66", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([13.56, 18.94, 17.97, 16.14, 15.02, 14.47, 22.35, 11.31,  8.19,\n", "       12.98, 16.91, 20.89, 23.4 , 26.31, 11.8 , 19.92,  6.46, 27.27,\n", "       10.72, 10.33,  8.81, 30.75,  7.56, 25.34,  7.02, 24.37, 30.65,\n", "        6.  , 30.79, 30.84, 28.72, 11.06,  7.21, 12.73, 11.55,  8.46,\n", "       29.69,  6.11,  7.84,  6.67, 10.47, 30.17, 10.08, 30.94, 30.89,\n", "       30.99, 14.03, 13.06,  6.19, 11.05, 15.49, 21.85, 10.56,  7.46,\n", "        5.31, 14.52,  9.58, 18.45, 12.13,  8.08, 19.42,  6.83, 16.46,\n", "       10.07, 17.47, 20.39, 25.81, 26.77, 23.87, 22.9 , 24.84, 12.61,\n", "       14.07,  6.71,  7.96,  9.43, 10.9 , 11.98, 13.58, 16.01, 15.04,\n", "        7.34,  6.07, 10.41,  9.92,  7.97,  7.35, 17.09,  9.44, 20.  ,\n", "       18.06, 10.91, 11.99, 15.05,  6.08,  9.93, 12.62, 24.85, 23.88,\n", "       14.08, 10.42,  6.72, 19.03, 16.02, 21.45, 13.59,  5.32, 25.82,\n", "       26.3 , 22.91, 12.79, 17.99,  8.59, 21.49,  8.99, 13.99, 14.49,\n", "       11.49, 10.99,  6.99, 26.49, 16.99, 15.59, 24.99, 10.49, 13.49,\n", "       22.39,  7.59, 19.99,  9.49, 18.99,  7.99, 25.69, 23.99, 28.18,\n", "       27.49, 29.67, 25.29, 24.49, 29.96, 27.79, 28.88, 26.99,  6.97,\n", "       10.75, 12.99,  9.75,  7.39,  9.16, 20.99, 11.47,  7.89, 14.46,\n", "       15.31, 17.27, 16.29,  8.39,  6.49, 18.25, 13.67, 25.65, 23.32,\n", "       25.11, 26.14, 25.44, 27.34, 24.11, 21.97, 23.13, 21.18, 19.53,\n", "       25.88, 26.57, 22.45, 20.75, 28.67, 28.99, 28.34, 28.14, 15.29,\n", "       11.16, 16.59,  8.49, 21.48, 13.44, 10.78,  9.17, 12.88,  7.91,\n", "       11.48, 24.24, 18.49, 14.85,  7.49,  9.8 , 20.5 , 15.77, 19.89,\n", "       25.09, 19.48, 28.49, 26.06, 27.99, 13.19, 14.77,  6.89, 12.59,\n", "       17.57, 10.64, 16.55, 13.18,  8.38, 11.22, 15.41,  9.76, 22.99,\n", "       17.86, 14.48,  6.24, 25.99,  7.26, 21.99, 27.31, 18.2 , 28.69,\n", "        8.24, 11.39, 25.49, 11.44, 15.99, 12.74, 14.99,  7.24, 22.74,\n", "       26.24, 29.99, 29.49, 24.74, 30.49, 30.74, 27.88, 14.33, 12.05,\n", "        8.18,  9.99, 16.9 , 18.55, 11.53, 14.65, 13.33, 12.69, 19.19,\n", "       12.29, 15.61, 25.78, 19.52, 24.5 ,  6.39,  5.93,  6.92, 21.67,\n", "       20.49,  6.68, 18.84, 25.8 , 25.57, 25.89, 25.83, 16.49, 13.66,\n", "       12.39, 18.54,  8.67, 17.14,  6.03, 14.31, 19.24,  7.07,  6.62,\n", "        7.62, 16.24, 12.85, 13.53,  8.9 ,  9.67, 14.98, 19.97, 13.98,\n", "        7.9 , 19.22, 22.4 , 23.7 , 24.08, 17.76, 21.7 , 16.2 , 15.1 ,\n", "       19.2 , 14.3 , 20.2 , 17.1 , 23.1 , 21.  ,  8.6 ,  9.25, 22.2 ,\n", "       22.7 , 15.22, 13.68, 21.6 , 16.78, 18.85,  9.71, 17.56, 15.88,\n", "       13.05, 12.35, 23.5 , 20.8 , 21.15, 24.89, 20.31, 25.28, 10.16,\n", "       15.8 , 12.12, 19.05, 18.75, 19.72, 17.77, 14.09, 11.14, 13.11,\n", "       23.28, 23.76, 21.98, 23.63, 23.83, 24.7 , 22.95, 22.47, 24.83,\n", "       10.74, 15.81, 22.78, 23.33, 24.2 , 24.76, 24.33, 23.26, 24.52,\n", "       15.27,  9.91, 20.3 , 15.96, 12.42, 17.58, 11.71, 19.91, 10.65,\n", "       16.77, 14.27,  7.51, 21.28, 23.52, 18.64, 23.91, 22.06, 14.28,\n", "       12.49, 18.24,  7.69, 13.35, 10.15, 23.43, 11.67, 22.15,  7.12,\n", "       17.5 , 14.64, 13.65, 14.16, 19.47, 18.92, 22.11,  5.99, 18.79,\n", "       15.23, 14.79,  5.42, 10.59, 17.49, 15.62, 21.36, 19.29, 18.39,\n", "       16.89, 20.62, 22.85, 19.69, 20.25, 23.22, 21.74, 22.48, 23.59,\n", "       18.07, 11.63,  7.42, 19.39, 16.11, 17.54, 22.64, 13.84, 17.19,\n", "       12.87, 20.69, 21.82, 19.79, 22.94, 24.59, 24.4 , 14.82, 14.17,\n", "        7.29, 17.88, 20.11, 17.51, 13.43, 14.91, 15.28, 15.65, 11.11,\n", "       10.37, 16.4 ,  7.66, 10.  , 18.62,  5.79,  9.63, 14.54, 12.68,\n", "       19.36, 13.8 , 21.59, 20.85, 21.22, 19.74, 20.48,  6.91, 12.23,\n", "       10.36,  6.17,  6.54, 16.69, 15.95,  8.88,  9.62, 16.32, 14.83,\n", "       13.72, 20.03, 17.8 , 15.2 , 15.57, 19.66, 17.06, 18.17, 17.43,\n", "       20.4 , 20.77, 18.91, 21.14, 17.44, 13.23,  7.88, 11.12, 13.61,\n", "       10.38, 17.93, 15.58, 14.84, 15.21,  6.76, 11.86,  7.14, 14.35,\n", "       16.82, 14.72, 16.45, 18.67, 20.53, 19.41, 20.16, 21.27, 18.3 ,\n", "       19.04, 20.9 , 21.64, 10.25, 10.62, 13.48, 14.59, 16.07, 15.7 ,\n", "        9.88, 11.36, 15.33, 13.85, 14.96, 14.22,  7.74, 13.22, 13.57,\n", "       17.04, 14.61,  8.94, 12.18, 11.83, 16.35, 13.92, 14.26, 19.13,\n", "       12.53, 16.7 , 16.  , 17.39, 18.09,  7.4 , 18.43, 17.74,  7.05,\n", "       20.52, 20.86, 18.78, 21.21, 19.82, 20.17, 13.16,  8.  , 13.47,\n", "       12.21, 16.63,  9.32, 12.84, 11.26, 15.68, 15.37, 10.95, 11.89,\n", "       14.11, 13.79,  7.68, 11.58,  7.37, 16.95, 18.53, 14.74, 14.42,\n", "       18.21, 17.26, 17.9 , 19.16,  9.38, 12.72, 13.36, 11.46, 10.51,\n", "        9.07, 13.04, 11.78, 12.41, 10.83, 12.09, 17.46, 17.15, 15.25,\n", "       10.2 , 14.93, 18.72, 14.62,  8.32, 14.12, 10.96, 10.01, 12.86,\n", "       11.28, 11.59,  8.63, 12.54, 12.22, 11.91, 15.38, 16.96, 13.17,\n", "        9.7 , 16.33, 14.75, 15.07, 10.71, 11.34, 10.39, 13.87, 11.03,\n", "       11.66, 13.24,  9.45, 13.55, 11.97, 12.92, 15.45, 14.5 , 14.18,\n", "       15.13, 16.08, 15.76, 17.03, 17.34, 16.71,  9.83, 13.62, 10.46,\n", "        9.51,  9.2 , 13.3 ,  7.75, 12.36, 12.67, 11.72, 13.93,  8.07,\n", "        7.43, 12.04, 14.25, 14.88, 11.41, 11.09, 10.14, 16.15, 15.83,\n", "       18.36,  9.64,  9.96,  9.01,  9.33, 11.54, 12.17, 12.8 , 14.38,\n", "       13.75, 14.7 , 13.12, 10.28,  8.7 , 14.67, 15.01, 17.78, 16.83,\n", "       17.59, 14.43, 16.65, 17.91, 17.28, 18.86, 18.61, 17.66, 18.29,\n", "       18.04, 14.57, 17.72, 15.51, 17.41, 17.22, 16.28])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"int_rate\"].unique()"]}, {"cell_type": "code", "execution_count": 32, "id": "2c6586da", "metadata": {}, "outputs": [{"data": {"text/plain": ["{11.99: 53869,\n", " 5.32: 47171,\n", " 10.99: 44165,\n", " 13.99: 43026,\n", " 11.49: 32009,\n", " 16.99: 30564,\n", " 12.99: 29276,\n", " 7.89: 28515,\n", " 9.17: 27835,\n", " 15.61: 25208,\n", " 14.99: 25108,\n", " 13.49: 24304,\n", " 16.02: 23711,\n", " 12.62: 23422,\n", " 10.42: 23182,\n", " 9.44: 22982,\n", " 15.05: 22283,\n", " 9.93: 22268,\n", " 14.08: 22146,\n", " 10.49: 22020,\n", " 9.99: 21547,\n", " 13.59: 21341,\n", " 12.74: 19989,\n", " 18.99: 19776,\n", " 12.69: 18774,\n", " 12.29: 18598,\n", " 10.91: 18485,\n", " 17.57: 18004,\n", " 11.44: 17970,\n", " 8.18: 17811,\n", " 7.97: 17676,\n", " 13.33: 17568,\n", " 15.99: 17487,\n", " 13.67: 17334,\n", " 7.21: 17298,\n", " 19.99: 17205,\n", " 16.29: 17095,\n", " 15.31: 16427,\n", " 11.53: 16180,\n", " 14.49: 16118,\n", " 18.25: 15459,\n", " 8.39: 15213,\n", " 7.35: 14782,\n", " 14.65: 14763,\n", " 8.24: 14760,\n", " 11.55: 14399,\n", " 14.47: 13984,\n", " 13.56: 13719,\n", " 6.11: 13713,\n", " 15.02: 13126,\n", " 17.99: 13017,\n", " 10.75: 11943,\n", " 6.49: 11884,\n", " 7.84: 11857,\n", " 11.47: 11808,\n", " 9.75: 11771,\n", " 6.99: 11600,\n", " 8.9: 11426,\n", " 16.14: 11338,\n", " 18.06: 11332,\n", " 11.39: 11229,\n", " 19.03: 11227,\n", " 15.59: 11206,\n", " 16.91: 11075,\n", " 7.49: 11013,\n", " 16.55: 10954,\n", " 9.49: 10794,\n", " 6.03: 10755,\n", " 7.99: 10639,\n", " 10.47: 10556,\n", " 8.46: 10505,\n", " 17.86: 10464,\n", " 17.27: 10438,\n", " 9.16: 10396,\n", " 7.9: 10079,\n", " 12.79: 9897,\n", " 13.98: 9800,\n", " 8.19: 9779,\n", " 12.73: 9758,\n", " 14.46: 9744,\n", " 12.49: 9726,\n", " 10.08: 9613,\n", " 17.09: 9459,\n", " 7.26: 9450,\n", " 12.12: 9414,\n", " 17.97: 9332,\n", " 14.33: 8835,\n", " 7.39: 8760,\n", " 6.67: 8752,\n", " 5.31: 8613,\n", " 13.11: 8459,\n", " 21.45: 8367,\n", " 11.06: 8218,\n", " 20.0: 8173,\n", " 18.94: 7859,\n", " 13.35: 7756,\n", " 6.24: 7477,\n", " 11.67: 7255,\n", " 19.92: 7135,\n", " 7.07: 7125,\n", " 10.9: 7061,\n", " 6.89: 7019,\n", " 8.99: 6989,\n", " 11.98: 6944,\n", " 12.61: 6833,\n", " 9.43: 6794,\n", " 6.72: 6670,\n", " 20.99: 6656,\n", " 7.62: 6650,\n", " 11.14: 6643,\n", " 19.52: 6613,\n", " 9.92: 6562,\n", " 20.89: 6193,\n", " 7.34: 6178,\n", " 10.15: 6116,\n", " 21.49: 6092,\n", " 23.99: 5967,\n", " 17.47: 5909,\n", " 14.07: 5785,\n", " 13.58: 5733,\n", " 18.55: 5665,\n", " 6.08: 5639,\n", " 18.45: 5588,\n", " 22.35: 5560,\n", " 15.04: 5530,\n", " 6.71: 5480,\n", " 6.62: 5409,\n", " 7.24: 5253,\n", " 6.97: 5244,\n", " 16.01: 5209,\n", " 19.42: 5172,\n", " 9.67: 5075,\n", " 6.07: 5020,\n", " 7.96: 5004,\n", " 11.8: 4983,\n", " 7.69: 4962,\n", " 12.39: 4759,\n", " 18.49: 4755,\n", " 11.05: 4723,\n", " 10.41: 4691,\n", " 10.64: 4684,\n", " 24.99: 4675,\n", " 15.8: 4614,\n", " 20.39: 4546,\n", " 13.06: 4522,\n", " 12.13: 4390,\n", " 12.59: 4382,\n", " 13.18: 4380,\n", " 7.46: 4352,\n", " 10.07: 4339,\n", " 13.66: 4327,\n", " 14.09: 4320,\n", " 10.16: 4318,\n", " 21.85: 4302,\n", " 6.92: 4274,\n", " 14.31: 4228,\n", " 8.81: 4192,\n", " 9.58: 4119,\n", " 8.59: 4091,\n", " 26.3: 4064,\n", " 22.74: 4033,\n", " 6.46: 4030,\n", " 10.72: 4003,\n", " 12.98: 3995,\n", " 14.03: 3964,\n", " 14.52: 3913,\n", " 6.83: 3906,\n", " 15.49: 3826,\n", " 8.08: 3598,\n", " 18.24: 3597,\n", " 21.99: 3577,\n", " 19.19: 3556,\n", " 8.67: 3543,\n", " 23.4: 3538,\n", " 16.46: 3536,\n", " 10.33: 3506,\n", " 16.59: 3494,\n", " 17.77: 3483,\n", " 20.49: 3483,\n", " 23.88: 3449,\n", " 6.19: 3305,\n", " 10.56: 3285,\n", " 24.85: 3227,\n", " 9.76: 3210,\n", " 20.2: 3184,\n", " 7.12: 3064,\n", " 14.64: 3029,\n", " 11.31: 3029,\n", " 14.16: 2952,\n", " 7.56: 2907,\n", " 22.99: 2870,\n", " 8.49: 2828,\n", " 7.02: 2823,\n", " 15.41: 2760,\n", " 13.65: 2753,\n", " 21.18: 2745,\n", " 10.78: 2731,\n", " 6.39: 2675,\n", " 11.48: 2674,\n", " 9.8: 2664,\n", " 16.49: 2598,\n", " 25.82: 2556,\n", " 12.35: 2546,\n", " 19.53: 2536,\n", " 27.27: 2499,\n", " 17.14: 2485,\n", " 6.68: 2471,\n", " 24.5: 2462,\n", " 22.45: 2363,\n", " 19.05: 2362,\n", " 12.05: 2333,\n", " 7.59: 2331,\n", " 18.75: 2280,\n", " 11.22: 2280,\n", " 13.44: 2267,\n", " 28.72: 2248,\n", " 12.88: 2246,\n", " 26.77: 2173,\n", " 14.3: 2139,\n", " 7.91: 2129,\n", " 20.75: 2102,\n", " 22.39: 2101,\n", " 16.2: 2089,\n", " 9.71: 2084,\n", " 12.85: 2076,\n", " 25.49: 2069,\n", " 14.98: 2067,\n", " 15.1: 2036,\n", " 19.72: 2029,\n", " 15.88: 1980,\n", " 13.53: 1971,\n", " 18.84: 1964,\n", " 8.38: 1920,\n", " 25.34: 1920,\n", " 21.0: 1916,\n", " 15.22: 1881,\n", " 16.78: 1881,\n", " 17.1: 1837,\n", " 5.93: 1812,\n", " 13.68: 1808,\n", " 24.08: 1784,\n", " 24.37: 1779,\n", " 14.85: 1778,\n", " 24.74: 1774,\n", " 26.24: 1749,\n", " 18.54: 1735,\n", " 21.97: 1707,\n", " 16.24: 1690,\n", " 26.31: 1636,\n", " 22.15: 1598,\n", " 22.91: 1575,\n", " 30.79: 1572,\n", " 18.92: 1561,\n", " 17.76: 1535,\n", " 14.48: 1533,\n", " 23.43: 1531,\n", " 23.13: 1517,\n", " 17.56: 1498,\n", " 19.24: 1478,\n", " 20.5: 1418,\n", " 28.69: 1416,\n", " 24.49: 1410,\n", " 15.77: 1407,\n", " 18.2: 1373,\n", " 24.84: 1372,\n", " 19.47: 1331,\n", " 19.22: 1244,\n", " 22.9: 1242,\n", " 30.17: 1236,\n", " 25.57: 1216,\n", " 29.69: 1214,\n", " 25.81: 1204,\n", " 10.74: 1199,\n", " 13.05: 1168,\n", " 21.67: 1118,\n", " 21.48: 1108,\n", " 21.98: 1087,\n", " 30.75: 1075,\n", " 25.8: 1023,\n", " 25.29: 1023,\n", " 19.2: 995,\n", " 30.65: 983,\n", " 11.71: 974,\n", " 7.51: 973,\n", " 18.85: 933,\n", " 22.4: 885,\n", " 25.69: 863,\n", " 23.87: 831,\n", " 30.99: 819,\n", " 24.11: 809,\n", " 22.47: 809,\n", " 25.83: 796,\n", " 29.49: 785,\n", " 19.97: 779,\n", " 20.31: 773,\n", " 19.48: 756,\n", " 30.84: 755,\n", " 7.88: 742,\n", " 22.95: 737,\n", " 30.94: 733,\n", " 9.25: 705,\n", " 12.42: 702,\n", " 30.89: 699,\n", " 10.65: 679,\n", " 6.0: 675,\n", " 26.49: 670,\n", " 21.15: 649,\n", " 19.89: 646,\n", " 29.99: 641,\n", " 14.27: 637,\n", " 26.99: 623,\n", " 23.28: 616,\n", " 21.7: 612,\n", " 25.89: 599,\n", " 9.91: 595,\n", " 23.7: 573,\n", " 5.42: 573,\n", " 25.99: 551,\n", " 25.88: 513,\n", " 30.49: 512,\n", " 23.63: 506,\n", " 27.49: 497,\n", " 22.2: 489,\n", " 21.6: 485,\n", " 25.78: 482,\n", " 26.57: 476,\n", " 10.37: 470,\n", " 16.77: 469,\n", " 26.06: 465,\n", " 30.74: 456,\n", " 23.1: 429,\n", " 11.86: 418,\n", " 23.32: 417,\n", " 5.79: 410,\n", " 8.6: 404,\n", " 15.81: 404,\n", " 10.59: 400,\n", " 7.29: 397,\n", " 22.7: 389,\n", " 9.63: 384,\n", " 23.76: 380,\n", " 20.8: 379,\n", " 27.34: 377,\n", " 27.79: 360,\n", " 12.53: 356,\n", " 24.89: 352,\n", " 27.31: 350,\n", " 5.99: 347,\n", " 7.14: 342,\n", " 11.11: 332,\n", " 23.83: 327,\n", " 23.5: 326,\n", " 13.61: 313,\n", " 28.18: 311,\n", " 6.91: 310,\n", " 6.54: 307,\n", " 15.27: 307,\n", " 8.94: 299,\n", " 15.96: 297,\n", " 7.66: 292,\n", " 17.58: 288,\n", " 28.99: 284,\n", " 13.23: 277,\n", " 11.12: 272,\n", " 11.36: 258,\n", " 10.36: 256,\n", " 6.17: 252,\n", " 10.0: 251,\n", " 12.68: 251,\n", " 14.96: 249,\n", " 28.88: 247,\n", " 11.83: 243,\n", " 10.62: 241,\n", " 15.95: 240,\n", " 16.32: 233,\n", " 15.65: 232,\n", " 10.25: 228,\n", " 10.38: 227,\n", " 28.14: 224,\n", " 27.88: 222,\n", " 25.11: 220,\n", " 15.21: 220,\n", " 15.23: 216,\n", " 12.18: 216,\n", " 18.64: 214,\n", " 16.89: 213,\n", " 24.7: 206,\n", " 12.87: 206,\n", " 15.58: 201,\n", " 8.0: 198,\n", " 9.62: 195,\n", " 28.34: 195,\n", " 14.79: 194,\n", " 16.45: 193,\n", " 25.44: 192,\n", " 17.49: 190,\n", " 9.32: 187,\n", " 9.88: 186,\n", " 12.21: 183,\n", " 29.67: 178,\n", " 13.48: 175,\n", " 14.91: 172,\n", " 15.62: 171,\n", " 25.28: 171,\n", " 24.24: 170,\n", " 8.88: 168,\n", " 6.76: 168,\n", " 11.89: 165,\n", " 28.49: 165,\n", " 16.82: 162,\n", " 13.22: 159,\n", " 13.43: 157,\n", " 18.39: 155,\n", " 16.4: 154,\n", " 7.74: 154,\n", " 13.57: 153,\n", " 12.84: 152,\n", " 13.85: 152,\n", " 25.09: 150,\n", " 29.96: 149,\n", " 25.65: 149,\n", " 15.28: 147,\n", " 13.47: 146,\n", " 19.91: 144,\n", " 13.16: 143,\n", " 12.23: 143,\n", " 13.92: 142,\n", " 28.67: 140,\n", " 14.26: 138,\n", " 13.8: 138,\n", " 14.35: 138,\n", " 15.33: 135,\n", " 20.3: 135,\n", " 26.14: 134,\n", " 14.22: 132,\n", " 14.59: 132,\n", " 17.19: 131,\n", " 14.84: 131,\n", " 16.0: 127,\n", " 13.79: 125,\n", " 14.72: 125,\n", " 13.72: 125,\n", " 14.17: 122,\n", " 19.29: 120,\n", " 14.61: 114,\n", " 14.11: 113,\n", " 24.83: 112,\n", " 19.69: 108,\n", " 11.58: 107,\n", " 18.79: 106,\n", " 14.42: 106,\n", " 14.83: 105,\n", " 15.7: 99,\n", " 17.93: 96,\n", " 11.26: 95,\n", " 7.68: 94,\n", " 15.57: 92,\n", " 15.2: 86,\n", " 14.74: 84,\n", " 22.78: 84,\n", " 10.95: 82,\n", " 20.25: 77,\n", " 16.07: 76,\n", " 14.54: 75,\n", " 21.28: 74,\n", " 7.4: 72,\n", " 18.3: 68,\n", " 17.88: 66,\n", " 17.51: 65,\n", " 16.69: 65,\n", " 11.03: 64,\n", " 20.62: 59,\n", " 10.71: 58,\n", " 10.39: 57,\n", " 11.34: 56,\n", " 13.24: 56,\n", " 15.37: 52,\n", " 18.67: 51,\n", " 11.97: 51,\n", " 13.55: 50,\n", " 15.68: 50,\n", " 11.66: 50,\n", " 16.35: 49,\n", " 8.32: 49,\n", " 18.62: 49,\n", " 22.06: 48,\n", " 9.45: 47,\n", " 23.33: 47,\n", " 23.26: 46,\n", " 21.36: 45,\n", " 8.63: 44,\n", " 12.92: 44,\n", " 27.99: 44,\n", " 16.7: 43,\n", " 14.82: 43,\n", " 19.04: 43,\n", " 14.5: 41,\n", " 13.87: 40,\n", " 14.18: 39,\n", " 17.06: 36,\n", " 24.2: 35,\n", " 14.38: 35,\n", " 19.36: 34,\n", " 7.43: 33,\n", " 20.11: 33,\n", " 7.37: 32,\n", " 10.28: 32,\n", " 19.41: 32,\n", " 17.43: 32,\n", " 17.04: 32,\n", " 21.74: 31,\n", " 22.11: 31,\n", " 10.83: 31,\n", " 19.74: 30,\n", " 9.96: 30,\n", " 18.17: 30,\n", " 9.64: 29,\n", " 11.78: 28,\n", " 11.46: 27,\n", " 7.75: 27,\n", " 8.07: 26,\n", " 17.8: 26,\n", " 12.8: 25,\n", " 17.74: 25,\n", " 19.79: 25,\n", " 13.75: 24,\n", " 15.45: 24,\n", " 9.33: 24,\n", " 16.95: 24,\n", " 16.63: 23,\n", " 9.38: 23,\n", " 13.04: 23,\n", " 13.12: 23,\n", " 7.05: 23,\n", " 15.13: 22,\n", " 17.39: 22,\n", " 10.51: 22,\n", " 12.41: 22,\n", " 20.48: 22,\n", " 18.43: 21,\n", " 11.54: 21,\n", " 24.33: 21,\n", " 12.09: 20,\n", " 20.53: 20,\n", " 20.16: 20,\n", " 11.28: 19,\n", " 20.9: 18,\n", " 12.72: 18,\n", " 10.2: 18,\n", " 15.01: 17,\n", " 17.26: 17,\n", " 11.41: 17,\n", " 9.01: 17,\n", " 12.17: 17,\n", " 13.3: 17,\n", " 19.13: 17,\n", " 8.7: 17,\n", " 18.61: 16,\n", " 21.27: 16,\n", " 20.85: 16,\n", " 15.76: 16,\n", " 11.91: 16,\n", " 22.48: 16,\n", " 12.22: 16,\n", " 10.96: 15,\n", " 9.2: 15,\n", " 16.08: 15,\n", " 9.07: 15,\n", " 23.91: 14,\n", " 18.29: 13,\n", " 20.03: 13,\n", " 17.34: 13,\n", " 14.7: 13,\n", " 11.72: 12,\n", " 10.46: 12,\n", " 11.59: 12,\n", " 21.64: 12,\n", " 13.36: 12,\n", " 20.77: 12,\n", " 18.91: 12,\n", " 12.36: 11,\n", " 13.17: 11,\n", " 23.52: 11,\n", " 10.14: 11,\n", " 12.04: 11,\n", " 17.54: 11,\n", " 12.86: 11,\n", " 9.83: 11,\n", " 19.66: 11,\n", " 18.09: 11,\n", " 14.25: 10,\n", " 13.93: 10,\n", " 9.51: 10,\n", " 16.28: 10,\n", " 14.12: 10,\n", " 9.7: 9,\n", " 23.22: 9,\n", " 18.21: 9,\n", " 12.54: 9,\n", " 13.62: 9,\n", " 18.07: 9,\n", " 15.07: 9,\n", " 14.75: 9,\n", " 22.85: 8,\n", " 21.59: 8,\n", " 11.63: 8,\n", " 11.09: 8,\n", " 17.66: 8,\n", " 10.01: 8,\n", " 17.22: 8,\n", " 17.03: 8,\n", " 14.93: 8,\n", " 19.82: 8,\n", " 21.22: 7,\n", " 16.11: 7,\n", " 18.53: 7,\n", " 21.21: 7,\n", " 7.42: 7,\n", " 15.83: 7,\n", " 15.25: 7,\n", " 18.78: 7,\n", " 14.88: 7,\n", " 21.14: 7,\n", " 16.71: 7,\n", " 18.04: 6,\n", " 20.17: 6,\n", " 24.76: 6,\n", " 24.52: 6,\n", " 17.9: 6,\n", " 20.86: 6,\n", " 15.38: 6,\n", " 15.51: 5,\n", " 18.36: 5,\n", " 16.15: 5,\n", " 19.16: 5,\n", " 12.67: 5,\n", " 20.52: 4,\n", " 14.62: 4,\n", " 16.65: 4,\n", " 16.96: 4,\n", " 17.15: 4,\n", " 14.43: 4,\n", " 19.39: 4,\n", " 23.59: 4,\n", " 20.4: 4,\n", " 14.67: 3,\n", " 14.57: 3,\n", " 18.86: 3,\n", " 20.69: 3,\n", " 21.82: 2,\n", " 14.77: 2,\n", " 13.84: 2,\n", " 17.59: 2,\n", " 22.94: 2,\n", " 16.33: 2,\n", " 17.91: 2,\n", " 17.28: 2,\n", " 15.29: 2,\n", " 17.72: 1,\n", " 17.41: 1,\n", " 24.4: 1,\n", " 16.9: 1,\n", " 18.72: 1,\n", " 13.19: 1,\n", " 16.83: 1,\n", " 17.78: 1,\n", " 22.64: 1,\n", " 11.16: 1,\n", " 17.5: 1,\n", " 24.59: 1,\n", " 17.46: 1,\n", " 17.44: 1,\n", " 14.28: 1}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"int_rate\"].value_counts())"]}, {"cell_type": "markdown", "id": "37a5531f", "metadata": {}, "source": ["#### Intallement (monthly payment owed by the borrower)"]}, {"cell_type": "code", "execution_count": 35, "id": "a2b9a971", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([  84.92,  777.23,  180.69, ..., 1256.  , 1087.34, 1158.68])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"installment\"].unique()"]}, {"cell_type": "code", "execution_count": 36, "id": "954a4864", "metadata": {}, "outputs": [{"data": {"text/plain": ["{301.15: 4420,\n", " 332.1: 4153,\n", " 361.38: 3704,\n", " 327.34: 3353,\n", " 602.3: 3095,\n", " 451.73: 3076,\n", " 329.72: 2614,\n", " 166.05: 2508,\n", " 498.15: 2410,\n", " 180.69: 2364,\n", " 318.79: 2315,\n", " 240.92: 2303,\n", " 664.2: 2243,\n", " 398.52: 2226,\n", " 392.81: 2200,\n", " 491.01: 2194,\n", " 312.86: 2193,\n", " 150.58: 2126,\n", " 265.68: 2126,\n", " 199.26: 2100,\n", " 320.05: 2071,\n", " 752.87: 2000,\n", " 336.9: 1983,\n", " 324.65: 1981,\n", " 163.67: 1956,\n", " 341.73: 1951,\n", " 654.68: 1948,\n", " 326.97: 1901,\n", " 261.88: 1870,\n", " 164.86: 1869,\n", " 196.41: 1856,\n", " 309.74: 1822,\n", " 313.23: 1785,\n", " 335.12: 1780,\n", " 322.35: 1780,\n", " 197.83: 1728,\n", " 375.43: 1716,\n", " 481.84: 1709,\n", " 382.55: 1680,\n", " 263.78: 1679,\n", " 324.98: 1677,\n", " 339.31: 1658,\n", " 469.29: 1593,\n", " 478.19: 1570,\n", " 339.79: 1551,\n", " 722.76: 1521,\n", " 625.72: 1509,\n", " 395.66: 1506,\n", " 279.16: 1495,\n", " 304.72: 1480,\n", " 494.57: 1472,\n", " 322.63: 1463,\n", " 349.65: 1458,\n", " 314.2: 1432,\n", " 465.27: 1410,\n", " 329.48: 1402,\n", " 310.38: 1389,\n", " 542.07: 1383,\n", " 335.69: 1333,\n", " 659.43: 1324,\n", " 279.72: 1305,\n", " 315.5: 1301,\n", " 329.91: 1299,\n", " 273.39: 1295,\n", " 159.4: 1292,\n", " 315.17: 1292,\n", " 314.48: 1291,\n", " 637.58: 1286,\n", " 330.0: 1277,\n", " 271.45: 1275,\n", " 843.22: 1268,\n", " 163.49: 1236,\n", " 351.67: 1221,\n", " 170.87: 1216,\n", " 250.29: 1215,\n", " 339.65: 1213,\n", " 333.53: 1207,\n", " 342.17: 1206,\n", " 683.46: 1199,\n", " 314.25: 1195,\n", " 255.04: 1188,\n", " 444.79: 1181,\n", " 410.08: 1177,\n", " 156.43: 1176,\n", " 210.81: 1170,\n", " 191.28: 1165,\n", " 269.52: 1161,\n", " 205.04: 1161,\n", " 387.15: 1152,\n", " 168.45: 1144,\n", " 512.6: 1140,\n", " 335.45: 1138,\n", " 162.49: 1133,\n", " 187.72: 1131,\n", " 346.61: 1128,\n", " 348.95: 1127,\n", " 346.9: 1124,\n", " 312.63: 1119,\n", " 1218.88: 1115,\n", " 487.47: 1110,\n", " 344.07: 1103,\n", " 162.33: 1103,\n", " 160.03: 1100,\n", " 483.94: 1093,\n", " 558.32: 1075,\n", " 404.27: 1075,\n", " 1162.34: 1073,\n", " 308.73: 1072,\n", " 377.04: 1070,\n", " 880.61: 1069,\n", " 169.66: 1068,\n", " 167.56: 1068,\n", " 161.18: 1067,\n", " 673.79: 1060,\n", " 259.99: 1054,\n", " 346.76: 1047,\n", " 301.11: 1043,\n", " 194.99: 1043,\n", " 232.47: 1039,\n", " 169.9: 1035,\n", " 356.48: 1029,\n", " 505.34: 1027,\n", " 306.45: 1026,\n", " 389.98: 1016,\n", " 229.14: 1013,\n", " 372.21: 1010,\n", " 464.6: 1007,\n", " 289.34: 1003,\n", " 329.62: 992,\n", " 384.06: 990,\n", " 326.07: 989,\n", " 476.33: 984,\n", " 1196.05: 981,\n", " 196.18: 977,\n", " 202.14: 975,\n", " 782.15: 974,\n", " 619.47: 973,\n", " 482.23: 972,\n", " 486.98: 971,\n", " 327.68: 971,\n", " 471.3: 966,\n", " 271.04: 965,\n", " 232.64: 964,\n", " 311.02: 961,\n", " 352.27: 957,\n", " 466.2: 957,\n", " 1095.0: 955,\n", " 678.61: 954,\n", " 490.45: 944,\n", " 194.79: 943,\n", " 1204.6: 941,\n", " 261.57: 940,\n", " 313.32: 937,\n", " 375.88: 932,\n", " 329.24: 927,\n", " 389.58: 927,\n", " 531.36: 924,\n", " 869.66: 922,\n", " 407.17: 920,\n", " 480.08: 919,\n", " 192.03: 915,\n", " 307.27: 914,\n", " 338.54: 912,\n", " 356.08: 912,\n", " 256.04: 911,\n", " 318.75: 911,\n", " 266.88: 909,\n", " 640.1: 909,\n", " 348.18: 906,\n", " 326.21: 901,\n", " 486.58: 900,\n", " 653.93: 899,\n", " 1247.68: 898,\n", " 259.72: 894,\n", " 649.96: 893,\n", " 377.37: 893,\n", " 483.52: 892,\n", " 814.21: 889,\n", " 285.42: 887,\n", " 164.74: 887,\n", " 324.89: 884,\n", " 386.82: 881,\n", " 628.39: 879,\n", " 378.2: 878,\n", " 649.3: 877,\n", " 154.87: 876,\n", " 99.63: 876,\n", " 323.05: 872,\n", " 203.59: 870,\n", " 258.1: 868,\n", " 167.85: 867,\n", " 371.68: 866,\n", " 626.46: 865,\n", " 699.3: 865,\n", " 337.47: 865,\n", " 263.93: 863,\n", " 697.9: 862,\n", " 233.1: 861,\n", " 187.94: 860,\n", " 645.25: 858,\n", " 197.69: 858,\n", " 421.61: 858,\n", " 392.36: 857,\n", " 589.22: 857,\n", " 132.84: 857,\n", " 193.41: 855,\n", " 317.54: 854,\n", " 328.06: 853,\n", " 469.84: 853,\n", " 268.09: 853,\n", " 257.88: 852,\n", " 333.6: 852,\n", " 304.36: 847,\n", " 298.17: 846,\n", " 156.62: 846,\n", " 201.07: 845,\n", " 166.77: 840,\n", " 659.81: 840,\n", " 630.34: 838,\n", " 523.75: 837,\n", " 395.89: 836,\n", " 472.75: 835,\n", " 320.29: 835,\n", " 309.97: 834,\n", " 434.75: 833,\n", " 175.84: 833,\n", " 161.32: 827,\n", " 332.72: 825,\n", " 494.86: 822,\n", " 157.24: 819,\n", " 361.67: 818,\n", " 609.44: 817,\n", " 250.59: 809,\n", " 285.8: 809,\n", " 188.69: 808,\n", " 488.53: 805,\n", " 459.67: 804,\n", " 268.56: 801,\n", " 367.74: 799,\n", " 173.45: 798,\n", " 612.89: 798,\n", " 266.83: 795,\n", " 185.84: 794,\n", " 533.75: 792,\n", " 263.58: 791,\n", " 335.65: 790,\n", " 251.58: 787,\n", " 171.09: 786,\n", " 251.36: 786,\n", " 340.18: 785,\n", " 372.71: 783,\n", " 321.5: 782,\n", " 197.95: 778,\n", " 203.88: 778,\n", " 325.69: 777,\n", " 193.58: 776,\n", " 334.49: 775,\n", " 465.57: 774,\n", " 238.17: 772,\n", " 307.5: 772,\n", " 496.95: 771,\n", " 271.84: 771,\n", " 457.08: 771,\n", " 230.8: 770,\n", " 494.22: 768,\n", " 876.0: 768,\n", " 155.19: 767,\n", " 778.38: 766,\n", " 277.29: 766,\n", " 157.1: 766,\n", " 644.69: 764,\n", " 608.72: 764,\n", " 298.89: 763,\n", " 996.29: 758,\n", " 268.36: 756,\n", " 559.44: 755,\n", " 893.54: 752,\n", " 597.78: 748,\n", " 164.96: 747,\n", " 670.23: 746,\n", " 475.7: 745,\n", " 201.42: 743,\n", " 903.45: 743,\n", " 1115.77: 742,\n", " 200.12: 742,\n", " 285.19: 741,\n", " 167.73: 740,\n", " 667.06: 740,\n", " 543.44: 740,\n", " 365.23: 738,\n", " 247.79: 738,\n", " 309.1: 736,\n", " 451.19: 735,\n", " 830.24: 734,\n", " 456.54: 733,\n", " 635.07: 733,\n", " 785.62: 731,\n", " 395.37: 728,\n", " 927.1: 728,\n", " 164.81: 728,\n", " 357.25: 726,\n", " 843.9: 725,\n", " 500.3: 724,\n", " 364.94: 723,\n", " 750.86: 722,\n", " 310.56: 722,\n", " 157.59: 722,\n", " 201.27: 717,\n", " 400.24: 714,\n", " 320.01: 714,\n", " 355.84: 711,\n", " 243.29: 711,\n", " 188.52: 710,\n", " 402.54: 710,\n", " 281.34: 709,\n", " 373.22: 706,\n", " 419.58: 706,\n", " 471.37: 706,\n", " 294.61: 705,\n", " 679.58: 703,\n", " 620.75: 703,\n", " 542.89: 702,\n", " 500.58: 698,\n", " 1204.42: 697,\n", " 353.01: 696,\n", " 372.45: 692,\n", " 98.92: 691,\n", " 131.89: 691,\n", " 178.24: 691,\n", " 381.04: 690,\n", " 222.4: 689,\n", " 351.53: 689,\n", " 305.31: 688,\n", " 519.91: 686,\n", " 581.58: 685,\n", " 136.7: 685,\n", " 370.48: 684,\n", " 157.13: 684,\n", " 186.23: 683,\n", " 291.95: 682,\n", " 399.26: 682,\n", " 381.06: 681,\n", " 291.76: 681,\n", " 473.24: 680,\n", " 344.17: 675,\n", " 335.93: 675,\n", " 503.54: 674,\n", " 790.82: 671,\n", " 630.99: 671,\n", " 582.75: 667,\n", " 563.98: 666,\n", " 658.95: 666,\n", " 625.81: 665,\n", " 471.71: 665,\n", " 463.09: 662,\n", " 402.83: 662,\n", " 366.37: 662,\n", " 1154.0: 661,\n", " 331.27: 661,\n", " 356.78: 659,\n", " 273.74: 658,\n", " 312.91: 657,\n", " 797.03: 657,\n", " 418.74: 656,\n", " 693.21: 655,\n", " 508.96: 654,\n", " 252.14: 653,\n", " 377.41: 646,\n", " 164.62: 645,\n", " 163.11: 645,\n", " 502.67: 643,\n", " 907.73: 643,\n", " 478.12: 639,\n", " 464.95: 638,\n", " 313.18: 637,\n", " 219.0: 636,\n", " 595.41: 635,\n", " 239.21: 634,\n", " 308.64: 634,\n", " 660.0: 631,\n", " 304.59: 630,\n", " 207.97: 630,\n", " 476.3: 630,\n", " 466.53: 629,\n", " 509.69: 629,\n", " 248.3: 628,\n", " 326.92: 628,\n", " 527.51: 627,\n", " 169.83: 625,\n", " 667.19: 625,\n", " 211.01: 624,\n", " 189.1: 623,\n", " 617.46: 623,\n", " 289.11: 621,\n", " 216.83: 621,\n", " 402.14: 620,\n", " 1129.19: 619,\n", " 1179.12: 619,\n", " 1145.69: 618,\n", " 848.51: 617,\n", " 306.31: 617,\n", " 534.72: 617,\n", " 372.96: 617,\n", " 304.54: 616,\n", " 205.3: 616,\n", " 344.95: 615,\n", " 1054.02: 615,\n", " 307.45: 615,\n", " 419.46: 614,\n", " 461.13: 614,\n", " 361.52: 614,\n", " 760.82: 613,\n", " 154.37: 612,\n", " 486.26: 611,\n", " 460.82: 611,\n", " 671.38: 611,\n", " 197.55: 610,\n", " 361.83: 610,\n", " 276.06: 609,\n", " 602.21: 608,\n", " 527.55: 608,\n", " 260.85: 608,\n", " 278.19: 608,\n", " 225.6: 607,\n", " 296.75: 607,\n", " 371.96: 607,\n", " 98.21: 606,\n", " 277.52: 605,\n", " 223.16: 605,\n", " 317.96: 602,\n", " 248.48: 602,\n", " 185.98: 601,\n", " 332.05: 600,\n", " 469.36: 600,\n", " 169.27: 600,\n", " 832.47: 600,\n", " 886.11: 599,\n", " 343.39: 599,\n", " 330.57: 599,\n", " 625.26: 598,\n", " 102.52: 598,\n", " 173.31: 597,\n", " 308.27: 597,\n", " 495.0: 596,\n", " 384.34: 596,\n", " 241.12: 596,\n", " 120.46: 595,\n", " 208.14: 595,\n", " 391.5: 593,\n", " 235.83: 592,\n", " 407.75: 592,\n", " 494.43: 592,\n", " 243.49: 592,\n", " 197.78: 591,\n", " 684.33: 589,\n", " 375.99: 588,\n", " 454.96: 588,\n", " 389.26: 588,\n", " 263.39: 588,\n", " 382.5: 587,\n", " 637.49: 587,\n", " 563.15: 587,\n", " 461.96: 586,\n", " 766.12: 586,\n", " 338.39: 584,\n", " 610.62: 584,\n", " 628.95: 584,\n", " 451.66: 583,\n", " 361.48: 583,\n", " 173.38: 581,\n", " 670.9: 580,\n", " 503.18: 579,\n", " 368.45: 579,\n", " 310.33: 578,\n", " 160.75: 577,\n", " 470.47: 577,\n", " 311.23: 576,\n", " 188.0: 576,\n", " 159.38: 576,\n", " 270.71: 576,\n", " 1282.79: 575,\n", " 176.14: 575,\n", " 457.97: 574,\n", " 246.99: 574,\n", " 464.94: 574,\n", " 476.01: 573,\n", " 468.94: 573,\n", " 415.93: 572,\n", " 703.34: 572,\n", " 858.18: 572,\n", " 422.01: 572,\n", " 503.21: 571,\n", " 277.18: 571,\n", " 651.37: 571,\n", " 165.0: 570,\n", " 510.07: 570,\n", " 308.0: 569,\n", " 237.85: 568,\n", " 375.49: 568,\n", " 850.95: 567,\n", " 365.67: 566,\n", " 203.12: 566,\n", " 765.1: 565,\n", " 301.93: 565,\n", " 238.01: 563,\n", " 622.04: 562,\n", " 156.66: 562,\n", " 643.0: 561,\n", " 489.45: 561,\n", " 342.22: 561,\n", " 263.7: 561,\n", " 583.89: 558,\n", " 489.31: 558,\n", " 482.25: 558,\n", " 209.73: 558,\n", " 1223.77: 557,\n", " 396.0: 557,\n", " 833.57: 556,\n", " 317.86: 555,\n", " 391.45: 554,\n", " 851.51: 554,\n", " 573.83: 554,\n", " 493.86: 554,\n", " 480.43: 552,\n", " 345.62: 551,\n", " 172.04: 551,\n", " 838.91: 550,\n", " 185.24: 550,\n", " 487.86: 550,\n", " 275.07: 549,\n", " 389.03: 549,\n", " 424.26: 549,\n", " 282.28: 548,\n", " 257.2: 547,\n", " 321.13: 546,\n", " 391.28: 546,\n", " 546.77: 545,\n", " 213.89: 545,\n", " 652.41: 545,\n", " 1238.93: 545,\n", " 395.55: 545,\n", " 155.51: 544,\n", " 385.8: 544,\n", " 157.75: 543,\n", " 594.62: 543,\n", " 270.83: 543,\n", " 227.26: 543,\n", " 342.12: 542,\n", " 659.24: 541,\n", " 256.23: 540,\n", " 255.0: 540,\n", " 260.97: 539,\n", " 366.26: 539,\n", " 366.51: 538,\n", " 130.94: 538,\n", " 469.98: 537,\n", " 458.45: 536,\n", " 324.61: 536,\n", " 278.54: 536,\n", " 230.57: 535,\n", " 555.99: 534,\n", " 250.66: 534,\n", " 378.59: 534,\n", " 311.62: 533,\n", " 178.04: 533,\n", " 186.61: 533,\n", " 470.26: 532,\n", " 796.18: 532,\n", " 379.76: 532,\n", " 447.83: 531,\n", " 199.63: 531,\n", " 174.83: 530,\n", " 458.28: 530,\n", " 460.9: 529,\n", " 341.68: 529,\n", " 341.22: 529,\n", " 170.09: 529,\n", " 481.69: 529,\n", " 693.8: 528,\n", " 679.29: 527,\n", " 248.82: 527,\n", " 507.8: 526,\n", " 783.7: 525,\n", " 802.29: 523,\n", " 323.38: 523,\n", " 451.9: 522,\n", " 167.97: 522,\n", " 366.72: 522,\n", " 249.08: 521,\n", " 1213.12: 521,\n", " 406.24: 520,\n", " 427.78: 520,\n", " 335.07: 517,\n", " 347.8: 517,\n", " 946.68: 517,\n", " 854.32: 517,\n", " 364.7: 516,\n", " 774.34: 516,\n", " 521.7: 516,\n", " 275.96: 515,\n", " 462.41: 515,\n", " 556.37: 515,\n", " 571.59: 514,\n", " 578.68: 513,\n", " 619.93: 512,\n", " 224.04: 512,\n", " 952.65: 511,\n", " 570.84: 511,\n", " 385.79: 511,\n", " 498.02: 511,\n", " 338.63: 510,\n", " 429.09: 510,\n", " 712.96: 510,\n", " 272.15: 508,\n", " 160.15: 508,\n", " 268.7: 507,\n", " 276.68: 506,\n", " 368.08: 506,\n", " 596.34: 506,\n", " 318.82: 506,\n", " 404.97: 505,\n", " 1265.16: 505,\n", " 195.73: 505,\n", " 677.07: 503,\n", " 128.02: 502,\n", " 269.92: 500,\n", " 465.84: 500,\n", " 281.58: 500,\n", " 389.01: 500,\n", " 513.25: 499,\n", " 272.98: 499,\n", " 688.13: 499,\n", " 230.41: 498,\n", " 227.49: 497,\n", " 397.56: 497,\n", " 235.13: 497,\n", " 360.95: 496,\n", " 235.69: 495,\n", " 228.88: 494,\n", " 623.23: 494,\n", " 283.28: 493,\n", " 192.9: 493,\n", " 217.38: 492,\n", " 317.12: 492,\n", " 260.55: 492,\n", " 973.15: 492,\n", " 518.71: 491,\n", " 343.84: 490,\n", " 460.1: 490,\n", " 676.78: 490,\n", " 234.99: 490,\n", " 380.56: 490,\n", " 90.35: 488,\n", " 268.75: 488,\n", " 529.77: 488,\n", " 989.14: 487,\n", " 264.89: 487,\n", " 410.6: 486,\n", " 286.92: 485,\n", " 322.3: 485,\n", " 192.17: 485,\n", " 472.14: 485,\n", " 616.54: 485,\n", " 153.23: 485,\n", " 693.51: 484,\n", " 614.53: 483,\n", " 680.36: 483,\n", " 375.16: 482,\n", " 417.81: 481,\n", " 377.09: 480,\n", " 135.73: 480,\n", " 275.33: 480,\n", " 1184.86: 480,\n", " 889.58: 479,\n", " 354.3: 477,\n", " 496.09: 476,\n", " 174.09: 476,\n", " 259.36: 475,\n", " 412.06: 475,\n", " 208.91: 473,\n", " 400.31: 473,\n", " 982.02: 472,\n", " 369.93: 472,\n", " 209.79: 472,\n", " 345.85: 472,\n", " 652.13: 471,\n", " 243.13: 470,\n", " 397.33: 470,\n", " 520.35: 470,\n", " 271.14: 470,\n", " 501.74: 469,\n", " 395.09: 469,\n", " 66.42: 469,\n", " 621.12: 467,\n", " 264.94: 467,\n", " 730.46: 466,\n", " 371.23: 466,\n", " 130.79: 466,\n", " 357.88: 465,\n", " 554.35: 465,\n", " 156.32: 464,\n", " 1280.2: 464,\n", " 1137.43: 464,\n", " 761.8: 462,\n", " 352.85: 462,\n", " 649.77: 461,\n", " 687.42: 461,\n", " 123.9: 460,\n", " 640.57: 460,\n", " 539.03: 460,\n", " 321.82: 459,\n", " 282.41: 459,\n", " 1218.61: 459,\n", " 245.16: 459,\n", " 521.03: 458,\n", " 1241.5: 457,\n", " 164.03: 457,\n", " 729.87: 456,\n", " 267.6: 456,\n", " 510.27: 455,\n", " 158.98: 455,\n", " 393.67: 455,\n", " 434.01: 455,\n", " 128.94: 455,\n", " 167.25: 454,\n", " 479.06: 454,\n", " 239.11: 453,\n", " 237.52: 453,\n", " 369.0: 453,\n", " 576.41: 453,\n", " 346.47: 453,\n", " 1099.69: 452,\n", " 628.49: 451,\n", " 593.49: 451,\n", " 172.09: 450,\n", " 129.86: 450,\n", " 632.42: 450,\n", " 327.63: 450,\n", " 368.72: 450,\n", " 520.13: 449,\n", " 339.74: 449,\n", " 275.26: 449,\n", " 1174.91: 448,\n", " 183.87: 447,\n", " 278.97: 447,\n", " 902.37: 447,\n", " 225.65: 446,\n", " 745.42: 446,\n", " 380.66: 445,\n", " 253.18: 445,\n", " 264.0: 445,\n", " 253.78: 445,\n", " 389.36: 445,\n", " 487.33: 445,\n", " 467.43: 445,\n", " 101.8: 444,\n", " 484.58: 444,\n", " 546.15: 444,\n", " 509.47: 444,\n", " 842.23: 443,\n", " 771.82: 443,\n", " 510.6: 442,\n", " 361.33: 442,\n", " 416.28: 441,\n", " 501.17: 441,\n", " 408.22: 441,\n", " 97.5: 441,\n", " 162.45: 440,\n", " 251.61: 440,\n", " 583.51: 440,\n", " 519.15: 440,\n", " 1252.91: 440,\n", " 486.97: 439,\n", " 155.28: 439,\n", " 1172.9: 439,\n", " 1230.85: 438,\n", " 608.22: 438,\n", " 1084.07: 438,\n", " 307.04: 437,\n", " 762.08: 437,\n", " 332.77: 436,\n", " 818.35: 436,\n", " 313.74: 435,\n", " 858.05: 435,\n", " 259.58: 435,\n", " 531.06: 435,\n", " 250.11: 435,\n", " 243.93: 435,\n", " 495.58: 434,\n", " 360.08: 434,\n", " 714.49: 434,\n", " 189.3: 434,\n", " 1190.62: 434,\n", " 519.97: 434,\n", " 1066.52: 433,\n", " 258.71: 433,\n", " 134.05: 433,\n", " 204.11: 433,\n", " 552.12: 432,\n", " 614.08: 432,\n", " 252.4: 432,\n", " 545.96: 432,\n", " 152.36: 431,\n", " 626.64: 431,\n", " 180.92: 431,\n", " 373.94: 431,\n", " 105.51: 430,\n", " 507.55: 430,\n", " 257.46: 430,\n", " 655.35: 430,\n", " 264.5: 430,\n", " 524.48: 429,\n", " 763.28: 429,\n", " 938.57: 429,\n", " 541.42: 429,\n", " 306.36: 429,\n", " 198.0: 428,\n", " 271.72: 428,\n", " 154.99: 428,\n", " 1187.57: 428,\n", " 382.95: 427,\n", " 191.25: 427,\n", " 668.98: 427,\n", " 219.26: 426,\n", " 506.35: 426,\n", " 629.01: 425,\n", " 335.87: 424,\n", " 376.37: 424,\n", " 182.62: 424,\n", " 643.63: 424,\n", " 1300.55: 424,\n", " 249.01: 424,\n", " 206.5: 423,\n", " 880.71: 422,\n", " 328.86: 421,\n", " 259.08: 421,\n", " 791.32: 421,\n", " 554.57: 421,\n", " 252.97: 420,\n", " 98.09: 420,\n", " 159.41: 420,\n", " 363.97: 420,\n", " 134.76: 419,\n", " 368.9: 419,\n", " 214.55: 419,\n", " 225.84: 419,\n", " 499.08: 418,\n", " 527.29: 418,\n", " 303.81: 418,\n", " 512.08: 418,\n", " 372.67: 418,\n", " 130.0: 418,\n", " 534.12: 417,\n", " 309.87: 417,\n", " 665.44: 417,\n", " 163.84: 416,\n", " 311.94: 416,\n", " 176.51: 416,\n", " 963.68: 416,\n", " 506.21: 416,\n", " 1269.73: 416,\n", " 508.53: 416,\n", " 206.44: 416,\n", " 618.19: 415,\n", " 206.97: 414,\n", " 539.84: 414,\n", " 247.97: 414,\n", " 523.14: 414,\n", " 230.64: 413,\n", " 201.56: 413,\n", " 452.41: 413,\n", " 245.51: 413,\n", " 187.58: 413,\n", " 956.37: 413,\n", " 138.65: 413,\n", " 337.23: 413,\n", " 225.87: 412,\n", " 318.19: 412,\n", " 401.39: 412,\n", " 774.91: 412,\n", " 97.4: 411,\n", " 564.56: 411,\n", " 485.07: 411,\n", " 416.82: 411,\n", " 424.85: 411,\n", " 413.94: 411,\n", " 333.82: 410,\n", " 338.93: 410,\n", " 305.09: 410,\n", " 361.93: 410,\n", " 216.82: 410,\n", " 323.01: 409,\n", " 135.92: 409,\n", " 1154.66: 409,\n", " 536.36: 409,\n", " 754.07: 409,\n", " 932.39: 408,\n", " 254.27: 408,\n", " 826.24: 408,\n", " 357.01: 408,\n", " 389.86: 407,\n", " 303.21: 407,\n", " 316.53: 407,\n", " 307.56: 407,\n", " 602.79: 406,\n", " 320.71: 406,\n", " 373.52: 406,\n", " 161.53: 406,\n", " 243.78: 406,\n", " 346.85: 406,\n", " 152.18: 406,\n", " 856.54: 405,\n", " 323.43: 405,\n", " 452.31: 405,\n", " 791.71: 405,\n", " 750.97: 404,\n", " 576.02: 404,\n", " 289.21: 404,\n", " 646.1: 404,\n", " 308.87: 403,\n", " 447.25: 403,\n", " 396.69: 403,\n", " 356.98: 402,\n", " 658.48: 402,\n", " 345.08: 402,\n", " 250.33: 402,\n", " 96.02: 402,\n", " 608.65: 402,\n", " 778.06: 402,\n", " 529.88: 401,\n", " 281.22: 401,\n", " 438.0: 401,\n", " 381.4: 400,\n", " 172.48: 400,\n", " 308.41: 400,\n", " 1204.57: 400,\n", " 371.64: 400,\n", " 125.3: 400,\n", " 237.86: 399,\n", " 697.41: 399,\n", " 691.23: 399,\n", " 310.88: 398,\n", " 68.35: 398,\n", " 175.77: 398,\n", " 200.7: 398,\n", " 293.21: 398,\n", " 516.1: 395,\n", " 373.63: 395,\n", " 461.24: 395,\n", " 824.29: 395,\n", " 783.07: 395,\n", " 482.02: 394,\n", " 381.55: 393,\n", " 190.52: 392,\n", " 794.65: 392,\n", " 281.81: 392,\n", " 1257.8: 392,\n", " 248.45: 392,\n", " 754.81: 391,\n", " 1096.29: 391,\n", " 331.19: 391,\n", " 636.38: 391,\n", " 254.37: 391,\n", " 606.41: 390,\n", " 462.0: 390,\n", " 397.41: 390,\n", " 777.55: 390,\n", " 635.72: 389,\n", " 365.31: 389,\n", " 682.44: 389,\n", " 387.66: 389,\n", " 388.06: 389,\n", " 344.02: 389,\n", " 251.4: 389,\n", " 385.35: 389,\n", " 1197.58: 388,\n", " 370.91: 388,\n", " 516.25: 387,\n", " 1235.52: 387,\n", " 396.14: 387,\n", " 515.75: 387,\n", " 805.17: 387,\n", " 273.08: 387,\n", " 492.08: 387,\n", " 100.54: 386,\n", " 550.14: 386,\n", " 921.64: 386,\n", " 287.44: 386,\n", " 180.67: 386,\n", " 485.38: 386,\n", " 1189.26: 386,\n", " 182.84: 385,\n", " 614.99: 385,\n", " 528.4: 385,\n", " 208.06: 384,\n", " 376.19: 384,\n", " 470.15: 384,\n", " 476.93: 384,\n", " 362.78: 384,\n", " 203.79: 383,\n", " ...}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"installment\"].value_counts())"]}, {"cell_type": "markdown", "id": "c5fe274c", "metadata": {}, "source": ["#### Grade"]}, {"cell_type": "code", "execution_count": 43, "id": "9f648193", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['C', 'D', 'B', 'A', 'E', 'F', 'G'], dtype=object)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"grade\"].unique()"]}, {"cell_type": "code", "execution_count": 42, "id": "091d49e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'B': 663557,\n", " 'C': 650053,\n", " 'A': 433027,\n", " 'D': 324424,\n", " 'E': 135639,\n", " 'F': 41800,\n", " 'G': 12168}"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"grade\"].value_counts())"]}, {"cell_type": "markdown", "id": "c5a4c0a3", "metadata": {}, "source": ["#### Sub Grade"]}, {"cell_type": "code", "execution_count": null, "id": "169adb9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['C1', 'D2', 'D1', 'C4', 'C3', 'C2', 'D5', 'B3', 'A4', 'B5', 'C5',\n", "       'D4', 'E1', 'E4', 'B4', 'D3', 'A1', 'E5', 'B2', 'B1', 'A5', 'F5',\n", "       'A3', 'E3', 'A2', 'E2', 'F4', 'G1', 'G2', 'F1', 'F2', 'F3', 'G4',\n", "       'G3', 'G5'], dtype=object)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df[\"sub_grade\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "6380456e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'C1': 145903,\n", " 'B5': 140288,\n", " 'B4': 139793,\n", " 'B3': 131514,\n", " 'C2': 131116,\n", " 'C3': 129193,\n", " 'C4': 127115,\n", " 'B2': 126621,\n", " 'B1': 125341,\n", " 'C5': 116726,\n", " 'A5': 107617,\n", " 'A4': 95874,\n", " 'A1': 86790,\n", " 'D1': 81787,\n", " 'A3': 73184,\n", " 'D2': 72899,\n", " 'A2': 69562,\n", " 'D3': 64819,\n", " 'D4': 56896,\n", " 'D5': 48023,\n", " 'E1': 33573,\n", " 'E2': 29924,\n", " 'E3': 26708,\n", " 'E4': 22763,\n", " 'E5': 22671,\n", " 'F1': 13413,\n", " 'F2': 9305,\n", " 'F3': 7791,\n", " 'F4': 6124,\n", " 'F5': 5167,\n", " 'G1': 4106,\n", " 'G2': 2688,\n", " 'G3': 2094,\n", " 'G4': 1712,\n", " 'G5': 1568}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dict(df[\"sub_grade\"].value_counts())"]}, {"cell_type": "markdown", "id": "00c4a7f9", "metadata": {}, "source": ["#### Annual income"]}, {"cell_type": "code", "execution_count": null, "id": "e09c7bd8", "metadata": {}, "outputs": [], "source": ["df[\"annual_inc\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "9d04a26b", "metadata": {}, "outputs": [], "source": ["dict(df[\"annual_inc\"].value_counts())"]}, {"cell_type": "markdown", "id": "db047ea5", "metadata": {}, "source": ["#### Dti (emplyment length in years)"]}, {"cell_type": "code", "execution_count": 48, "id": "6621164f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 18.24,  26.52,  10.51, ..., 124.36, 196.88,  66.19])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"dti\"].unique()"]}, {"cell_type": "code", "execution_count": 49, "id": "7f5f80e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0.0: 1732,\n", " 18.0: 1584,\n", " 14.4: 1577,\n", " 16.8: 1576,\n", " 19.2: 1566,\n", " 15.6: 1506,\n", " 13.2: 1496,\n", " 12.0: 1486,\n", " 20.4: 1424,\n", " 21.6: 1391,\n", " 10.8: 1335,\n", " 22.8: 1300,\n", " 16.32: 1260,\n", " 24.0: 1257,\n", " 18.72: 1256,\n", " 17.28: 1247,\n", " 14.88: 1235,\n", " 13.92: 1233,\n", " 18.48: 1213,\n", " 16.2: 1205,\n", " 13.68: 1202,\n", " 16.08: 1197,\n", " 14.64: 1196,\n", " 18.6: 1189,\n", " 17.52: 1188,\n", " 16.56: 1181,\n", " 15.84: 1179,\n", " 12.48: 1175,\n", " 17.76: 1169,\n", " 15.12: 1166,\n", " 19.92: 1165,\n", " 19.68: 1159,\n", " 17.4: 1158,\n", " 9.6: 1156,\n", " 20.88: 1155,\n", " 13.44: 1155,\n", " 17.04: 1154,\n", " 14.16: 1147,\n", " 20.0: 1146,\n", " 15.36: 1138,\n", " 12.96: 1124,\n", " 19.44: 1120,\n", " 11.52: 1119,\n", " 12.72: 1118,\n", " 18.96: 1118,\n", " 15.0: 1115,\n", " 18.24: 1110,\n", " 13.8: 1104,\n", " 20.16: 1103,\n", " 11.76: 1094,\n", " 26.4: 1092,\n", " 16.4: 1087,\n", " 19.8: 1080,\n", " 21.12: 1080,\n", " 12.24: 1075,\n", " 8.4: 1075,\n", " 17.94: 1072,\n", " 21.36: 1067,\n", " 15.92: 1062,\n", " 18.8: 1060,\n", " 17.7: 1059,\n", " 18.43: 1057,\n", " 14.33: 1057,\n", " 22.32: 1055,\n", " 11.04: 1053,\n", " 15.4: 1049,\n", " 14.5: 1048,\n", " 19.76: 1047,\n", " 21.0: 1047,\n", " 17.73: 1047,\n", " 17.6: 1046,\n", " 16.78: 1046,\n", " 14.0: 1045,\n", " 16.03: 1044,\n", " 16.77: 1043,\n", " 11.28: 1043,\n", " 15.48: 1042,\n", " 16.0: 1042,\n", " 20.64: 1042,\n", " 16.18: 1041,\n", " 16.53: 1040,\n", " 15.2: 1040,\n", " 14.86: 1040,\n", " 17.56: 1040,\n", " 15.82: 1039,\n", " 15.81: 1038,\n", " 16.74: 1034,\n", " 21.2: 1034,\n", " 16.43: 1033,\n", " 14.35: 1033,\n", " 13.28: 1031,\n", " 12.67: 1029,\n", " 17.23: 1028,\n", " 13.63: 1028,\n", " 17.19: 1028,\n", " 16.5: 1028,\n", " 14.2: 1027,\n", " 15.58: 1027,\n", " 14.92: 1026,\n", " 16.36: 1025,\n", " 13.5: 1025,\n", " 15.16: 1025,\n", " 14.13: 1024,\n", " 14.83: 1024,\n", " 14.38: 1024,\n", " 12.6: 1022,\n", " 16.3: 1022,\n", " 17.18: 1022,\n", " 16.6: 1022,\n", " 16.13: 1022,\n", " 17.71: 1022,\n", " 14.68: 1021,\n", " 17.64: 1021,\n", " 15.63: 1021,\n", " 19.28: 1020,\n", " 15.53: 1020,\n", " 15.07: 1019,\n", " 14.6: 1018,\n", " 25.2: 1017,\n", " 17.1: 1017,\n", " 16.63: 1016,\n", " 15.47: 1016,\n", " 15.24: 1016,\n", " 18.45: 1015,\n", " 19.66: 1015,\n", " 15.3: 1015,\n", " 10.08: 1015,\n", " 18.69: 1014,\n", " 13.48: 1014,\n", " 16.98: 1014,\n", " 15.23: 1013,\n", " 14.93: 1013,\n", " 14.08: 1013,\n", " 12.58: 1012,\n", " 18.28: 1012,\n", " 14.78: 1012,\n", " 16.26: 1012,\n", " 14.74: 1011,\n", " 19.35: 1010,\n", " 15.64: 1010,\n", " 17.08: 1010,\n", " 19.08: 1010,\n", " 17.49: 1009,\n", " 16.05: 1008,\n", " 15.1: 1008,\n", " 16.29: 1008,\n", " 17.25: 1007,\n", " 14.7: 1007,\n", " 16.51: 1006,\n", " 16.88: 1006,\n", " 15.72: 1006,\n", " 17.63: 1006,\n", " 18.93: 1005,\n", " 22.08: 1005,\n", " 17.98: 1005,\n", " 12.93: 1004,\n", " 16.87: 1004,\n", " 13.96: 1004,\n", " 19.73: 1004,\n", " 19.89: 1003,\n", " 19.5: 1003,\n", " 15.9: 1003,\n", " 10.32: 1002,\n", " 16.27: 1002,\n", " 15.75: 1002,\n", " 18.46: 1002,\n", " 15.87: 1002,\n", " 14.57: 1002,\n", " 15.05: 1001,\n", " 15.89: 1001,\n", " 14.96: 1001,\n", " 17.16: 1001,\n", " 17.22: 1000,\n", " 14.25: 1000,\n", " 13.82: 1000,\n", " 16.86: 999,\n", " 15.88: 999,\n", " 16.89: 999,\n", " 17.32: 999,\n", " 13.88: 998,\n", " 12.94: 998,\n", " 15.42: 997,\n", " 21.84: 996,\n", " 17.47: 996,\n", " 18.36: 996,\n", " 15.45: 996,\n", " 18.13: 995,\n", " 18.53: 995,\n", " 14.22: 994,\n", " 18.33: 994,\n", " 17.36: 994,\n", " 14.97: 994,\n", " 23.52: 994,\n", " 15.31: 994,\n", " 15.04: 993,\n", " 12.64: 993,\n", " 20.48: 993,\n", " 16.06: 992,\n", " 14.44: 992,\n", " 17.38: 992,\n", " 13.65: 991,\n", " 17.0: 991,\n", " 19.38: 991,\n", " 14.82: 990,\n", " 14.03: 990,\n", " 18.7: 989,\n", " 16.12: 989,\n", " 17.44: 988,\n", " 12.7: 988,\n", " 14.34: 988,\n", " 17.48: 988,\n", " 18.03: 988,\n", " 12.3: 988,\n", " 15.5: 988,\n", " 13.9: 987,\n", " 10.56: 987,\n", " 19.04: 987,\n", " 12.74: 987,\n", " 18.67: 986,\n", " 17.68: 986,\n", " 17.02: 985,\n", " 15.09: 985,\n", " 13.41: 985,\n", " 16.38: 985,\n", " 15.27: 983,\n", " 16.75: 983,\n", " 17.95: 983,\n", " 13.49: 983,\n", " 16.22: 983,\n", " 16.85: 983,\n", " 23.28: 982,\n", " 16.91: 982,\n", " 19.65: 982,\n", " 13.73: 982,\n", " 14.59: 982,\n", " 16.11: 982,\n", " 16.73: 981,\n", " 19.71: 981,\n", " 14.51: 981,\n", " 17.82: 981,\n", " 18.39: 981,\n", " 14.8: 981,\n", " 14.9: 980,\n", " 13.24: 980,\n", " 11.4: 980,\n", " 15.33: 980,\n", " 14.52: 979,\n", " 15.76: 979,\n", " 14.49: 979,\n", " 20.1: 978,\n", " 17.2: 978,\n", " 16.96: 978,\n", " 16.83: 978,\n", " 14.28: 978,\n", " 12.27: 978,\n", " 14.27: 977,\n", " 17.67: 977,\n", " 15.93: 977,\n", " 23.04: 977,\n", " 14.67: 977,\n", " 16.65: 976,\n", " 18.68: 976,\n", " 17.97: 976,\n", " 16.15: 976,\n", " 19.4: 975,\n", " 13.72: 975,\n", " 13.04: 975,\n", " 14.46: 975,\n", " 17.54: 975,\n", " 19.13: 974,\n", " 14.76: 974,\n", " 14.79: 974,\n", " 14.81: 974,\n", " 18.88: 974,\n", " 17.79: 974,\n", " 17.14: 973,\n", " 15.55: 973,\n", " 14.36: 973,\n", " 16.44: 973,\n", " 17.58: 973,\n", " 17.11: 972,\n", " 18.42: 972,\n", " 14.77: 972,\n", " 14.12: 972,\n", " 13.15: 972,\n", " 15.73: 972,\n", " 17.55: 972,\n", " 18.66: 971,\n", " 12.12: 971,\n", " 16.24: 971,\n", " 14.43: 971,\n", " 13.4: 971,\n", " 12.83: 971,\n", " 13.78: 971,\n", " 17.88: 970,\n", " 14.1: 970,\n", " 14.18: 970,\n", " 16.62: 970,\n", " 18.04: 970,\n", " 16.14: 970,\n", " 17.07: 970,\n", " 18.83: 970,\n", " 18.21: 969,\n", " 15.29: 969,\n", " 18.32: 969,\n", " 14.61: 969,\n", " 15.79: 969,\n", " 13.57: 969,\n", " 15.26: 969,\n", " 15.78: 969,\n", " 19.9: 969,\n", " 12.84: 969,\n", " 15.96: 969,\n", " 18.3: 969,\n", " 18.74: 968,\n", " 15.49: 968,\n", " 16.58: 968,\n", " 18.34: 968,\n", " 14.62: 968,\n", " 12.76: 967,\n", " 19.11: 967,\n", " 14.11: 966,\n", " 14.24: 966,\n", " 15.02: 966,\n", " 17.3: 966,\n", " 20.13: 966,\n", " 20.38: 965,\n", " 13.71: 965,\n", " 16.92: 965,\n", " 18.12: 965,\n", " 15.98: 965,\n", " 16.37: 964,\n", " 12.15: 964,\n", " 16.25: 964,\n", " 13.17: 964,\n", " 14.66: 964,\n", " 17.31: 964,\n", " 19.63: 964,\n", " 15.17: 964,\n", " 19.88: 964,\n", " 14.75: 964,\n", " 16.1: 963,\n", " 15.51: 963,\n", " 17.26: 963,\n", " 13.56: 963,\n", " 13.6: 962,\n", " 17.15: 962,\n", " 16.02: 962,\n", " 15.46: 962,\n", " 18.19: 962,\n", " 16.54: 962,\n", " 18.51: 962,\n", " 13.87: 962,\n", " 17.5: 961,\n", " 19.1: 961,\n", " 17.93: 961,\n", " 22.2: 961,\n", " 15.03: 961,\n", " 13.76: 960,\n", " 16.64: 960,\n", " 12.17: 960,\n", " 19.49: 960,\n", " 14.84: 960,\n", " 18.08: 960,\n", " 15.71: 960,\n", " 13.7: 959,\n", " 18.18: 959,\n", " 13.86: 959,\n", " 16.47: 959,\n", " 19.18: 959,\n", " 16.45: 959,\n", " 14.04: 959,\n", " 18.52: 959,\n", " 17.62: 959,\n", " 15.94: 958,\n", " 14.55: 958,\n", " 15.57: 958,\n", " 13.18: 958,\n", " 15.39: 958,\n", " 17.12: 958,\n", " 17.39: 958,\n", " 20.03: 957,\n", " 15.65: 957,\n", " 18.4: 957,\n", " 15.21: 957,\n", " 16.35: 957,\n", " 18.64: 957,\n", " 13.83: 957,\n", " 13.25: 956,\n", " 13.52: 956,\n", " 16.48: 956,\n", " 20.45: 955,\n", " 15.14: 955,\n", " 18.9: 955,\n", " 15.56: 955,\n", " 17.42: 955,\n", " 12.8: 955,\n", " 13.54: 955,\n", " 18.84: 954,\n", " 16.9: 954,\n", " 17.24: 954,\n", " 13.85: 954,\n", " 14.98: 954,\n", " 18.22: 954,\n", " 16.84: 954,\n", " 15.08: 954,\n", " 15.15: 954,\n", " 16.67: 953,\n", " 13.89: 953,\n", " 14.37: 953,\n", " 18.05: 953,\n", " 13.58: 952,\n", " 14.48: 952,\n", " 15.67: 952,\n", " 18.56: 952,\n", " 15.7: 952,\n", " 15.38: 952,\n", " 17.34: 952,\n", " 13.35: 952,\n", " 17.8: 952,\n", " 17.01: 952,\n", " 9.84: 952,\n", " 14.45: 952,\n", " 13.31: 951,\n", " 14.94: 951,\n", " 18.5: 950,\n", " 14.32: 950,\n", " 14.21: 950,\n", " 19.48: 950,\n", " 17.66: 950,\n", " 13.0: 950,\n", " 19.14: 950,\n", " 15.41: 950,\n", " 15.68: 950,\n", " 19.01: 950,\n", " 19.41: 950,\n", " 16.95: 950,\n", " 17.13: 949,\n", " 19.52: 949,\n", " 14.29: 949,\n", " 17.33: 949,\n", " 19.07: 948,\n", " 17.9: 948,\n", " 13.94: 948,\n", " 22.56: 948,\n", " 15.54: 948,\n", " 13.59: 948,\n", " 14.91: 948,\n", " 18.77: 947,\n", " 20.12: 947,\n", " 14.71: 947,\n", " 13.22: 947,\n", " 14.05: 947,\n", " 18.16: 947,\n", " 14.73: 947,\n", " 14.56: 946,\n", " 15.95: 946,\n", " 14.09: 946,\n", " 16.42: 946,\n", " 17.06: 946,\n", " 19.17: 946,\n", " 17.43: 945,\n", " 17.84: 945,\n", " 17.17: 945,\n", " 16.7: 945,\n", " 14.47: 945,\n", " 19.95: 944,\n", " 16.46: 944,\n", " 18.14: 944,\n", " 16.97: 944,\n", " 13.84: 943,\n", " 15.18: 943,\n", " 12.56: 943,\n", " 15.28: 943,\n", " 13.64: 943,\n", " 14.23: 943,\n", " 13.39: 943,\n", " 16.68: 942,\n", " 22.4: 942,\n", " 15.97: 942,\n", " 19.23: 942,\n", " 12.78: 942,\n", " 13.42: 942,\n", " 13.13: 942,\n", " 13.61: 941,\n", " 14.53: 941,\n", " 19.31: 941,\n", " 15.86: 941,\n", " 18.63: 941,\n", " 19.97: 940,\n", " 16.71: 940,\n", " 19.02: 940,\n", " 20.44: 940,\n", " 14.72: 940,\n", " 14.99: 940,\n", " 16.23: 940,\n", " 18.2: 940,\n", " 16.49: 939,\n", " 13.32: 938,\n", " 18.57: 938,\n", " 15.66: 937,\n", " 13.27: 937,\n", " 12.98: 937,\n", " 16.93: 937,\n", " 17.87: 937,\n", " 18.31: 936,\n", " 16.17: 936,\n", " 15.62: 936,\n", " 12.91: 936,\n", " 12.46: 936,\n", " 18.15: 936,\n", " 13.37: 936,\n", " 15.34: 936,\n", " 16.72: 935,\n", " 17.78: 935,\n", " 16.76: 935,\n", " 16.16: 934,\n", " 12.62: 934,\n", " 14.69: 934,\n", " 17.46: 934,\n", " 23.4: 934,\n", " 21.17: 933,\n", " 12.69: 933,\n", " 11.7: 933,\n", " 12.39: 933,\n", " 14.31: 933,\n", " 13.16: 932,\n", " 20.52: 932,\n", " 16.94: 932,\n", " 13.75: 932,\n", " 19.25: 932,\n", " 19.96: 931,\n", " 14.95: 931,\n", " 18.58: 931,\n", " 11.71: 931,\n", " 13.33: 931,\n", " 20.82: 931,\n", " 18.09: 931,\n", " 14.54: 930,\n", " 16.28: 930,\n", " 12.75: 929,\n", " 16.41: 929,\n", " 15.8: 929,\n", " 15.06: 929,\n", " 16.69: 929,\n", " 14.01: 928,\n", " 19.24: 928,\n", " 12.53: 928,\n", " 11.93: 928,\n", " 14.06: 927,\n", " 18.37: 927,\n", " 16.99: 927,\n", " 19.36: 927,\n", " 18.98: 927,\n", " 19.0: 926,\n", " 17.91: 926,\n", " 17.74: 926,\n", " 12.77: 926,\n", " 14.3: 926,\n", " 18.27: 925,\n", " 14.85: 925,\n", " 19.47: 925,\n", " 13.36: 925,\n", " 17.85: 925,\n", " 18.54: 925,\n", " 18.92: 924,\n", " 19.59: 924,\n", " 18.94: 924,\n", " 19.85: 924,\n", " 14.26: 924,\n", " 19.05: 924,\n", " 13.98: 924,\n", " 19.55: 924,\n", " 15.22: 924,\n", " 11.0: 923,\n", " 19.32: 923,\n", " 20.85: 923,\n", " 12.9: 923,\n", " 13.12: 923,\n", " 18.1: 923,\n", " 17.45: 922,\n", " 17.37: 922,\n", " 19.03: 922,\n", " 17.92: 922,\n", " 12.41: 922,\n", " 15.44: 922,\n", " 12.87: 921,\n", " 14.07: 921,\n", " 17.69: 921,\n", " 16.04: 921,\n", " 13.77: 921,\n", " 16.52: 921,\n", " 15.77: 921,\n", " 17.83: 920,\n", " 12.2: 920,\n", " 19.58: 919,\n", " 17.57: 919,\n", " 13.08: 919,\n", " 20.74: 919,\n", " 12.5: 919,\n", " 15.52: 919,\n", " 12.51: 919,\n", " 15.69: 919,\n", " 12.54: 919,\n", " 13.3: 919,\n", " 14.42: 918,\n", " 21.68: 918,\n", " 13.47: 918,\n", " 20.28: 918,\n", " 12.33: 918,\n", " 10.2: 918,\n", " 19.34: 917,\n", " 20.14: 917,\n", " 20.37: 917,\n", " 19.06: 917,\n", " 18.29: 917,\n", " 18.02: 917,\n", " 18.89: 917,\n", " 19.62: 916,\n", " 16.66: 916,\n", " 12.38: 916,\n", " 18.35: 916,\n", " 11.97: 916,\n", " 20.29: 915,\n", " 15.74: 915,\n", " 16.61: 915,\n", " 17.59: 915,\n", " 20.31: 915,\n", " 19.84: 915,\n", " 14.19: 915,\n", " 17.61: 915,\n", " 18.86: 914,\n", " 19.16: 914,\n", " 17.89: 914,\n", " 20.7: 914,\n", " 13.95: 914,\n", " 21.57: 914,\n", " 13.06: 913,\n", " 12.82: 913,\n", " 12.42: 913,\n", " 20.96: 913,\n", " 21.58: 913,\n", " 17.81: 912,\n", " 15.43: 912,\n", " 12.03: 912,\n", " 20.04: 911,\n", " 11.45: 911,\n", " 7.2: 911,\n", " 17.86: 911,\n", " 20.62: 911,\n", " 20.19: 910,\n", " 12.55: 910,\n", " 20.33: 910,\n", " 22.0: 910,\n", " 15.99: 910,\n", " 13.51: 910,\n", " 19.37: 910,\n", " 14.63: 910,\n", " 11.63: 910,\n", " 12.43: 910,\n", " 19.57: 910,\n", " 18.62: 909,\n", " 11.98: 909,\n", " 13.05: 909,\n", " 13.23: 909,\n", " 17.09: 908,\n", " 12.4: 908,\n", " 17.96: 908,\n", " 19.6: 908,\n", " 19.7: 908,\n", " 20.24: 908,\n", " 18.06: 908,\n", " 12.32: 907,\n", " 12.88: 907,\n", " 13.09: 907,\n", " 20.36: 907,\n", " 15.19: 907,\n", " 21.3: 907,\n", " 13.62: 907,\n", " 20.21: 907,\n", " 11.84: 907,\n", " 19.33: 907,\n", " 13.38: 907,\n", " 12.85: 906,\n", " 13.29: 906,\n", " 19.78: 906,\n", " 21.09: 905,\n", " 18.55: 905,\n", " 21.23: 905,\n", " 12.08: 904,\n", " 22.43: 904,\n", " 20.79: 904,\n", " 13.03: 904,\n", " 11.91: 904,\n", " 14.58: 904,\n", " 20.72: 903,\n", " 15.32: 903,\n", " 12.35: 903,\n", " 13.66: 903,\n", " 21.72: 903,\n", " 20.34: 903,\n", " 19.42: 903,\n", " 18.26: 903,\n", " 12.65: 902,\n", " 20.91: 902,\n", " 8.88: 902,\n", " 17.03: 902,\n", " 12.07: 902,\n", " 20.07: 901,\n", " 16.34: 901,\n", " 21.65: 901,\n", " 12.34: 901,\n", " 16.82: 900,\n", " 18.65: 900,\n", " 23.76: 900,\n", " 11.82: 900,\n", " 19.22: 900,\n", " 18.85: 899,\n", " 11.55: 899,\n", " 13.74: 899,\n", " 19.83: 898,\n", " 11.36: 898,\n", " 11.8: 898,\n", " 21.96: 897,\n", " 18.91: 897,\n", " 20.94: 897,\n", " 18.76: 897,\n", " 12.45: 897,\n", " 9.12: 896,\n", " 21.63: 896,\n", " 20.53: 895,\n", " 20.86: 895,\n", " 12.19: 895,\n", " 17.72: 895,\n", " 23.6: 894,\n", " 12.28: 894,\n", " 12.36: 893,\n", " 11.95: 893,\n", " 11.47: 893,\n", " 20.66: 893,\n", " 18.11: 892,\n", " 19.77: 892,\n", " 12.86: 892,\n", " 19.64: 892,\n", " 11.73: 892,\n", " 20.6: 891,\n", " 21.92: 891,\n", " 15.91: 891,\n", " 13.07: 891,\n", " 20.58: 891,\n", " 12.57: 891,\n", " 21.08: 890,\n", " 20.2: 890,\n", " 20.68: 890,\n", " 12.52: 890,\n", " 12.59: 889,\n", " 20.08: 889,\n", " 11.1: 888,\n", " 13.55: 888,\n", " 11.67: 888,\n", " 20.84: 887,\n", " 13.1: 887,\n", " 20.22: 887,\n", " 12.29: 886,\n", " 19.09: 885,\n", " 14.02: 885,\n", " 14.14: 885,\n", " 15.25: 885,\n", " 21.9: 885,\n", " 12.04: 885,\n", " 17.65: 885,\n", " 16.39: 885,\n", " 18.75: 885,\n", " 18.07: 885,\n", " 16.55: 884,\n", " 18.99: 884,\n", " 21.46: 884,\n", " 11.38: 884,\n", " 21.39: 884,\n", " 20.5: 883,\n", " 20.27: 883,\n", " 22.5: 883,\n", " 15.35: 883,\n", " 12.1: 883,\n", " 13.53: 883,\n", " 13.26: 883,\n", " 12.05: 883,\n", " 20.09: 883,\n", " 19.94: 883,\n", " 11.88: 882,\n", " 22.13: 882,\n", " 19.56: 882,\n", " 13.34: 882,\n", " 11.69: 882,\n", " 20.75: 882,\n", " 19.82: 882,\n", " 19.74: 882,\n", " 12.44: 881,\n", " 19.54: 881,\n", " 20.11: 881,\n", " 21.71: 881,\n", " 10.88: 880,\n", " 18.81: 880,\n", " 20.93: 880,\n", " 18.87: 880,\n", " 11.96: 880,\n", " 18.44: 880,\n", " 11.9: 880,\n", " 18.78: 880,\n", " 11.57: 880,\n", " 20.69: 879,\n", " 19.46: 879,\n", " 13.79: 879,\n", " 21.48: 879,\n", " 20.98: 879,\n", " 21.24: 878,\n", " 13.99: 878,\n", " 21.53: 878,\n", " 19.26: 878,\n", " 16.59: 877,\n", " 16.19: 877,\n", " 21.03: 877,\n", " 18.38: 877,\n", " 12.14: 877,\n", " 10.77: 877,\n", " 20.18: 876,\n", " 11.89: 876,\n", " 22.52: 876,\n", " 21.04: 876,\n", " 20.97: 876,\n", " 21.16: 876,\n", " 10.64: 875,\n", " 6.0: 875,\n", " 13.11: 875,\n", " 13.14: 874,\n", " 11.79: 874,\n", " 12.22: 874,\n", " 11.08: 873,\n", " 22.05: 873,\n", " 12.21: 873,\n", " 19.27: 873,\n", " 11.6: 872,\n", " 23.12: 872,\n", " 11.46: 872,\n", " 21.56: 872,\n", " 21.1: 871,\n", " 11.3: 871,\n", " 12.09: 871,\n", " 12.99: 870,\n", " 20.43: 870,\n", " 24.48: 870,\n", " 12.61: 870,\n", " 12.68: 869,\n", " 10.92: 869,\n", " 23.33: 869,\n", " 21.47: 869,\n", " 20.61: 869,\n", " 11.22: 869,\n", " 20.76: 869,\n", " 17.35: 869,\n", " 22.36: 868,\n", " 20.57: 868,\n", " 21.73: 868,\n", " 11.02: 867,\n", " 21.54: 867,\n", " 17.75: 867,\n", " 11.56: 867,\n", " 21.28: 866,\n", " 21.31: 866,\n", " 11.37: 865,\n", " 13.46: 865,\n", " 21.8: 864,\n", " 27.6: 864,\n", " 10.83: 864,\n", " 18.17: 864,\n", " 19.98: 864,\n", " 11.5: 864,\n", " 12.63: 864,\n", " 18.59: 863,\n", " 11.68: 863,\n", " 12.11: 863,\n", " 22.16: 862,\n", " 15.83: 862,\n", " 21.42: 862,\n", " 11.31: 862,\n", " 11.64: 862,\n", " 22.04: 862,\n", " 21.06: 862,\n", " 21.45: 862,\n", " 11.24: 862,\n", " 13.01: 861,\n", " 16.01: 861,\n", " 19.61: 861,\n", " 12.18: 861,\n", " 11.12: 861,\n", " 10.78: 861,\n", " 20.49: 861,\n", " 11.33: 860,\n", " 21.14: 859,\n", " 11.61: 859,\n", " 18.82: 859,\n", " 12.89: 859,\n", " 11.34: 859,\n", " 24.6: 859,\n", " 11.19: 858,\n", " 19.29: 858,\n", " 21.64: 858,\n", " 20.06: 858,\n", " 20.56: 857,\n", " 20.51: 857,\n", " 11.78: 857,\n", " 14.15: 857,\n", " 19.86: 857,\n", " 24.72: 857,\n", " 11.07: 857,\n", " 21.41: 856,\n", " 20.77: 856,\n", " 19.3: 855,\n", " 19.79: 855,\n", " 22.67: 855,\n", " 20.42: 855,\n", " 11.42: 854,\n", " 20.23: 854,\n", " 11.66: 854,\n", " 10.71: 853,\n", " 21.44: 853,\n", " 19.15: 853,\n", " 10.96: 852,\n", " 20.95: 852,\n", " 22.53: 852,\n", " 12.23: 852,\n", " 11.49: 851,\n", " 21.32: 851,\n", " 12.92: 850,\n", " 12.37: 850,\n", " 20.54: 850,\n", " 10.18: 850,\n", " 12.66: 850,\n", " 19.72: 849,\n", " 10.91: 849,\n", " 20.8: 849,\n", " 11.58: 848,\n", " 11.15: 848,\n", " 13.97: 848,\n", " 19.87: 848,\n", " 15.01: 848,\n", " 22.76: 848,\n", " 12.81: 848,\n", " 17.21: 848,\n", " 24.24: 848,\n", " 11.92: 848,\n", " 10.99: 847,\n", " 11.13: 847,\n", " 10.6: 847,\n", " 20.67: 847,\n", " 20.9: 847,\n", " 22.92: 846,\n", " 18.79: 846,\n", " 23.0: 846,\n", " 10.97: 846,\n", " 17.77: 846,\n", " 23.57: 846,\n", " 11.48: 845,\n", " 20.71: 845,\n", " 9.36: 845,\n", " 10.35: 845,\n", " 10.82: 845,\n", " 11.23: 844,\n", " 11.35: 844,\n", " 12.79: 844,\n", " 21.78: 844,\n", " 11.54: 844,\n", " 21.27: 843,\n", " 10.62: 843,\n", " 21.38: 843,\n", " 10.68: 843,\n", " 19.99: 843,\n", " 23.01: 842,\n", " 10.9: 842,\n", " 11.18: 842,\n", " 12.06: 842,\n", " 13.02: 842,\n", " 21.4: 841,\n", " 20.32: 840,\n", " 11.59: 840,\n", " 11.85: 840,\n", " 11.21: 840,\n", " 23.19: 840,\n", " 12.02: 840,\n", " 21.05: 839,\n", " 22.48: 839,\n", " 19.53: 839,\n", " 11.65: 838,\n", " 22.34: 838,\n", " 25.44: 838,\n", " 21.52: 838,\n", " 21.33: 837,\n", " 22.28: 837,\n", " 20.26: 836,\n", " 24.8: 836,\n", " ...}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"dti\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### emp_length (Employment length in years)"]}, {"cell_type": "code", "execution_count": 50, "id": "1c3c835b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['10+ years', '6 years', '4 years', '< 1 year', '2 years',\n", "       '9 years', nan, '5 years', '3 years', '7 years', '1 year',\n", "       '8 years'], dtype=object)"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"emp_length\"].unique()"]}, {"cell_type": "code", "execution_count": 51, "id": "a90b76bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'10+ years': 748005,\n", " '2 years': 203677,\n", " '< 1 year': 189988,\n", " '3 years': 180753,\n", " '1 year': 148403,\n", " '5 years': 139698,\n", " '4 years': 136605,\n", " '6 years': 102628,\n", " '7 years': 92695,\n", " '8 years': 91914,\n", " '9 years': 79395}"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"emp_length\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### emp Title (The job title supplied by the Borrow<PERSON>)"]}, {"cell_type": "code", "execution_count": 52, "id": "1c3c835b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Chef', 'Postmaster ', 'Administrative', ...,\n", "       'Sales, Estimating & Design', 'Acft mechanic', 'BABYSITTER'],\n", "      dtype=object)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"emp_title\"].unique()"]}, {"cell_type": "code", "execution_count": 53, "id": "a90b76bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Teacher': 38824,\n", " 'Manager': 34298,\n", " 'Owner': 21977,\n", " 'Registered Nurse': 15867,\n", " 'Driver': 14753,\n", " 'RN': 14737,\n", " 'Supervisor': 14297,\n", " 'Sales': 13050,\n", " 'Project Manager': 10971,\n", " 'Office Manager': 9772,\n", " 'General Manager': 9251,\n", " 'Director': 8934,\n", " 'owner': 8507,\n", " 'President': 7660,\n", " 'Engineer': 7304,\n", " 'manager': 7060,\n", " 'teacher': 6692,\n", " 'Operations Manager': 6128,\n", " 'Vice President': 5874,\n", " 'Accountant': 5596,\n", " 'Sales Manager': 5408,\n", " 'driver': 5386,\n", " 'Attorney': 5365,\n", " 'Administrative Assistant': 5270,\n", " 'Nurse': 5189,\n", " 'Truck Driver': 4911,\n", " 'Account Manager': 4727,\n", " 'Police Officer': 4572,\n", " 'Technician': 4525,\n", " 'Analyst': 4238,\n", " 'Executive Assistant': 4161,\n", " 'Server': 4121,\n", " 'Store Manager': 4088,\n", " 'sales': 3876,\n", " 'Mechanic': 3724,\n", " 'Paralegal': 3699,\n", " 'Software Engineer': 3635,\n", " 'supervisor': 3586,\n", " 'Supervisor ': 3354,\n", " 'CEO': 3350,\n", " 'Controller': 3325,\n", " 'Assistant Manager': 3269,\n", " 'Consultant': 3247,\n", " 'Program Manager': 3237,\n", " 'Branch Manager': 3166,\n", " 'Foreman': 3089,\n", " 'truck driver': 3020,\n", " 'Administrator': 2997,\n", " 'Electrician': 2996,\n", " 'Registered nurse': 2865,\n", " 'Principal': 2831,\n", " 'Truck driver': 2771,\n", " 'Manager ': 2669,\n", " 'Operator': 2657,\n", " 'Business Analyst': 2617,\n", " 'Account Executive': 2597,\n", " 'Executive Director': 2551,\n", " 'Professor': 2484,\n", " 'IT Manager': 2483,\n", " 'Customer Service': 2372,\n", " 'Clerk': 2304,\n", " 'Director of Operations': 2222,\n", " 'LPN': 2215,\n", " 'Bartender': 2165,\n", " 'Teacher ': 2115,\n", " 'Associate': 2063,\n", " 'Superintendent': 2052,\n", " 'registered nurse': 2035,\n", " 'Secretary': 2033,\n", " 'Legal Assistant': 2011,\n", " 'District Manager': 1971,\n", " 'Physician': 1949,\n", " 'CNA': 1921,\n", " 'Maintenance': 1883,\n", " 'Financial Advisor': 1880,\n", " 'Machinist': 1874,\n", " 'Pharmacist': 1840,\n", " 'Cashier': 1821,\n", " 'Officer': 1820,\n", " 'mechanic': 1815,\n", " 'Laborer': 1799,\n", " 'Realtor': 1774,\n", " 'Partner': 1769,\n", " 'Financial Analyst': 1743,\n", " 'Accounting Manager': 1743,\n", " 'Bookkeeper': 1706,\n", " 'Senior Manager': 1690,\n", " 'Social Worker': 1649,\n", " 'Service Manager': 1631,\n", " 'Welder': 1620,\n", " 'Property Manager': 1595,\n", " 'Instructor': 1590,\n", " 'Production Manager': 1564,\n", " 'Owner ': 1562,\n", " 'Cook': 1560,\n", " 'Firefighter': 1552,\n", " 'Underwriter': 1535,\n", " 'Sales Associate': 1526,\n", " 'Software Developer': 1522,\n", " 'Nurse Practitioner': 1510,\n", " 'Marketing Manager': 1482,\n", " 'Business Manager': 1459,\n", " 'Receptionist': 1456,\n", " 'Medical Assistant': 1455,\n", " 'VP': 1453,\n", " 'nurse': 1449,\n", " 'General manager': 1445,\n", " 'MANAGER': 1442,\n", " 'Agent': 1437,\n", " 'Security Officer': 1433,\n", " 'Office manager': 1429,\n", " 'Dispatcher': 1395,\n", " 'Regional Manager': 1394,\n", " 'Senior Accountant': 1389,\n", " 'Counselor': 1385,\n", " 'Pastor': 1382,\n", " 'CFO': 1377,\n", " 'Correctional Officer': 1374,\n", " 'Network Engineer': 1360,\n", " 'Finance Manager': 1356,\n", " 'Case Manager': 1355,\n", " 'Chef': 1353,\n", " 'Pilot': 1339,\n", " 'technician': 1338,\n", " 'Store manager': 1335,\n", " 'Machine Operator': 1327,\n", " 'Carpenter': 1325,\n", " 'Managing Director': 1316,\n", " 'Systems Engineer': 1294,\n", " 'HR Manager': 1294,\n", " 'Staff Accountant': 1288,\n", " 'Assistant Principal': 1271,\n", " 'Management': 1270,\n", " 'Driver ': 1270,\n", " 'Loan Officer': 1268,\n", " 'Rn': 1267,\n", " 'IT Specialist': 1264,\n", " 'Technician ': 1261,\n", " 'Senior Consultant': 1257,\n", " 'OWNER': 1253,\n", " 'office manager': 1249,\n", " 'Tech': 1248,\n", " 'Recruiter': 1216,\n", " 'Accounting': 1212,\n", " 'Auditor': 1203,\n", " 'Buyer': 1198,\n", " 'Legal Secretary': 1197,\n", " 'Sales Representative': 1172,\n", " 'Senior Project Manager': 1166,\n", " 'server': 1164,\n", " 'Respiratory Therapist': 1161,\n", " 'Product Manager': 1152,\n", " 'Registered Nurse ': 1149,\n", " 'Assistant Professor': 1148,\n", " 'Sales manager': 1135,\n", " 'Sales ': 1115,\n", " 'Police officer': 1114,\n", " 'Custodian': 1114,\n", " 'CSR': 1097,\n", " 'Flight Attendant': 1097,\n", " 'Sales Rep': 1089,\n", " 'Senior Software Engineer': 1083,\n", " 'Team Leader': 1083,\n", " 'Coordinator': 1082,\n", " 'Superintendent ': 1081,\n", " 'machine operator': 1081,\n", " 'Captain': 1081,\n", " 'operator': 1075,\n", " 'clerk': 1068,\n", " 'Regional Sales Manager': 1053,\n", " 'Maintenance ': 1045,\n", " 'Warehouse Manager': 1043,\n", " 'Designer': 1041,\n", " 'Physical Therapist': 1030,\n", " 'Caregiver': 1029,\n", " 'Educator': 1026,\n", " 'Senior Vice President': 1024,\n", " 'Investigator': 1023,\n", " 'general manager': 1022,\n", " 'Deputy Sheriff': 1021,\n", " 'Paramedic': 1017,\n", " 'foreman': 1012,\n", " 'Area Manager': 1009,\n", " 'president': 1003,\n", " 'Estimator': 1002,\n", " 'Project manager': 999,\n", " 'Director of Sales': 996,\n", " 'engineer': 983,\n", " 'Project Engineer': 981,\n", " 'Sergeant': 980,\n", " 'Banker': 953,\n", " 'Vice President ': 947,\n", " 'Inspector': 944,\n", " 'Plant Manager': 944,\n", " 'Pharmacy Technician': 937,\n", " 'electrician': 934,\n", " 'IT': 931,\n", " 'Program Director': 927,\n", " 'Teller': 925,\n", " 'Director ': 924,\n", " 'Assistant manager': 921,\n", " 'Physician Assistant': 911,\n", " 'maintenance': 905,\n", " 'Graphic Designer': 901,\n", " 'Sales Consultant': 901,\n", " 'Customer Service Rep': 900,\n", " 'Systems Analyst': 892,\n", " 'Project Coordinator': 891,\n", " 'store manager': 889,\n", " 'Truck driver ': 888,\n", " 'sales manager': 884,\n", " 'COO': 883,\n", " 'Territory Manager': 882,\n", " 'Associate Director': 865,\n", " 'Customer Service Representative': 865,\n", " 'Deputy': 862,\n", " 'Dental Hygienist': 861,\n", " 'Security': 860,\n", " 'Systems Administrator': 858,\n", " 'Business Development Manager': 858,\n", " 'Machine operator': 853,\n", " 'Painter': 852,\n", " 'Personal Banker': 851,\n", " 'Assistant Director': 850,\n", " 'cashier': 849,\n", " 'customer service': 846,\n", " 'Associate Professor': 844,\n", " 'Bus Driver': 841,\n", " 'police officer': 836,\n", " 'IT Director': 835,\n", " 'Assistant Store Manager': 834,\n", " 'Electrician ': 825,\n", " 'Insurance Agent': 821,\n", " 'Customer Service Manager': 821,\n", " 'Plumber': 817,\n", " 'Senior Analyst': 816,\n", " 'Conductor': 815,\n", " 'Letter Carrier': 813,\n", " 'machinist': 813,\n", " 'Admin': 804,\n", " 'Service Technician': 801,\n", " 'cook': 800,\n", " 'Maintenance Supervisor': 797,\n", " 'Senior Associate': 797,\n", " 'Program Analyst': 797,\n", " 'Warehouse': 795,\n", " 'Administrative Assistant ': 788,\n", " 'Engineering Manager': 785,\n", " 'Special Education Teacher': 781,\n", " 'Production': 769,\n", " 'Production Supervisor': 761,\n", " 'Human Resources': 757,\n", " 'Dental Assistant': 755,\n", " 'Assembler': 751,\n", " 'laborer': 750,\n", " 'GM': 746,\n", " 'Operations Supervisor': 746,\n", " 'Department Manager': 741,\n", " 'Mechanic ': 740,\n", " 'Installer': 735,\n", " 'Data Analyst': 735,\n", " 'Marketing': 730,\n", " 'Team Lead': 730,\n", " 'Detective': 727,\n", " 'Bus Operator': 719,\n", " 'President ': 719,\n", " 'Programmer': 715,\n", " 'Senior Engineer': 714,\n", " 'SALES': 713,\n", " 'Marketing Director': 713,\n", " 'System Administrator': 713,\n", " 'Web Developer': 709,\n", " 'Executive Chef': 707,\n", " 'Customer service': 707,\n", " 'Administration': 704,\n", " 'Electrical Engineer': 701,\n", " 'welder': 696,\n", " 'Network Administrator': 693,\n", " 'Therapist': 691,\n", " 'Purchasing Manager': 689,\n", " 'Quality Manager': 687,\n", " 'Corrections Officer': 687,\n", " 'project manager': 685,\n", " 'Occupational Therapist': 681,\n", " 'Program Coordinator': 675,\n", " 'tech': 672,\n", " 'SUPERVISOR': 669,\n", " 'OFFICE MANAGER': 668,\n", " 'School Counselor': 664,\n", " 'rn': 664,\n", " 'Trainer': 663,\n", " 'Delivery Driver': 661,\n", " 'General Manager ': 661,\n", " 'Courier': 659,\n", " 'Salesman': 649,\n", " 'LVN': 644,\n", " 'DRIVER': 643,\n", " 'US Army': 641,\n", " 'Human Resources Manager': 639,\n", " 'Inside Sales': 638,\n", " 'Registered nurse ': 638,\n", " 'Operations': 633,\n", " 'Nurse Manager': 632,\n", " 'Broker': 628,\n", " 'Service Tech': 624,\n", " 'Sales Engineer': 620,\n", " 'Managing Partner': 618,\n", " 'Accounts Payable': 615,\n", " 'Mechanical Engineer': 615,\n", " 'Operations manager': 612,\n", " 'Sales Executive': 601,\n", " 'Sales Director': 600,\n", " 'Stylist': 597,\n", " 'Administrative': 597,\n", " 'Office Administrator': 595,\n", " 'Office Manager ': 593,\n", " 'Correction Officer': 585,\n", " 'TEACHER': 584,\n", " 'Lieutenant': 577,\n", " 'Medical Technologist': 576,\n", " 'assistant manager': 574,\n", " 'Project Manager ': 572,\n", " 'Office Assistant': 571,\n", " 'Practice Manager': 570,\n", " 'Director of Finance': 569,\n", " 'Librarian': 568,\n", " 'Labor': 566,\n", " 'custodian': 565,\n", " 'carpenter': 561,\n", " 'Teacher/Coach': 561,\n", " 'Specialist': 557,\n", " 'Dentist': 555,\n", " 'Material Handler': 554,\n", " 'Claims Adjuster': 545,\n", " 'Forman': 544,\n", " 'Admin Assistant': 543,\n", " 'REGISTERED NURSE': 542,\n", " 'Truck Driver ': 540,\n", " 'Branch manager': 539,\n", " 'Senior Director': 538,\n", " 'Operator ': 538,\n", " 'Maintenance Manager': 538,\n", " 'Assistant Vice President': 535,\n", " 'Management Analyst': 533,\n", " 'Management ': 532,\n", " 'Assistant manager ': 531,\n", " 'Director of Marketing': 531,\n", " 'Producer': 531,\n", " 'IT Analyst': 529,\n", " 'Programmer Analyst': 522,\n", " 'Lead': 522,\n", " 'operations manager': 522,\n", " 'Logistics Manager': 519,\n", " 'Construction Manager': 518,\n", " 'Lpn': 517,\n", " 'Executive Assistant ': 517,\n", " 'IT Project Manager': 517,\n", " 'Quality Engineer': 515,\n", " 'attorney': 514,\n", " 'Architect': 514,\n", " 'Merchandiser': 512,\n", " 'Account manager': 511,\n", " 'Service Advisor': 510,\n", " 'Senior Financial Analyst': 510,\n", " 'Creative Director': 510,\n", " 'Assistant': 509,\n", " 'Owner/Operator': 508,\n", " 'CPA': 506,\n", " 'Sr. Project Manager': 505,\n", " 'HR Director': 500,\n", " 'Relationship Manager': 499,\n", " 'Customer service ': 496,\n", " 'Director of Nursing': 495,\n", " 'Assistant Manager ': 495,\n", " 'warehouse': 494,\n", " 'Waitress': 493,\n", " 'service tech': 490,\n", " 'Chief Operating Officer': 486,\n", " 'Senior Account Manager': 484,\n", " 'Radiologic Technologist': 483,\n", " 'accountant': 482,\n", " 'Contract Specialist': 481,\n", " 'Receptionist ': 480,\n", " 'Scientist': 479,\n", " 'chef': 479,\n", " 'Operations Manager ': 479,\n", " 'Finance Director': 476,\n", " 'Finance': 476,\n", " 'Foreman ': 475,\n", " 'letter carrier': 474,\n", " 'director': 474,\n", " 'Business Development': 474,\n", " 'bus driver': 472,\n", " 'Lineman': 469,\n", " 'Machine operator ': 467,\n", " 'bartender': 463,\n", " 'Assistant General Manager': 463,\n", " 'Correctional officer': 462,\n", " 'Maintenance Technician': 461,\n", " 'sales associate': 460,\n", " 'Facilities Manager': 460,\n", " 'Budget Analyst': 459,\n", " 'RN ': 458,\n", " 'Dealer': 457,\n", " 'Bank of America': 456,\n", " 'Probation Officer': 455,\n", " 'Accountant ': 453,\n", " 'Executive': 453,\n", " 'Mail Carrier': 453,\n", " 'service manager': 453,\n", " 'Parts Manager': 452,\n", " 'General manager ': 449,\n", " 'Loan Processor': 449,\n", " 'Special Agent': 448,\n", " 'Social worker': 447,\n", " 'Clerical': 445,\n", " 'Sr. Manager': 443,\n", " 'Planner': 443,\n", " 'School Psychologist': 443,\n", " 'Police officer ': 441,\n", " 'Service manager': 440,\n", " 'production': 439,\n", " 'District Sales Manager': 438,\n", " 'Regional Director': 433,\n", " 'Cna': 432,\n", " 'Housekeeper': 432,\n", " 'pharmacist': 432,\n", " 'Engineer ': 432,\n", " 'Senior Pastor': 431,\n", " 'Paraprofessional': 431,\n", " 'Assembly': 430,\n", " 'Credit Analyst': 429,\n", " 'Nurse ': 427,\n", " 'Assistant Controller': 423,\n", " 'Psychologist': 422,\n", " 'Housekeeping': 422,\n", " 'Faculty': 421,\n", " 'Shift Supervisor': 421,\n", " 'Facility Manager': 420,\n", " 'physician': 417,\n", " 'Waiter': 416,\n", " 'Program Specialist': 416,\n", " 'Bus driver': 416,\n", " 'Senior Business Analyst': 415,\n", " 'Accounting Clerk': 414,\n", " 'Carrier': 414,\n", " 'cna': 414,\n", " 'IT Consultant': 413,\n", " 'Chemist': 412,\n", " 'Editor': 412,\n", " 'Manufacturing Engineer': 412,\n", " 'SALES MANAGER': 411,\n", " 'Attorney ': 411,\n", " 'Security Guard': 409,\n", " 'Field Service Engineer': 409,\n", " 'SVP': 409,\n", " 'correctional officer': 408,\n", " 'HR Specialist': 408,\n", " 'TRUCK DRIVER': 408,\n", " 'Bartender ': 407,\n", " 'secretary': 406,\n", " 'Massage Therapist': 405,\n", " 'Civil Engineer': 404,\n", " 'Associate Attorney': 403,\n", " 'Nanny': 402,\n", " 'Contractor': 402,\n", " 'System Engineer': 402,\n", " 'Medical Assistant ': 401,\n", " 'paralegal': 401,\n", " 'Field Engineer': 400,\n", " 'Site Manager': 400,\n", " 'Service tech': 400,\n", " 'medical assistant': 398,\n", " 'System Analyst': 397,\n", " 'Police Officer ': 396,\n", " 'EMT': 395,\n", " 'Sales rep': 395,\n", " 'RN Case Manager': 394,\n", " 'Staff Nurse': 391,\n", " 'Director of IT': 390,\n", " 'Marketing Coordinator': 390,\n", " 'Director of Human Resources': 388,\n", " 'VP of Operations': 387,\n", " 'Letter carrier': 387,\n", " 'Office manager ': 387,\n", " 'HR Generalist': 387,\n", " 'Developer': 383,\n", " 'superintendent': 383,\n", " 'Administration ': 381,\n", " 'Senior Account Executive': 381,\n", " 'Medical assistant': 380,\n", " 'Billing Specialist': 380,\n", " 'Design Engineer': 379,\n", " 'Police Sergeant': 379,\n", " 'Phlebotomist': 379,\n", " 'VP Sales': 378,\n", " 'Account Executive ': 378,\n", " 'assembler': 376,\n", " 'HR': 376,\n", " 'Director of Business Development': 376,\n", " 'Medical assistant ': 376,\n", " 'Customer Service ': 375,\n", " 'Chief Engineer': 375,\n", " 'CTO': 375,\n", " 'Payroll Manager': 372,\n", " 'Advisor': 372,\n", " 'Team leader': 370,\n", " 'Chief Financial Officer': 369,\n", " '<PERSON><PERSON>': 368,\n", " 'Warehouse Supervisor': 367,\n", " 'AVP': 367,\n", " 'sales rep': 366,\n", " 'Secretary ': 366,\n", " 'Baker': 366,\n", " 'Portfolio Manager': 365,\n", " 'PRESIDENT': 364,\n", " 'Engineering Technician': 364,\n", " 'Machinist ': 364,\n", " 'Air Traffic Controller': 363,\n", " 'GENERAL MANAGER': 363,\n", " 'Sales associate': 363,\n", " 'Safety Manager': 361,\n", " 'Store Manager ': 361,\n", " 'Administrative assistant ': 359,\n", " 'Pharmacy Tech': 358,\n", " 'Carpenter ': 357,\n", " 'Store manager ': 357,\n", " 'Quality Control': 357,\n", " 'analyst': 357,\n", " 'Account Manager ': 355,\n", " 'Purchasing Agent': 355,\n", " 'Medical Coder': 352,\n", " 'account manager': 352,\n", " 'Processor': 352,\n", " 'receptionist': 351,\n", " 'dispatcher': 351,\n", " 'Business Systems Analyst': 350,\n", " 'Maintenance Tech': 350,\n", " 'Compliance Officer': 349,\n", " 'Database Administrator': 349,\n", " 'Equipment Operator': 348,\n", " 'Senior Software Developer': 347,\n", " 'Senior Systems Engineer': 347,\n", " 'Physical Therapist Assistant': 346,\n", " 'Shipping': 345,\n", " 'Lab Tech': 345,\n", " 'Paralegal ': 344,\n", " 'painter': 344,\n", " 'Resident Physician': 344,\n", " 'Quality Assurance': 344,\n", " 'Logistics': 343,\n", " 'Speech Language Pathologist': 343,\n", " 'Operation Manager': 343,\n", " 'Maintenance Mechanic': 341,\n", " 'labor': 341,\n", " 'Art Director': 340,\n", " 'Server ': 340,\n", " 'AT&T': 340,\n", " 'Medical Biller': 340,\n", " 'Billing Manager': 340,\n", " 'Pharmacy Manager': 337,\n", " 'Warehouse ': 337,\n", " 'Research Associate': 336,\n", " 'Executive Vice President': 335,\n", " 'caregiver': 333,\n", " 'Real Estate Agent': 333,\n", " 'Administrative Coordinator': 333,\n", " 'Assistant Branch Manager': 332,\n", " 'Stocker': 332,\n", " 'Computer Programmer': 332,\n", " 'Security officer': 332,\n", " 'Porter': 330,\n", " 'Aircraft Mechanic': 328,\n", " 'Outside Sales': 328,\n", " 'Field Technician': 327,\n", " 'Sales Manager ': 326,\n", " 'Lab Technician': 326,\n", " 'Nursing Assistant': 326,\n", " 'Nurse Practitioner ': 325,\n", " 'Technical Writer': 324,\n", " 'Purchasing': 324,\n", " 'Firefighter/Paramedic': 323,\n", " 'Locomotive Engineer': 322,\n", " 'Kaiser Permanente': 322,\n", " 'ACCOUNTANT': 322,\n", " 'Mortgage Loan Officer': 322,\n", " 'Optician': 321,\n", " 'Charge Nurse': 321,\n", " 'security officer': 320,\n", " 'Pipefitter': 319,\n", " 'National Sales Manager': 318,\n", " 'Customer Service Supervisor': 318,\n", " 'VP Operations': 318,\n", " 'vice president': 316,\n", " 'Project manager ': 316,\n", " 'Lawyer': 316,\n", " 'plumber': 315,\n", " 'City Carrier': 312,\n", " 'Staff RN': 311,\n", " 'Operations Analyst': 310,\n", " 'USAF': 310,\n", " 'Operations Specialist': 308,\n", " 'Payroll Specialist': 308,\n", " 'Loan officer': 307,\n", " 'Scheduler': 307,\n", " 'supervisor ': 307,\n", " 'Process Engineer': 306,\n", " 'Compliance Manager': 305,\n", " 'Heavy Equipment Operator': 305,\n", " 'Certified Nursing Assistant': 304,\n", " 'General Sales Manager': 304,\n", " 'Relationship Banker': 304,\n", " 'conductor': 302,\n", " 'USPS': 301,\n", " 'Deputy Director': 300,\n", " 'Analyst ': 299,\n", " 'Licensed Practical Nurse': 299,\n", " 'Sales Representative ': 297,\n", " 'Accounting Supervisor': 297,\n", " 'Division Manager': 297,\n", " 'Tax Manager': 296,\n", " 'administrative assistant': 296,\n", " 'Training Manager': 295,\n", " 'Accounting Specialist': 295,\n", " 'Credit Manager': 295,\n", " 'Accounting ': 294,\n", " 'Community Manager': 294,\n", " 'Case manager': 293,\n", " 'VP of Sales': 292,\n", " 'Technical Support': 292,\n", " 'consultant': 291,\n", " 'owner/operator': 291,\n", " 'Staff Sergeant': 290,\n", " 'Escrow Officer': 290,\n", " 'Security Supervisor': 290,\n", " 'social worker': 289,\n", " 'Custodian ': 289,\n", " 'Correction officer': 289,\n", " 'Dental assistant': 287,\n", " 'Cashier ': 287,\n", " 'officer': 287,\n", " 'Shop Foreman': 287,\n", " 'Substitute Teacher': 286,\n", " 'Accounts Receivable': 286,\n", " 'warehouse manager': 286,\n", " 'Administrative assistant': 285,\n", " 'mail carrier': 284,\n", " 'firefighter': 284,\n", " 'Veterinarian': 284,\n", " 'Warehouse manager': 283,\n", " 'Fleet Manager': 282,\n", " 'Application Developer': 281,\n", " 'Machine Operator ': 280,\n", " 'Shift Manager': 280,\n", " 'team leader': 280,\n", " 'Sous Chef': 280,\n", " 'Sales Specialist': 280,\n", " 'Registrar': 280,\n", " 'UPS': 280,\n", " 'Operations Director': 279,\n", " 'Managing Member': 279,\n", " 'Postmaster': 279,\n", " 'Principal Engineer': 279,\n", " 'Sr. Software Engineer': 278,\n", " 'Walmart': 278,\n", " 'Delivery driver': 278,\n", " 'branch manager': 278,\n", " 'bus operator': 278,\n", " 'District manager': 278,\n", " 'Accounting Assistant': 277,\n", " 'Field Supervisor': 277,\n", " 'Senior Designer': 277,\n", " 'Solutions Architect': 276,\n", " 'Human Resources Specialist': 276,\n", " 'lpn': 276,\n", " 'Sonographer': 275,\n", " 'courier': 275,\n", " 'Inventory Manager': 275,\n", " 'material handler': 273,\n", " 'Office Coordinator': 273,\n", " 'Rural Carrier': 273,\n", " 'Mortgage Banker': 272,\n", " 'Table Games Dealer': 272,\n", " 'Hairstylist': 272,\n", " 'bookkeeper': 272,\n", " 'CRNA': 271,\n", " 'Business Consultant': 271,\n", " 'Equipment operator': 271,\n", " 'Consultant ': 271,\n", " 'installer': 270,\n", " 'Border Patrol Agent': 270,\n", " 'Principal Consultant': 269,\n", " 'Bus operator': 268,\n", " 'Manger': 268,\n", " 'management': 267,\n", " 'School Bus Driver': 266,\n", " 'Respiratory therapist': 265,\n", " 'Barista': 265,\n", " 'inspector': 265,\n", " 'Dental assistant ': 264,\n", " 'Branch Manager ': 264,\n", " 'Administrative Officer': 264,\n", " 'Administrator ': 264,\n", " 'Production ': 262,\n", " 'Dental hygienist': 262,\n", " 'Human Resource Manager': 262,\n", " 'Regional Vice President': 261,\n", " 'Marketing Specialist': 261,\n", " 'Automotive Technician': 261,\n", " 'delivery driver': 261,\n", " 'Territory Sales Manager': 261,\n", " 'National Account Manager': 259,\n", " 'Research Assistant': 258,\n", " 'Mortgage Underwriter': 258,\n", " 'Security ': 258,\n", " 'Service Coordinator': 257,\n", " 'Janitor': 257,\n", " 'Senior Auditor': 256,\n", " 'Mail carrier': 256,\n", " 'associate': 256,\n", " 'Sales manager ': 255,\n", " 'production manager': 255,\n", " 'Director of Development': 254,\n", " 'Director of Technology': 254,\n", " 'Nurse practitioner': 253,\n", " 'Sales Coordinator': 252,\n", " 'Physical therapist': 252,\n", " 'Program Assistant': 252,\n", " 'QA Manager': 252,\n", " 'Soldier': 252,\n", " 'Trooper': 252,\n", " 'Principal ': 251,\n", " 'Speech Pathologist': 251,\n", " 'flight attendant': 251,\n", " 'Field Service Technician': 250,\n", " 'Training Specialist': 250,\n", " 'agent': 249,\n", " 'Mailhandler': 249,\n", " 'Owner Operator': 249,\n", " 'Research Scientist': 248,\n", " 'Quality Assurance Manager': 248,\n", " 'Clinical Manager': 248,\n", " 'Material handler': 248,\n", " 'Instructional Designer': 248,\n", " 'Security Specialist': 247,\n", " 'Billing': 247,\n", " 'Minister': 246,\n", " 'Sergeant ': 245,\n", " 'Contract Administrator': 245,\n", " 'Sales Associate ': 245,\n", " 'Administrative Specialist': 245,\n", " 'Senior Network Engineer': 244,\n", " 'respiratory therapist': 243,\n", " 'Practice Administrator': 242,\n", " 'Lead Teacher': 242,\n", " 'Sr Manager': 242,\n", " 'Account Director': 242,\n", " 'Kitchen Manager': 241,\n", " 'Market Manager': 241,\n", " 'accounting': 241,\n", " 'Pharmacist ': 240,\n", " 'manager ': 240,\n", " 'CIO': 240,\n", " 'Senior Buyer': 239,\n", " 'Lead Technician': 238,\n", " 'Military': 238,\n", " 'administrator': 238,\n", " 'dental assistant': 237,\n", " 'Flight attendant': 237,\n", " 'Business Office Manager': 236,\n", " 'Administrative Manager': 236,\n", " 'owner operator': 236,\n", " 'Math Teacher': 236,\n", " 'Test Engineer': 236,\n", " 'Journeyman': 235,\n", " 'Owner operator': 234,\n", " 'CNC Machinist': 234,\n", " 'HHA': 234,\n", " 'Software Architect': 234,\n", " 'Director of Engineering': 234,\n", " 'Financial Consultant': 233,\n", " 'Wells Fargo': 233,\n", " 'Retail Manager': 233,\n", " 'Security Manager': 233,\n", " 'CMA': 233,\n", " 'POLICE OFFICER': 233,\n", " 'security': 232,\n", " 'Claims Manager': 232,\n", " 'Restaurant Manager': 232,\n", " 'Operations Coordinator': 232,\n", " 'Claims Examiner': 232,\n", " 'Materials Manager': 231,\n", " 'Pharmacy technician': 231,\n", " 'Firefighter ': 231,\n", " 'Assistant Project Manager': 231,\n", " 'Computer Technician': 231,\n", " 'Production manager': 230,\n", " 'Surgical Tech': 229,\n", " 'Logistics Coordinator': 229,\n", " 'Senior Developer': 229,\n", " 'Program manager': 229,\n", " 'Executive Secretary': 229,\n", " 'Chief Technology Officer': 229,\n", " 'Concierge': 228,\n", " 'Assembly ': 228,\n", " 'Co-Owner': 228,\n", " 'Supply Chain Manager': 228,\n", " 'PCA': 228,\n", " 'Lecturer': 228,\n", " 'Vice President of Operations': 227,\n", " 'Research Analyst': 227,\n", " 'Tech Support': 227,\n", " 'Adjuster': 226,\n", " 'pharmacy technician': 226,\n", " 'Executive Director ': 226,\n", " 'Home Health Aide': 226,\n", " 'correction officer': 225,\n", " 'Transportation Manager': 225,\n", " 'NURSE': 225,\n", " 'Loader': 225,\n", " 'Vice president': 224,\n", " 'Transportation': 224,\n", " 'QA Analyst': 224,\n", " 'Customer Service Representative ': 224,\n", " 'maintenance supervisor': 224,\n", " 'case manager': 223,\n", " 'Laborer ': 223,\n", " 'Sr. Business Analyst': 223,\n", " 'Electronics Technician': 223,\n", " 'Packer': 222,\n", " 'Flight Attendant ': 222,\n", " 'Claims Specialist': 222,\n", " 'Office': 221,\n", " 'Teacher Assistant': 221,\n", " 'Veterinary Technician': 221,\n", " 'Finance manager': 221,\n", " 'teller': 221,\n", " 'Department of Defense': 221,\n", " 'Quality Analyst': 221,\n", " 'Detention Officer': 220,\n", " 'Area Sales Manager': 220,\n", " 'BRANCH MANAGER': 220,\n", " 'IT Technician': 220,\n", " 'Collector': 220,\n", " 'Technical Specialist': 218,\n", " 'Communications Manager': 218,\n", " 'Lab Manager': 218,\n", " 'Auto Technician': 218,\n", " 'Risk Manager': 217,\n", " 'Contract Manager': 217,\n", " 'Payroll Administrator': 217,\n", " 'Forklift Operator': 217,\n", " 'IT Support': 217,\n", " 'Senior Paralegal': 217,\n", " 'E6': 216,\n", " 'PROJECT MANAGER': 216,\n", " 'Clinical Supervisor': 216,\n", " 'Surgical Technologist': 215,\n", " 'Sales Agent': 215,\n", " 'Judge': 214,\n", " 'Sr Project Manager': 213,\n", " 'IBM': 213,\n", " 'Millwright': 213,\n", " 'Cleaner': 213,\n", " 'Clinical Director': 212,\n", " 'Director of Operations ': 212,\n", " 'Customer service rep': 212,\n", " 'Shop Manager': 212,\n", " 'Warehouse Associate': 211,\n", " 'Senior Underwriter': 211,\n", " 'Care Giver': 211,\n", " 'professor': 211,\n", " 'Pharmacy technician ': 210,\n", " 'ENGINEER': 210,\n", " 'customer service rep': 210,\n", " 'Counselor ': 210,\n", " 'partner': 209,\n", " 'E-6': 209,\n", " 'equipment operator': 208,\n", " 'salesman': 208,\n", " 'Lead Engineer': 208,\n", " 'software engineer': 208,\n", " 'Payroll': 208,\n", " 'Assistant Superintendent': 208,\n", " 'Sr. Financial Analyst': 208,\n", " 'Production Coordinator': 208,\n", " 'Nurse practitioner ': 208,\n", " 'Front Desk': 207,\n", " 'Lab tech': 207,\n", " 'Legal Assistant ': 207,\n", " 'Trader': 207,\n", " 'Sales associate ': 207,\n", " 'counselor': 207,\n", " 'President/CEO': 207,\n", " 'Software engineer': 206,\n", " 'Deputy Clerk': 206,\n", " 'Senior Program Manager': 206,\n", " 'owner ': 206,\n", " 'Mail Handler': 205,\n", " 'Appraiser': 205,\n", " 'Doorman': 205,\n", " 'United States Air Force': 205,\n", " 'Technical Director': 204,\n", " 'RN Supervisor': 204,\n", " 'OPERATIONS MANAGER': 204,\n", " 'Personal Trainer': 204,\n", " 'Asst. Manager': 204,\n", " 'Personal banker': 204,\n", " 'Compliance Analyst': 203,\n", " 'Team lead': 203,\n", " 'Coordinator ': 203,\n", " 'PTA': 203,\n", " 'Lieutenant ': 203,\n", " 'Technical Analyst': 202,\n", " 'Service advisor': 202,\n", " 'Journeyman Electrician': 202,\n", " 'Pressman': 202,\n", " 'Sr. Systems Engineer': 201,\n", " 'Executive Administrative Assistant': 201,\n", " 'Assistant Property Manager': 201,\n", " 'Barber': 200,\n", " 'Housekeeping ': 200,\n", " 'forman': 200,\n", " 'Physician ': 200,\n", " 'United States Postal Service': 200,\n", " 'service technician': 200,\n", " 'Maintenance supervisor': 200,\n", " 'Crane Operator': 199,\n", " 'Information Technology': 199,\n", " 'State of California': 199,\n", " 'stocker': 199,\n", " 'dental hygienist': 198,\n", " 'Financial Manager': 198,\n", " 'Hair Stylist': 198,\n", " 'Financial advisor': 198,\n", " 'Quality Inspector': 198,\n", " 'Shipping Manager': 198,\n", " 'Biller': 197,\n", " 'DIRECTOR': 197,\n", " 'assembly': 197,\n", " 'Audit Manager': 197,\n", " 'Corrections officer': 197,\n", " 'State Trooper': 197,\n", " 'Electronic Technician': 196,\n", " 'Service technician': 196,\n", " 'Doctor': 196,\n", " 'Chief of Staff': 196,\n", " 'Clinician': 196,\n", " 'personal banker': 195,\n", " 'Tax Accountant': 195,\n", " 'carrier': 195,\n", " 'Photographer': 195,\n", " 'housekeeping': 195,\n", " 'Quality Assurance Specialist': 195,\n", " 'Service technician ': 194,\n", " 'Team Manager': 194,\n", " 'Guidance Counselor': 194,\n", " 'Underwriter ': 194,\n", " 'Funeral Director': 194,\n", " 'Solution Architect': 194,\n", " 'Csr': 193,\n", " 'Dispatch': 193,\n", " 'finance manager': 193,\n", " 'Registered Dental Hygienist': 193,\n", " 'Researcher': 193,\n", " 'Asset Manager': 193,\n", " 'Optometrist': 192,\n", " 'Dean': 192,\n", " 'Coach': 192,\n", " 'Sr Software Engineer': 192,\n", " 'Respiratory Therapist ': 192,\n", " 'Pharmacy tech': 192,\n", " 'Property manager': 192,\n", " 'waitress': 191,\n", " 'Associate ': 191,\n", " 'General Foreman': 191,\n", " 'Customer Service Specialist': 190,\n", " 'Team Member': 190,\n", " 'General Counsel': 190,\n", " 'Staff Attorney': 190,\n", " 'corrections officer': 189,\n", " 'Pharmacy Technician ': 189,\n", " 'Associate Manager': 189,\n", " 'MRI Technologist': 189,\n", " 'underwriter': 189,\n", " 'nurse practitioner': 189,\n", " 'District Manager ': 189,\n", " 'Law Enforcement': 189,\n", " 'waiter': 188,\n", " 'Vice President of Sales': 188,\n", " 'Caseworker': 188,\n", " 'Certified Medical Assistant': 187,\n", " 'Claims Representative': 187,\n", " 'Paraprofessional ': 187,\n", " 'Group Leader': 187,\n", " 'Project Director': 187,\n", " 'CUSTOMER SERVICE': 187,\n", " 'Register Nurse': 187,\n", " 'Major': 186,\n", " 'pharmacy tech': 186,\n", " ...}"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"emp_title\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### home ownership"]}, {"cell_type": "code", "execution_count": 54, "id": "1c3c835b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['RENT', '<PERSON>OR<PERSON><PERSON><PERSON>', 'OWN', 'ANY', 'NONE', 'OTHER'], dtype=object)"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"home_ownership\"].unique()"]}, {"cell_type": "code", "execution_count": 55, "id": "a90b76bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'MORTGAGE': 1111450,\n", " 'RENT': 894929,\n", " 'OWN': 253057,\n", " 'ANY': 996,\n", " 'OTHER': 182,\n", " 'NONE': 54}"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"home_ownership\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### Verification Status"]}, {"cell_type": "code", "execution_count": 56, "id": "1c3c835b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Not Verified', 'Source Verified', 'Verified'], dtype=object)"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"verification_status\"].unique()"]}, {"cell_type": "code", "execution_count": 57, "id": "a90b76bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Source Verified': 886231, 'Not Verified': 744806, 'Verified': 629631}"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(df[\"verification_status\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### fico range low"]}, {"cell_type": "code", "execution_count": 63, "id": "1c3c835b", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'fico_range_low'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31mKeyError\u001b[0m: 'fico_range_low'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[63], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfico_range_low\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39munique()\n", "File \u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31mKeyError\u001b[0m: 'fico_range_low'"]}], "source": ["df[\"fico_range_low\"].unique()"]}, {"cell_type": "code", "execution_count": 64, "id": "a90b76bc", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'fico_range_low'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31mKeyError\u001b[0m: 'fico_range_low'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[64], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mdict\u001b[39m(\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfico_range_low\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mvalue_counts())\n", "File \u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31mKeyError\u001b[0m: 'fico_range_low'"]}], "source": ["dict(df[\"fico_range_low\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"fico_range_high\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"fico_range_high\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"delinq_2yrs\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"delinq_2yrs\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"inq_last_6mths\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"inq_last_6mths\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"pub_rec\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"pub_rec\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"revol_bal\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"revol_bal\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"revol_util\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"revol_util\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"purpose\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"purpose\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"title\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"title\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"addr_state\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"addr_state\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"loan_status\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_status\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"fico_avg\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"fico_avg\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"loan_to_income_ratio\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_to_income_ratio\"].value_counts())"]}, {"cell_type": "markdown", "id": "bf5a1602", "metadata": {}, "source": ["#### dti"]}, {"cell_type": "code", "execution_count": null, "id": "1c3c835b", "metadata": {}, "outputs": [], "source": ["df[\"emp_length_numeric\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "a90b76bc", "metadata": {}, "outputs": [], "source": ["dict(df[\"emp_length_numeric\"].value_counts())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}