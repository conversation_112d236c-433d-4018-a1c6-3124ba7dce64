#!/usr/bin/env python3
"""
Test script to verify quota error handling.
"""

from src.models.llm_response import LoanDecision
from src.models.loan_data import LoanData
from src.providers.base import LLMConfig
from src.approaches.single_frontier import SingleFrontierApproach
from src.providers.google_provider import GoogleProvider, QuotaExceededException
import sys
import os
from unittest.mock import Mock, patch

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_quota_error_handling():
    """Test that quota errors are handled properly."""
    print("🧪 Testing Quota Error Handling")
    print("=" * 50)

    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )

    # Create a mock provider that raises quota errors
    provider = Mock(spec=GoogleProvider)
    provider.model_name = "gemini-2.0-flash"
    provider.config = config

    # Mock the generate method to raise quota error
    def mock_generate(prompt):
        raise Exception("Quota exceeded: Resource exhausted")

    provider.generate = mock_generate

    # Create the approach
    approach = SingleFrontierApproach(provider)

    # Create test loan data
    loan_data = LoanData(
        loan_amnt=10000.0,
        int_rate=12.5,
        grade="B",
        sub_grade="B2",
        annual_inc=50000.0,
        dti=15.0,
        fico_range_low=700,
        fico_range_high=720
    )

    print("🔄 Testing quota error response...")

    # Test the prediction
    result = approach.predict_single(loan_data)

    print(f"✅ Result received:")
    print(f"   Decision: {result.decision.value}")
    print(f"   Confidence: {result.confidence.value}")
    print(f"   Risk Assessment: {result.risk_assessment.value}")
    print(f"   Reasoning: {result.reasoning[:100]}...")

    # Verify the result
    if result.decision == LoanDecision.CONDITIONAL:
        print("✅ PASS: Quota error correctly handled with CONDITIONAL decision")
    else:
        print(f"❌ FAIL: Expected CONDITIONAL, got {result.decision.value}")
        return False

    if "quota" in result.reasoning.lower():
        print("✅ PASS: Reasoning correctly mentions quota issue")
    else:
        print("❌ FAIL: Reasoning doesn't mention quota issue")
        return False

    return True


def test_retry_logic():
    """Test the retry logic with exponential backoff."""
    print("\n🧪 Testing Retry Logic")
    print("=" * 50)

    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )

    # Create a mock provider
    provider = Mock(spec=GoogleProvider)
    provider.model_name = "gemini-2.0-flash"
    provider.config = config

    # Track call count
    call_count = 0

    def mock_generate(prompt):
        nonlocal call_count
        call_count += 1
        if call_count <= 2:  # Fail first 2 attempts
            raise Exception("Rate limit exceeded")
        else:  # Succeed on 3rd attempt
            return {
                "success": True,
                "response": '{"decision": "APPROVE", "confidence": "MEDIUM", "risk_assessment": "LOW", "reasoning": "Test response", "key_factors": ["Test"], "positive_factors": ["Test"], "negative_factors": []}'
            }

    provider.generate = mock_generate

    # Create the approach
    approach = SingleFrontierApproach(provider)

    # Create test loan data
    loan_data = LoanData(
        loan_amnt=10000.0,
        int_rate=12.5,
        grade="B",
        sub_grade="B2",
        annual_inc=50000.0,
        dti=15.0,
        fico_range_low=700,
        fico_range_high=720
    )

    print("🔄 Testing retry logic (should succeed on 3rd attempt)...")

    # Test the prediction
    result = approach.predict_single(loan_data)

    print(f"✅ Result received after {call_count} attempts:")
    print(f"   Decision: {result.decision.value}")
    print(f"   Confidence: {result.confidence.value}")

    if call_count == 3:
        print("✅ PASS: Retry logic worked correctly (3 attempts)")
        return True
    else:
        print(f"❌ FAIL: Expected 3 attempts, got {call_count}")
        return False


def main():
    """Run all tests."""
    print("🏦 Quota Error Handling Test Suite")
    print("=" * 60)

    tests_passed = 0
    total_tests = 2

    # Test 1: Quota error handling
    if test_quota_error_handling():
        tests_passed += 1

    # Test 2: Retry logic
    if test_retry_logic():
        tests_passed += 1

    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
