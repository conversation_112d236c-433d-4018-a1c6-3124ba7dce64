{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Loan Approval Prediction - Data Preprocessing & Feature Analysis\n", "\n", "This notebook provides comprehensive preprocessing steps for the Lending Club loan dataset and identifies the most impactful features for loan approval decisions."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder\n", "from sklearn.impute import SimpleImputer, KNNImputer\n", "from sklearn.feature_selection import SelectKBest, chi2, f_classif\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "import pathlib as pl\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "plt.style.use('seaborn-v0_8')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('64bit', 'WindowsPE')\n"]}], "source": ["# Make sure the platform architecture is x64 to allow the use of more than 4Gb of RAM\n", "import platform\n", "print(platform.architecture())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Initial Assessment"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading loan data...\n", "Memory usage: 5942.30 MB\n", "CPU times: total: 52.6 s\n", "Wall time: 1min 3s\n"]}, {"data": {"text/plain": ["(2260668, 145)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "\n", "# Load data efficiently for large dataset\n", "def load_loan_data(file_path, chunk_size=10000):\n", "    \"\"\"\n", "    Load large CSV file in chunks to handle memory efficiently\n", "    \"\"\"\n", "    chunks = []\n", "    for chunk in pd.read_csv(file_path, chunksize=chunk_size):\n", "        chunks.append(chunk)\n", "    return pd.concat(chunks, ignore_index=True)\n", "\n", "# Load the dataset\n", "# DATA_PATH = \"../data/Lending Club loan data/loan.csv\"\n", "DATA_PATH = pl.Path.cwd() / \"data\" / \"Lending Club loan data\" / \"loan.csv\"\n", "print(\"Loading loan data...\")\n", "df = load_loan_data(DATA_PATH)\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "df.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==============================\n", "DATA QUALITY ASSESSMENT\n", "==============================\n", "Dataset shape: (2260668, 145)\n", "\n", "Columns with missing values: 113\n", "\n", "Columns Missing data analysis sorted by missing percentage:\n", "                                                                                Column  Missing_Count  Missing_Percentage Data_Type\n", "id                                                                                  id        2260668          100.000000   float64\n", "url                                                                                url        2260668          100.000000   float64\n", "member_id                                                                    member_id        2260668          100.000000   float64\n", "orig_projected_additional_accrued_interest  orig_projected_additional_accrued_interest        2252242           99.627278   float64\n", "hardship_length                                                        hardship_length        2250055           99.530537   float64\n", "hardship_reason                                                        hardship_reason        2250055           99.530537    object\n", "hardship_status                                                        hardship_status        2250055           99.530537    object\n", "deferral_term                                                            deferral_term        2250055           99.530537   float64\n", "hardship_amount                                                        hardship_amount        2250055           99.530537   float64\n", "hardship_start_date                                                hardship_start_date        2250055           99.530537    object\n", "hardship_end_date                                                    hardship_end_date        2250055           99.530537    object\n", "payment_plan_start_date                                        payment_plan_start_date        2250055           99.530537    object\n", "hardship_loan_status                                              hardship_loan_status        2250055           99.530537    object\n", "hardship_dpd                                                              hardship_dpd        2250055           99.530537   float64\n", "hardship_payoff_balance_amount                          hardship_payoff_balance_amount        2250055           99.530537   float64\n", "hardship_last_payment_amount                              hardship_last_payment_amount        2250055           99.530537   float64\n", "hardship_type                                                            hardship_type        2250055           99.530537    object\n", "debt_settlement_flag_date                                    debt_settlement_flag_date        2227612           98.537777    object\n", "settlement_status                                                    settlement_status        2227612           98.537777    object\n", "settlement_date                                                        settlement_date        2227612           98.537777    object\n", "settlement_amount                                                    settlement_amount        2227612           98.537777   float64\n", "settlement_percentage                                            settlement_percentage        2227612           98.537777   float64\n", "settlement_term                                                        settlement_term        2227612           98.537777   float64\n", "sec_app_mths_since_last_major_derog                sec_app_mths_since_last_major_derog        2224726           98.410116   float64\n", "sec_app_revol_util                                                  sec_app_revol_util        2154484           95.302981   float64\n", "revol_bal_joint                                                        revol_bal_joint        2152648           95.221766   float64\n", "sec_app_open_acc                                                      sec_app_open_acc        2152647           95.221722   float64\n", "sec_app_chargeoff_within_12_mths                      sec_app_chargeoff_within_12_mths        2152647           95.221722   float64\n", "sec_app_open_act_il                                                sec_app_open_act_il        2152647           95.221722   float64\n", "sec_app_collections_12_mths_ex_med                  sec_app_collections_12_mths_ex_med        2152647           95.221722   float64\n", "sec_app_mort_acc                                                      sec_app_mort_acc        2152647           95.221722   float64\n", "sec_app_inq_last_6mths                                          sec_app_inq_last_6mths        2152647           95.221722   float64\n", "sec_app_earliest_cr_line                                      sec_app_earliest_cr_line        2152647           95.221722    object\n", "sec_app_num_rev_accts                                            sec_app_num_rev_accts        2152647           95.221722   float64\n", "verification_status_joint                                    verification_status_joint        2144938           94.880717    object\n", "dti_joint                                                                    dti_joint        2139962           94.660605   float64\n", "annual_inc_joint                                                      annual_inc_joint        2139958           94.660428   float64\n", "desc                                                                              desc        2134603           94.423551    object\n", "mths_since_last_record                                          mths_since_last_record        1901512           84.112837   float64\n", "mths_since_recent_bc_dlq                                      mths_since_recent_bc_dlq        1740967           77.011175   float64\n", "mths_since_last_major_derog                                mths_since_last_major_derog        1679893           74.309585   float64\n", "mths_since_recent_revol_delinq                          mths_since_recent_revol_delinq        1520309           67.250432   float64\n", "next_pymnt_d                                                              next_pymnt_d        1303607           57.664681    object\n", "mths_since_last_delinq                                          mths_since_last_delinq        1158502           51.246003   float64\n", "il_util                                                                        il_util        1068850           47.280273   float64\n", "mths_since_rcnt_il                                                  mths_since_rcnt_il         909924           40.250227   float64\n", "all_util                                                                      all_util         866348           38.322655   float64\n", "inq_last_12m                                                              inq_last_12m         866130           38.313012   float64\n", "total_cu_tl                                                                total_cu_tl         866130           38.313012   float64\n", "open_acc_6m                                                                open_acc_6m         866130           38.313012   float64\n", "open_il_24m                                                                open_il_24m         866129           38.312968   float64\n", "open_act_il                                                                open_act_il         866129           38.312968   float64\n", "inq_fi                                                                          inq_fi         866129           38.312968   float64\n", "max_bal_bc                                                                  max_bal_bc         866129           38.312968   float64\n", "open_rv_24m                                                                open_rv_24m         866129           38.312968   float64\n", "open_rv_12m                                                                open_rv_12m         866129           38.312968   float64\n", "total_bal_il                                                              total_bal_il         866129           38.312968   float64\n", "open_il_12m                                                                open_il_12m         866129           38.312968   float64\n", "mths_since_recent_inq                                            mths_since_recent_inq         295435           13.068482   float64\n", "emp_title                                                                    emp_title         166969            7.385826    object\n", "num_tl_120dpd_2m                                                      num_tl_120dpd_2m         153657            6.796973   float64\n", "emp_length                                                                  emp_length         146907            6.498389    object\n", "mo_sin_old_il_acct                                                  mo_sin_old_il_acct         139071            6.151766   float64\n", "bc_util                                                                        bc_util          76071            3.364979   float64\n", "percent_bc_gt_75                                                      percent_bc_gt_75          75379            3.334368   float64\n", "bc_open_to_buy                                                          bc_open_to_buy          74935            3.314728   float64\n", "mths_since_recent_bc                                              mths_since_recent_bc          73412            3.247359   float64\n", "pct_tl_nvr_dlq                                                          pct_tl_nvr_dlq          70431            3.115495   float64\n", "avg_cur_bal                                                                avg_cur_bal          70346            3.111735   float64\n", "num_rev_accts                                                            num_rev_accts          70277            3.108683   float64\n", "mo_sin_rcnt_rev_tl_op                                            mo_sin_rcnt_rev_tl_op          70277            3.108683   float64\n", "mo_sin_old_rev_tl_op                                              mo_sin_old_rev_tl_op          70277            3.108683   float64\n", "num_tl_90g_dpd_24m                                                  num_tl_90g_dpd_24m          70276            3.108639   float64\n", "num_actv_rev_tl                                                        num_actv_rev_tl          70276            3.108639   float64\n", "tot_coll_amt                                                              tot_coll_amt          70276            3.108639   float64\n", "tot_cur_bal                                                                tot_cur_bal          70276            3.108639   float64\n", "total_rev_hi_lim                                                      total_rev_hi_lim          70276            3.108639   float64\n", "num_tl_op_past_12m                                                  num_tl_op_past_12m          70276            3.108639   float64\n", "num_op_rev_tl                                                            num_op_rev_tl          70276            3.108639   float64\n", "num_il_tl                                                                    num_il_tl          70276            3.108639   float64\n", "mo_sin_rcnt_tl                                                          mo_sin_rcnt_tl          70276            3.108639   float64\n", "num_accts_ever_120_pd                                            num_accts_ever_120_pd          70276            3.108639   float64\n", "num_actv_bc_tl                                                          num_actv_bc_tl          70276            3.108639   float64\n", "num_rev_tl_bal_gt_0                                                num_rev_tl_bal_gt_0          70276            3.108639   float64\n", "num_tl_30dpd                                                              num_tl_30dpd          70276            3.108639   float64\n", "tot_hi_cred_lim                                                        tot_hi_cred_lim          70276            3.108639   float64\n", "num_bc_tl                                                                    num_bc_tl          70276            3.108639   float64\n", "total_il_high_credit_limit                                  total_il_high_credit_limit          70276            3.108639   float64\n", "num_bc_sats                                                                num_bc_sats          58590            2.591712   float64\n", "num_sats                                                                      num_sats          58590            2.591712   float64\n", "acc_open_past_24mths                                              acc_open_past_24mths          50030            2.213063   float64\n", "mort_acc                                                                      mort_acc          50030            2.213063   float64\n", "total_bc_limit                                                          total_bc_limit          50030            2.213063   float64\n", "total_bal_ex_mort                                                    total_bal_ex_mort          50030            2.213063   float64\n", "title                                                                            title          23326            1.031819    object\n", "last_pymnt_d                                                              last_pymnt_d           2426            0.107313    object\n", "revol_util                                                                  revol_util           1802            0.079711   float64\n", "dti                                                                                dti           1711            0.075686   float64\n", "pub_rec_bankruptcies                                              pub_rec_bankruptcies           1365            0.060380   float64\n", "chargeoff_within_12_mths                                      chargeoff_within_12_mths            145            0.006414   float64\n", "collections_12_mths_ex_med                                  collections_12_mths_ex_med            145            0.006414   float64\n", "tax_liens                                                                    tax_liens            105            0.004645   float64\n", "last_credit_pull_d                                                  last_credit_pull_d             73            0.003229    object\n", "inq_last_6mths                                                          inq_last_6mths             30            0.001327   float64\n", "open_acc                                                                      open_acc             29            0.001283   float64\n", "total_acc                                                                    total_acc             29            0.001283   float64\n", "earliest_cr_line                                                      earliest_cr_line             29            0.001283    object\n", "delinq_2yrs                                                                delinq_2yrs             29            0.001283   float64\n", "delinq_amnt                                                                delinq_amnt             29            0.001283   float64\n", "acc_now_delinq                                                          acc_now_delinq             29            0.001283   float64\n", "pub_rec                                                                        pub_rec             29            0.001283   float64\n", "annual_inc                                                                  annual_inc              4            0.000177   float64\n", "zip_code                                                                      zip_code              1            0.000044    object\n", "+============================================================+\n", "\n", "Columns with non-float data type: 21\n", "\n", "Columns Missing data analysis sorted by missing percentage:\n", "                                              Column  Missing_Count  Missing_Percentage Data_Type\n", "hardship_reason                      hardship_reason        2250055           99.530537    object\n", "hardship_status                      hardship_status        2250055           99.530537    object\n", "hardship_start_date              hardship_start_date        2250055           99.530537    object\n", "hardship_end_date                  hardship_end_date        2250055           99.530537    object\n", "payment_plan_start_date      payment_plan_start_date        2250055           99.530537    object\n", "hardship_loan_status            hardship_loan_status        2250055           99.530537    object\n", "hardship_type                          hardship_type        2250055           99.530537    object\n", "debt_settlement_flag_date  debt_settlement_flag_date        2227612           98.537777    object\n", "settlement_status                  settlement_status        2227612           98.537777    object\n", "settlement_date                      settlement_date        2227612           98.537777    object\n", "sec_app_earliest_cr_line    sec_app_earliest_cr_line        2152647           95.221722    object\n", "verification_status_joint  verification_status_joint        2144938           94.880717    object\n", "desc                                            desc        2134603           94.423551    object\n", "next_pymnt_d                            next_pymnt_d        1303607           57.664681    object\n", "emp_title                                  emp_title         166969            7.385826    object\n", "emp_length                                emp_length         146907            6.498389    object\n", "title                                          title          23326            1.031819    object\n", "last_pymnt_d                            last_pymnt_d           2426            0.107313    object\n", "last_credit_pull_d                last_credit_pull_d             73            0.003229    object\n", "earliest_cr_line                    earliest_cr_line             29            0.001283    object\n", "zip_code                                    zip_code              1            0.000044    object\n"]}], "source": ["# Initial data assessment\n", "def assess_data_quality(df):\n", "    \"\"\"\n", "    Comprehensive data quality assessment\n", "    \"\"\"\n", "    print(\"=\" * 30)\n", "    print(\"DATA QUALITY ASSESSMENT\")\n", "    print(\"=\" * 30)\n", "    \n", "    print(f\"Dataset shape: {df.shape}\")\n", "    \n", "    \n", "    # Missing values analysis\n", "    missing_data = pd.DataFrame({\n", "        'Column': df.columns,\n", "        'Missing_Count': df.isnull().sum(),\n", "        'Missing_Percentage': (df.isnull().sum() / len(df)) * 100,\n", "        'Data_Type': df.dtypes\n", "    })\n", "    \n", "    missing_data = missing_data[missing_data['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)\n", "    \n", "    print(f\"\\nColumns with missing values: {len(missing_data)}\")\n", "    print(\"\\nColumns Missing data analysis sorted by missing percentage:\")\n", "    print(missing_data.to_string())\n", "    \n", "    print(\"+\" + \"=\"*60 + \"+\")\n", "\n", "    n_float_dtype = missing_data[missing_data['Data_Type'] != \"float64\"]\n", "    \n", "    print(f\"\\nColumns with non-float data type: {len(n_float_dtype)}\")\n", "    print(\"\\nColumns Missing data analysis sorted by missing percentage:\")\n", "    print(n_float_dtype.to_string())\n", "\n", "    return missing_data\n", "\n", "missing_analysis = assess_data_quality(df)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x2400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "def visualize_missing_data(df, columns_to_include=None):\n", "    \"\"\"\n", "    Visualize missing data statistics in a DataFrame.\n", "    \"\"\"\n", "    if columns_to_include is not None and len(columns_to_include) > 0:\n", "        # Filter the df to only include the specified columns\n", "        # Ensure the specified columns actually exist in the missing_df\n", "        valid_included_cols = [col for col in columns_to_include if col in df['Column'].values]\n", "        \n", "        if valid_included_cols:\n", "            # Replace df with filtered version\n", "            df = df[df['Column'].isin(valid_included_cols)]\n", "        else:\n", "            print(\"Warning: None of the specified columns to include have missing data or exist in the DataFrame.\")\n", "            print(\"Drawing all columns with missing data.\")\n", "            \n", "    # the number of rows of the df correspondes to the number of columns\n", "    num_cols_to_plot = len(df) \n", "\n", "    # Adjust the figure height based on the number of columns to plot\n", "    fig_height = max(6, min(24, num_cols_to_plot * 0.3))\n", "    plt.figure(figsize=(12, fig_height))\n", "\n", "    plt.barh(df['Column'], df['Missing_Percentage'], color='skyblue')\n", "    plt.xlabel('Missing Percentage')\n", "    plt.ylabel('Columns')\n", "    plt.title('Missing Data Statistics')\n", "    plt.gca().invert_yaxis() \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "visualize_missing_data(missing_analysis)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>hardship_reason</th>\n", "      <th>hardship_type</th>\n", "      <th>hardship_status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>95385</th>\n", "      <td>UNEMPLOYMENT</td>\n", "      <td>INTEREST ONLY-3 MONTHS DEFERRAL</td>\n", "      <td>ACTIVE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130968</th>\n", "      <td>NATURAL_DISASTER</td>\n", "      <td>INTEREST ONLY-3 MONTHS DEFERRAL</td>\n", "      <td>COMPLETED</td>\n", "    </tr>\n", "    <tr>\n", "      <th>132095</th>\n", "      <td>NATURAL_DISASTER</td>\n", "      <td>INTEREST ONLY-3 MONTHS DEFERRAL</td>\n", "      <td>COMPLETED</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148481</th>\n", "      <td>NATURAL_DISASTER</td>\n", "      <td>INTEREST ONLY-3 MONTHS DEFERRAL</td>\n", "      <td>COMPLETED</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192074</th>\n", "      <td>NATURAL_DISASTER</td>\n", "      <td>INTEREST ONLY-3 MONTHS DEFERRAL</td>\n", "      <td>COMPLETED</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         hardship_reason                    hardship_type hardship_status\n", "95385       UNEMPLOYMENT  INTEREST ONLY-3 MONTHS DEFERRAL          ACTIVE\n", "130968  NATURAL_DISASTER  INTEREST ONLY-3 MONTHS DEFERRAL       COMPLETED\n", "132095  NATURAL_DISASTER  INTEREST ONLY-3 MONTHS DEFERRAL       COMPLETED\n", "148481  NATURAL_DISASTER  INTEREST ONLY-3 MONTHS DEFERRAL       COMPLETED\n", "192074  NATURAL_DISASTER  INTEREST ONLY-3 MONTHS DEFERRAL       COMPLETED"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"hardship_reason\"].notna()][[\"hardship_reason\", \"hardship_type\", \"hardship_status\"]].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Target Variable Analysis"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["loan_status\n", "Fully Paid                                             1041952\n", "Current                                                 919695\n", "Charged Off                                             261655\n", "Late (31-120 days)                                       21897\n", "In Grace Period                                           8952\n", "Late (16-30 days)                                         3737\n", "Does not meet the credit policy. Status:<PERSON>y Paid        1988\n", "Does not meet the credit policy. Status:Charged Off        761\n", "Default                                                     31\n", "Name: count, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_status\"].value_counts()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["df_status_counts = pd.DataFrame(df['loan_status'].value_counts())\n", "status_counts = df['loan_status'].value_counts()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1041952,  919695,  261655,   21897,    8952,    3737,    1988,\n", "           761,      31], dtype=int64)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["status_counts.values"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "TARGET VARIABLE ANALYSIS - LOAN STATUS\n", "============================================================\n", "Loan Status Distribution:\n", "Fully Paid: 1,041,952 (46.09%)\n", "Current: 919,695 (40.68%)\n", "Charged Off: 261,655 (11.57%)\n", "Late (31-120 days): 21,897 (0.97%)\n", "In Grace Period: 8,952 (0.40%)\n", "Late (16-30 days): 3,737 (0.17%)\n", "Does not meet the credit policy. Status:Fully Paid: 1,988 (0.09%)\n", "Does not meet the credit policy. Status:Charged Off: 761 (0.03%)\n", "Default: 31 (0.00%)\n", "\n", "Overall Approval Rate: 86.77%\n"]}, {"data": {"image/png": "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*************************************************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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.ticker as mticker\n", "\n", "\n", "# Analyze loan_status (target variable)\n", "def analyze_target_variable(df, target_col='loan_status', target_name='Target variable', scientific_notation=False):\n", "    \"\"\"\n", "    Analyze the target variable's value distribution\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"TARGET VARIABLE ANALYSIS - LOAN STATUS\")\n", "    print(\"=\" * 60)\n", "    \n", "    status_counts = df[target_col].value_counts()\n", "    # Calculate distribution of each unique value\n", "    status_pct = df[target_col].value_counts(normalize=True) * 100\n", "    \n", "    print(\"Loan Status Distribution:\")\n", "    for status, count in status_counts.items():\n", "        pct = status_pct[status]\n", "        print(f\"{status}: {count:,} ({pct:.2f}%)\")\n", "    \n", "    # Create binary target for approval prediction\n", "    # Good loans: 'Fully Paid', 'Current' -> 1\n", "    # Bad loans: 'Charged Off', 'Default', 'Late', etc. -> 0\n", "    good_loans = ['Fully Paid', 'Current']\n", "    df['loan_approved'] = df[target_col].apply(lambda x: 1 if x in good_loans else 0)\n", "    \n", "    approval_rate = df['loan_approved'].mean() * 100\n", "    print(f\"\\nOverall Approval Rate: {approval_rate:.2f}%\")\n", "    \n", "    # Visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Original status distribution\n", "    # status_counts.plot(kind='bar', ax=ax1, color='skyblue')\n", "    # After creating your plot and before plt.show():\n", "    ax1.barh(status_counts.index, status_counts.values, color='skyblue')\n", "    if not scientific_notation:\n", "        ax1.xaxis.set_major_formatter(mticker.ScalarFormatter(useOffset=False, useMathText=False))\n", "        ax1.ticklabel_format(style='plain', axis='x')\n", "    ax1.set_title(target_name + ' Distribution')\n", "    ax1.set_xlabel('Count')\n", "    ax1.set_ylabel('Loan Status')\n", "    ax1.tick_params(axis='x', rotation=60)\n", "    ax1.invert_yaxis() \n", "    \n", "    # Binary approval distribution\n", "    approval_counts = df['loan_approved'].value_counts()\n", "    labels = ['Approved/Good', 'Not Approved/Defaulted']\n", "    ax2.pie(approval_counts.values, labels=labels, autopct='%1.1f%%', startangle=90)\n", "    ax2.set_title('Binary Loan Approval Distribution')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return df\n", "\n", "df = analyze_target_variable(df, target_name='Loan Status')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Feature Importance Analysis"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["CRITICAL_FEATURES = [\n", "    'loan_amnt', 'int_rate', 'grade', 'sub_grade', 'annual_inc', 'dti',\n", "    'fico_range_low', 'fico_range_high', 'emp_length', 'home_ownership',\n", "    'verification_status', 'purpose', 'addr_state', 'delinq_2yrs',\n", "    'inq_last_6mths', 'open_acc', 'pub_rec', 'revol_bal', 'revol_util',\n", "    'total_acc', 'collections_12_mths_ex_med', 'acc_now_delinq'\n", "]"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["visualize_missing_data(missing_analysis, columns_to_include=CRITICAL_FEATURES)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON> Missing Data"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "FEATURE IMPORTANCE ANALYSIS PREPROCESSING\n", "============================================================\n", "Initial critical features found in DataFrame: 20 out of 22\n", "No entirely empty features found to drop.\n", "+==============================+\n", "Imputing numerical columns\n", "+==============================+\n", "No numeric columns required imputation for loan_amnt.\n", "No numeric columns required imputation for int_rate.\n", "Imputed missing numeric values with median in 1 columns for annual_inc.\n", "Imputed missing numeric values with median in 1 columns for dti.\n", "Imputed missing numeric values with median in 1 columns for delinq_2yrs.\n", "Imputed missing numeric values with median in 1 columns for inq_last_6mths.\n", "Imputed missing numeric values with median in 1 columns for open_acc.\n", "Imputed missing numeric values with median in 1 columns for pub_rec.\n", "No numeric columns required imputation for revol_bal.\n", "Imputed missing numeric values with median in 1 columns for revol_util.\n", "Imputed missing numeric values with median in 1 columns for total_acc.\n", "Imputed missing numeric values with median in 1 columns for collections_12_mths_ex_med.\n", "Imputed missing numeric values with median in 1 columns for acc_now_delinq.\n", "No numeric columns required imputation for loan_approved.\n", "Imputed missing numeric values with median in 10 columns in Total.\n", "+==============================+\n", "Imputing categorical columns\n", "+==============================+\n", "No categorical columns required imputation for grade.\n", "No categorical columns required imputation for sub_grade.\n", "Imputed missing categorical values with mode/Unknown in 1 columns for emp_length.\n", "No categorical columns required imputation for home_ownership.\n", "No categorical columns required imputation for verification_status.\n", "No categorical columns required imputation for purpose.\n", "No categorical columns required imputation for addr_state.\n", "Imputed missing categorical values with mode/Unknown in 1 columns.\n", "\n", "Final features remaining for analysis: 20.\n", "------------------------------------------------------------\n"]}], "source": ["def handle_missing_values(df, target_col='loan_approved'):\n", "    \"\"\"\n", "    Cleans the input DataFrame by handling missing values for a set of critical features.\n", "    - Drops any critical feature columns that are entirely empty.\n", "    - Imputes missing values in numeric columns with the median.\n", "    - Imputes missing values in categorical columns with 'Unknown'.\n", "    - Provides detailed logs about dropped and imputed columns.\n", "    Returns the cleaned DataFrame and the list of features actually included.\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"FEATURE IMPORTANCE ANALYSIS PREPROCESSING\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Filter available critical features from whole dataframe\n", "    available_features = [col for col in CRITICAL_FEATURES if col in df.columns]\n", "    print(f\"Initial critical features found in DataFrame: {len(available_features)} out of {len(CRITICAL_FEATURES)}\")\n", "    \n", "    analysis_df = df[available_features + [target_col]].copy()\n", "\n", "    # Delete entirely empty columns (NEW IMPROVEMENT)\n", "    columns_to_drop_due_to_empty = []\n", "    for col in analysis_df.columns:\n", "        if col != target_col and analysis_df[col].isnull().all():\n", "            columns_to_drop_due_to_empty.append(col)\n", "\n", "    for critical_column in CRITICAL_FEATURES:\n", "        if critical_column in columns_to_drop_due_to_empty:\n", "            print(\"WARNING: Critical feature\", critical_column, \"is entirely empty and will be dropped.\")\n", "            print(\"Please update the CRITICAL_FEATURES list.\")\n", "\n", "    if columns_to_drop_due_to_empty:\n", "        print(f\"Dropping {len(columns_to_drop_due_to_empty)} features that are entirely empty: {columns_to_drop_due_to_empty}\")\n", "        analysis_df.drop(columns=columns_to_drop_due_to_empty, inplace=True)\n", "        # Update our list of available features to reflect the dropped columns\n", "        available_features = [col for col in available_features if col not in columns_to_drop_due_to_empty]\n", "    else:\n", "        print(\"No entirely empty features found to drop.\")\n", "    \n", "    # Handle remaining missing values (Imputation)\n", "\n", "    # Separate columns into numeric and categorical types for appropriate imputation.\n", "    numeric_cols = analysis_df.select_dtypes(include=[np.number]).columns\n", "    categorical_cols = analysis_df.select_dtypes(include=['object']).columns\n", "    \n", "    print(\"+\" + \"=\"*30 + \"+\")\n", "    print(\"Imputing numerical columns\")\n", "    print(\"+\" + \"=\"*30 + \"+\")\n", "\n", "    # Numeric columns -> Fill with median.\n", "    # The median is preferred over the mean: less sensitive to outliers.\n", "    total_imputed_numeric_count = 0\n", "    for col in numeric_cols:\n", "        imputed_numeric_count = 0\n", "        if col != target_col: # Again, don't impute the target variable\n", "            if analysis_df[col].isnull().any(): # Only attempt imputation if there are actual NaNs\n", "                analysis_df[col].fillna(analysis_df[col].median(), inplace=True)\n", "                imputed_numeric_count += 1\n", "                total_imputed_numeric_count += 1\n", "        if imputed_numeric_count > 0:\n", "            print(f\"Imputed missing numeric values with median in {imputed_numeric_count} columns for {col}.\")\n", "        else:\n", "            print(f\"No numeric columns required imputation for {col}.\")\n", "\n", "    if total_imputed_numeric_count > 0:\n", "        print(f\"Imputed missing numeric values with median in {total_imputed_numeric_count} columns in Total.\")\n", "    else:\n", "        print(\"No numeric columns required imputation.\")\n", "\n", "    print(\"+\" + \"=\"*30 + \"+\")\n", "    print(\"Imputing categorical columns\")\n", "    print(\"+\" + \"=\"*30 + \"+\")\n", "\n", "    # Categorical columns -> Fill with mode or 'Unknown'.\n", "    # The mode is the most frequent category. 'Unknown' is a fallback for very rare cases\n", "    # where even after dropping entirely empty columns, a categorical column might somehow have no mode.\n", "    total_imputed_categorical_count = 0\n", "    for col in categorical_cols:\n", "        imputed_categorical_count = 0\n", "        if analysis_df[col].isnull().any(): # Only attempt imputation if there are actual NaNs\n", "            analysis_df[col].fillna('Unknown', inplace=True)\n", "            imputed_categorical_count += 1\n", "            total_imputed_categorical_count += 1\n", "        if imputed_categorical_count > 0:\n", "            print(f\"Imputed missing categorical values with mode/Unknown in {imputed_categorical_count} columns for {col}.\")\n", "        else:\n", "            print(f\"No categorical columns required imputation for {col}.\")\n", "\n", "    if total_imputed_categorical_count > 0:\n", "        print(f\"Imputed missing categorical values with mode/Unknown in {total_imputed_categorical_count} columns.\")\n", "    else:\n", "        print(\"No categorical columns required imputation.\")\n", "\n", "    # imputed_categorical_count = 0\n", "    # for col in categorical_cols:\n", "    #     if analysis_df[col].isnull().any(): # Only attempt imputation if there are actual NaNs\n", "    #         mode_val = analysis_df[col].mode()\n", "    #         if not mode_val.empty:\n", "    #             analysis_df[col].fillna(mode_val[0], inplace=True)\n", "    #         else:\n", "    #             # Fallback if mode is empty (highly unlikely after dropping all-NaN columns, but robust)\n", "    #             analysis_df[col].fillna('Unknown', inplace=True) \n", "    #         imputed_categorical_count += 1\n", "    # if imputed_categorical_count > 0:\n", "    #     print(f\"Imputed missing categorical values with mode/Unknown in {imputed_categorical_count} columns.\")\n", "    # else:\n", "    #     print(\"No categorical columns required imputation.\")\n", "\n", "    print(f\"\\nFinal features remaining for analysis: {len(available_features)}.\")\n", "    print(\"-\" * 60)\n", "    \n", "    # The function returns the cleaned DataFrame ready for feature importance analysis,\n", "    # and the updated list of feature names actually included.\n", "    return analysis_df, available_features\n", "\n", "analysis_df, available_features = handle_missing_values(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SAVE_DATA_PATH = pl.Path.cwd() / \"data\" / \"Lending Club loan data\" / \"filtered_loan_data.csv\"\n", "df.to_csv(SAVE_DATA_PATH, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def comprehensive_preprocessing(df):\n", "    \"\"\"\n", "    Comprehensive preprocessing pipeline for loan data\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"COMPREHENSIVE PREPROCESSING PIPELINE\")\n", "    print(\"=\" * 60)\n", "    \n", "    df_processed = df.copy()\n", "    \n", "    # Step 1: Remove columns with >70% missing values\n", "    high_missing_cols = []\n", "    for col in df_processed.columns:\n", "        missing_pct = (df_processed[col].isnull().sum() / len(df_processed)) * 100\n", "        if missing_pct > 70:\n", "            high_missing_cols.append(col)\n", "    \n", "    print(f\"Removing {len(high_missing_cols)} columns with >70% missing values\")\n", "    df_processed = df_processed.drop(columns=high_missing_cols)\n", "    \n", "    # Step 2: Handle date columns\n", "    date_columns = ['issue_d', 'earliest_cr_line', 'last_pymnt_d', 'last_credit_pull_d']\n", "    for col in date_columns:\n", "        if col in df_processed.columns:\n", "            df_processed[col] = pd.to_datetime(df_processed[col], errors='coerce')\n", "            # Extract useful features from dates\n", "            if col == 'issue_d':\n", "                df_processed['issue_year'] = df_processed[col].dt.year\n", "                df_processed['issue_month'] = df_processed[col].dt.month\n", "            elif col == 'earliest_cr_line':\n", "                df_processed['credit_history_length'] = (pd.to_datetime('2018-12-31') - df_processed[col]).dt.days / 365.25\n", "    \n", "    # Step 3: Handle employment length\n", "    if 'emp_length' in df_processed.columns:\n", "        def parse_emp_length(emp_length):\n", "            if pd.isna(emp_length) or emp_length == 'n/a':\n", "                return 0\n", "            elif '< 1 year' in str(emp_length):\n", "                return 0.5\n", "            elif '10+ years' in str(emp_length):\n", "                return 10\n", "            else:\n", "                try:\n", "                    return float(str(emp_length).split()[0])\n", "                except:\n", "                    return 0\n", "        \n", "        df_processed['emp_length_numeric'] = df_processed['emp_length'].apply(parse_emp_length)\n", "    \n", "    # Step 4: Create derived features\n", "    if 'annual_inc' in df_processed.columns and 'loan_amnt' in df_processed.columns:\n", "        df_processed['loan_to_income_ratio'] = df_processed['loan_amnt'] / (df_processed['annual_inc'] + 1)\n", "    \n", "    if 'fico_range_low' in df_processed.columns and 'fico_range_high' in df_processed.columns:\n", "        df_processed['fico_avg'] = (df_processed['fico_range_low'] + df_processed['fico_range_high']) / 2\n", "    \n", "    if 'revol_bal' in df_processed.columns and 'annual_inc' in df_processed.columns:\n", "        df_processed['revol_bal_to_income'] = df_processed['revol_bal'] / (df_processed['annual_inc'] + 1)\n", "    \n", "    print(f\"Processed dataset shape: {df_processed.shape}\")\n", "    return df_processed\n", "\n", "df_processed = comprehensive_preprocessing(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Missing Value Treatment Strategy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def handle_missing_values(df, strategy='mixed'):\n", "    \"\"\"\n", "    Handle missing values with different strategies based on feature importance\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"MISSING VALUE TREATMENT\")\n", "    print(\"=\" * 60)\n", "    \n", "    df_clean = df.copy()\n", "    \n", "    # Separate numeric and categorical columns\n", "    numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()\n", "    categorical_cols = df_clean.select_dtypes(include=['object']).columns.tolist()\n", "    \n", "    # Remove target column from processing\n", "    if 'loan_approved' in numeric_cols:\n", "        numeric_cols.remove('loan_approved')\n", "    \n", "    print(f\"Numeric columns: {len(numeric_cols)}\")\n", "    print(f\"Categorical columns: {len(categorical_cols)}\")\n", "    \n", "    # Handle numeric missing values\n", "    for col in numeric_cols:\n", "        missing_pct = (df_clean[col].isnull().sum() / len(df_clean)) * 100\n", "        if missing_pct > 0:\n", "            if col in ['annual_inc', 'dti', 'revol_util']:  # Important financial metrics\n", "                # Use median for financial metrics\n", "                df_clean[col].fillna(df_clean[col].median(), inplace=True)\n", "            elif col in ['delinq_2yrs', 'inq_last_6mths', 'pub_rec', 'collections_12_mths_ex_med']:\n", "                # Use 0 for count-based metrics (assuming no record means 0)\n", "                df_clean[col].fillna(0, inplace=True)\n", "            else:\n", "                # Use median for other numeric columns\n", "                df_clean[col].fillna(df_clean[col].median(), inplace=True)\n", "    \n", "    # Handle categorical missing values\n", "    for col in categorical_cols:\n", "        missing_pct = (df_clean[col].isnull().sum() / len(df_clean)) * 100\n", "        if missing_pct > 0:\n", "            if col in ['grade', 'sub_grade']:  # Critical categorical features\n", "                # Use mode for grade-related features\n", "                mode_val = df_clean[col].mode()[0] if not df_clean[col].mode().empty else 'Unknown'\n", "                df_clean[col].fillna(mode_val, inplace=True)\n", "            else:\n", "                # Use 'Unknown' for other categorical features\n", "                df_clean[col].fillna('Unknown', inplace=True)\n", "    \n", "    # Report missing values after treatment\n", "    remaining_missing = df_clean.isnull().sum().sum()\n", "    print(f\"Remaining missing values: {remaining_missing}\")\n", "    \n", "    return df_clean\n", "\n", "df_clean = handle_missing_values(df_processed)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Feature Engineering & Encoding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def encode_categorical_features(df, target_col='loan_approved'):\n", "    \"\"\"\n", "    Encode categorical features for machine learning\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"CATEGORICAL FEATURE ENCODING\")\n", "    print(\"=\" * 60)\n", "    \n", "    df_encoded = df.copy()\n", "    \n", "    # High cardinality categorical features (use target encoding or frequency encoding)\n", "    high_cardinality_cols = ['addr_state', 'emp_title']\n", "    \n", "    # Medium cardinality categorical features (use one-hot encoding)\n", "    medium_cardinality_cols = ['grade', 'sub_grade', 'home_ownership', 'verification_status', 'purpose']\n", "    \n", "    # Handle high cardinality features with frequency encoding\n", "    for col in high_cardinality_cols:\n", "        if col in df_encoded.columns:\n", "            freq_map = df_encoded[col].value_counts().to_dict()\n", "            df_encoded[f'{col}_frequency'] = df_encoded[col].map(freq_map)\n", "            \n", "            # Target encoding for high cardinality\n", "            if target_col in df_encoded.columns:\n", "                target_mean = df_encoded.groupby(col)[target_col].mean()\n", "                df_encoded[f'{col}_target_encoded'] = df_encoded[col].map(target_mean)\n", "    \n", "    # One-hot encode medium cardinality features\n", "    for col in medium_cardinality_cols:\n", "        if col in df_encoded.columns:\n", "            # Limit to top categories to avoid too many features\n", "            top_categories = df_encoded[col].value_counts().head(10).index.tolist()\n", "            df_encoded[col] = df_encoded[col].apply(lambda x: x if x in top_categories else 'Other')\n", "            \n", "            # Create dummy variables\n", "            dummies = pd.get_dummies(df_encoded[col], prefix=col, drop_first=True)\n", "            df_encoded = pd.concat([df_encoded, dummies], axis=1)\n", "    \n", "    print(f\"Encoded dataset shape: {df_encoded.shape}\")\n", "    return df_encoded\n", "\n", "df_encoded = encode_categorical_features(df_clean)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Feature Scaling and Final Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_final_dataset(df, target_col='loan_approved'):\n", "    \"\"\"\n", "    Final dataset preparation for machine learning\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"FINAL DATASET PREPARATION\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Separate features and target\n", "    if target_col in df.columns:\n", "        X = df.drop(columns=[target_col])\n", "        y = df[target_col]\n", "    else:\n", "        X = df.copy()\n", "        y = None\n", "    \n", "    # Remove non-predictive columns\n", "    cols_to_remove = ['id', 'member_id', 'url', 'desc', 'title', 'loan_status']\n", "    cols_to_remove = [col for col in cols_to_remove if col in X.columns]\n", "    X = X.drop(columns=cols_to_remove)\n", "    \n", "    # Select only numeric columns for scaling\n", "    numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()\n", "    \n", "    # Scale numeric features\n", "    scaler = StandardScaler()\n", "    X_scaled = X.copy()\n", "    X_scaled[numeric_cols] = scaler.fit_transform(X[numeric_cols])\n", "    \n", "    print(f\"Final feature matrix shape: {X_scaled.shape}\")\n", "    if y is not None:\n", "        print(f\"Target distribution: {y.value_counts().to_dict()}\")\n", "    \n", "    return X_scaled, y, scaler\n", "\n", "X_final, y_final, scaler = prepare_final_dataset(df_encoded)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Feature Importance Analysis with Random Forest"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_feature_importance_rf(X, y, top_n=20):\n", "    \"\"\"\n", "    Analyze feature importance using Random Forest\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"RANDOM FOREST FEATURE IMPORTANCE ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    if y is None:\n", "        print(\"No target variable available for importance analysis\")\n", "        return None\n", "    \n", "    # Sample data if too large\n", "    if len(X) > 50000:\n", "        sample_idx = np.random.choice(len(X), 50000, replace=False)\n", "        X_sample = X.iloc[sample_idx]\n", "        y_sample = y.iloc[sample_idx]\n", "    else:\n", "        X_sample = X\n", "        y_sample = y\n", "    \n", "    # Train Random Forest\n", "    rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)\n", "    rf.fit(X_sample, y_sample)\n", "    \n", "    # Get feature importance\n", "    importance_df = pd.DataFrame({\n", "        'feature': X.columns,\n", "        'importance': rf.feature_importances_\n", "    }).sort_values('importance', ascending=False)\n", "    \n", "    print(f\"Top {top_n} Most Important Features:\")\n", "    print(importance_df.head(top_n))\n", "    \n", "    # Visualization\n", "    plt.figure(figsize=(12, 8))\n", "    top_features = importance_df.head(top_n)\n", "    plt.barh(range(len(top_features)), top_features['importance'])\n", "    plt.yticks(range(len(top_features)), top_features['feature'])\n", "    plt.xlabel('Feature Importance')\n", "    plt.title(f'Top {top_n} Feature Importance (Random Forest)')\n", "    plt.gca().invert_yaxis()\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return importance_df\n", "\n", "if y_final is not None:\n", "    feature_importance = analyze_feature_importance_rf(X_final, y_final)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Summary and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_preprocessing_summary():\n", "    \"\"\"\n", "    Generate comprehensive preprocessing summary and recommendations\n", "    \"\"\"\n", "    print(\"=\" * 80)\n", "    print(\"PREPROCESSING SUMMARY & RECOMMENDATIONS\")\n", "    print(\"=\" * 80)\n", "    \n", "    recommendations = \"\"\"\n", "    KEY PREPROCESSING STEPS COMPLETED:\n", "    \n", "    1. DATA QUALITY ASSESSMENT\n", "       ✓ Identified columns with high missing values (>70%) and removed them\n", "       ✓ Analyzed data types and mixed-type columns\n", "       ✓ Assessed overall data quality and memory usage\n", "    \n", "    2. TARGET VARIABLE ENGINEERING\n", "       ✓ Created binary target 'loan_approved' from loan_status\n", "       ✓ Analyzed approval rates and loan outcomes\n", "    \n", "    3. FEATURE ENGINEERING\n", "       ✓ Parsed employment length into numeric format\n", "       ✓ Created derived features (loan-to-income ratio, FICO average)\n", "       ✓ Extracted useful information from date columns\n", "    \n", "    4. MISSING VALUE TREATMENT\n", "       ✓ Used domain-specific imputation strategies\n", "       ✓ Applied median imputation for financial metrics\n", "       ✓ Used zero imputation for count-based features\n", "    \n", "    5. CATEGORICAL ENCODING\n", "       ✓ Applied frequency encoding for high-cardinality features\n", "       ✓ Used one-hot encoding for medium-cardinality features\n", "       ✓ Implemented target encoding where appropriate\n", "    \n", "    6. FEATURE SCALING\n", "       ✓ Applied StandardScaler to numeric features\n", "       ✓ Prepared final feature matrix for machine learning\n", "    \n", "    MOST IMPORTANT FEATURES FOR LOAN APPROVAL:\n", "    \n", "    PRIMARY IMPACT (Critical for Decision):\n", "    • Interest Rate (int_rate) - Higher rates indicate higher risk\n", "    • Loan Grade (grade/sub_grade) - LC's internal risk assessment\n", "    • FICO Score (fico_range_low/high) - Credit worthiness\n", "    • Debt-to-Income Ratio (dti) - Ability to repay\n", "    • Annual Income (annual_inc) - Repayment capacity\n", "    • Loan Amount (loan_amnt) - Risk exposure\n", "    \n", "    SECONDARY IMPACT (Important for Fine-tuning):\n", "    • Employment Length (emp_length) - Stability indicator\n", "    • Home Ownership (home_ownership) - Financial stability\n", "    • Verification Status (verification_status) - Income verification\n", "    • Purpose (purpose) - Loan intent and risk profile\n", "    • Delinquencies (delinq_2yrs) - Past payment behavior\n", "    • Credit Inquiries (inq_last_6mths) - Recent credit seeking\n", "    \n", "    NEXT STEPS FOR MODEL BUILDING:\n", "    \n", "    1. Split data into train/validation/test sets (70/15/15)\n", "    2. Handle class imbalance if present (SMOTE, class weights)\n", "    3. Try multiple algorithms (Random Forest, XGBoost, LightGBM)\n", "    4. Perform hyperparameter tuning\n", "    5. Evaluate using appropriate metrics (AUC-ROC, Precision-Recall)\n", "    6. Implement cross-validation for robust evaluation\n", "    7. Create feature importance plots and model interpretability\n", "    \n", "    BUSINESS IMPACT CONSIDERATIONS:\n", "    \n", "    • Focus on reducing False Positives (approving bad loans)\n", "    • Balance approval rates with risk management\n", "    • Consider regulatory compliance requirements\n", "    • Implement model monitoring for drift detection\n", "    • Ensure fairness across demographic groups\n", "    \"\"\"\n", "    \n", "    print(recommendations)\n", "\n", "generate_preprocessing_summary()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 4}