{"cells": [{"cell_type": "code", "execution_count": 2, "id": "bf47c773", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pathlib as pl"]}, {"cell_type": "code", "execution_count": 3, "id": "c0a28081", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19,55,112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (47,123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4348\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n"]}], "source": ["# Make sure jupyter lab's current directory is the project root\n", "DATA_PATH = pl.Path.cwd() / \"data\" / \"Lending Club loan data\" / \"loan.csv\"\n", "\n", "\n", "chunk_size = 10000\n", "chunks = []\n", "\n", "for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "    chunks.append(chunk)\n", "\n", "df = pd.concat(chunks, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "9968b283", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2260668, 145)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "51f91c20", "metadata": {}, "outputs": [{"data": {"text/plain": ["loan_status\n", "Fully Paid                                             1041952\n", "Current                                                 919695\n", "Charged Off                                             261655\n", "Late (31-120 days)                                       21897\n", "In Grace Period                                           8952\n", "Late (16-30 days)                                         3737\n", "Does not meet the credit policy. Status:<PERSON>y Paid        1988\n", "Does not meet the credit policy. Status:Charged Off        761\n", "Default                                                     31\n", "Name: count, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_status\"].value_counts()"]}, {"cell_type": "code", "execution_count": 6, "id": "57279620", "metadata": {}, "outputs": [{"data": {"text/plain": ["(31, 145)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_default = df[df[\"loan_status\"] == \"Default\"]\n", "df_default.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "ea0e302b", "metadata": {}, "outputs": [{"data": {"text/plain": ["31"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["nb_default = df_default.shape[0]\n", "nb_default"]}, {"cell_type": "code", "execution_count": 8, "id": "0fc61032", "metadata": {}, "outputs": [{"data": {"text/plain": ["(31, 145)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_paid = df[df[\"loan_status\"] == \"Fully Paid\"][:nb_default]\n", "df_paid.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "20acf29c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(62, 145)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_balanced = pd.concat([df_default, df_paid], ignore_index=True)\n", "df_balanced.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "1e7389fd", "metadata": {}, "outputs": [], "source": ["df_balanced.to_csv(\"data/Lending Club loan data/loan_d_fp_balanced.csv\", index=False)"]}, {"cell_type": "markdown", "id": "6dd5ac5c", "metadata": {}, "source": ["- Loan Amount: $40,000.00\n", "- Interest Rate: 14.07%\n", "- Grade: C (C3)\n", "- Annual Income: $95,000.00\n", "- Debt-to-Income Ratio: 19.59%\n", "- Home Ownership: MORTGAGE\n", "- Employment Length: 2 years\n", "- Purpose: Debt Consolidation\n", "- Credit Utilization: 9.2%\n", "- Target Variable: Default"]}, {"cell_type": "code", "execution_count": 40, "id": "fd15adc3", "metadata": {}, "outputs": [], "source": ["variables = [\"loan_amnt\", \"int_rate\", \"grade\", \"sub_grade\", \"annual_inc\", \"dti\", \"emp_length\", \"home_ownership\", \"purpose\", \"loan_status\"]"]}, {"cell_type": "code", "execution_count": 45, "id": "130d91be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Variable loan_amnt found in dataframe\n", "Variable int_rate found in dataframe\n", "Variable grade found in dataframe\n", "Variable sub_grade found in dataframe\n", "Variable annual_inc found in dataframe\n", "Variable dti found in dataframe\n", "Variable emp_length found in dataframe\n", "Variable home_ownership found in dataframe\n", "Variable purpose found in dataframe\n", "Variable loan_status found in dataframe\n"]}], "source": ["for var in variables:\n", "    try:\n", "        df_balanced[var]\n", "        print(f\"Variable {var} found in dataframe\")\n", "    except KeyError:\n", "        print(f\"Variable {var} not found in dataframe\")"]}, {"cell_type": "code", "execution_count": 50, "id": "20261cfa", "metadata": {}, "outputs": [{"data": {"text/plain": ["loan_amnt                      40000\n", "int_rate                       14.07\n", "grade                              C\n", "sub_grade                         C3\n", "annual_inc                   95000.0\n", "dti                            19.59\n", "emp_length                   2 years\n", "home_ownership              MORTGAGE\n", "purpose           debt_consolidation\n", "loan_status                  Default\n", "Name: 8, dtype: object"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["df_balanced[variables].iloc[8]"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}