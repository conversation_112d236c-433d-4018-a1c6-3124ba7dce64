# Hugging Face Inference API Integration

This document explains how to use Hugging Face models via the Inference API without downloading them locally.

## Overview

The Hugging Face Inference API allows you to use models hosted on Hugging Face Hub without downloading them to your local machine. This is particularly useful for:

- **Resource constraints**: Avoid downloading large models (GBs of data)
- **Quick experimentation**: Test models without setup time
- **Production deployment**: Use models without managing infrastructure
- **Cost efficiency**: Pay per API call instead of maintaining hardware

## Configuration

### 1. Get Your API Key

1. Go to [Hugging Face](https://huggingface.co/)
2. Sign up/login to your account
3. Go to Settings → Access Tokens
4. Create a new token with "Read" permissions
5. Copy the token

### 2. Set Environment Variable

```bash
# Linux/Mac
export HUGGINGFACE_API_KEY="your_token_here"

# Windows
set HUGGINGFACE_API_KEY=your_token_here
```

### 3. Update config.yaml

Add `use_inference_api: true` to your Hugging Face model configurations:

```yaml
models:
  poll:
    models:
      - provider: "huggingface"
        model_name: "meta-llama/Llama-3.1-8B-Instruct"
        weight: 0.3
        use_inference_api: true  # Enable Inference API
      - provider: "huggingface"
        model_name: "ProsusAI/finbert"
        weight: 0.4
        use_inference_api: true  # Enable Inference API
      - provider: "huggingface"
        model_name: "nlpaueb/sec-bert-base"
        weight: 0.3
        use_inference_api: true  # Enable Inference API
```

## Supported Models

### Financial Models
- `ProsusAI/finbert` - Financial sentiment analysis
- `nlpaueb/sec-bert-base` - SEC document analysis
- `yiyanghkust/finbert-tone` - Financial tone analysis

### General Language Models
- `meta-llama/Llama-3.1-8B-Instruct` - Instruction-following
- `microsoft/DialoGPT-medium` - Conversational AI
- `google/flan-t5-large` - Text-to-text generation

## Usage Examples

### Individual Model Usage

```python
from src.providers.factory import LLMProviderFactory
from src.providers.base import LLMConfig

# Configuration for Inference API
config = {
    "provider": "huggingface",
    "model_name": "ProsusAI/finbert",
    "temperature": 0.1,
    "max_tokens": 200,
    "use_inference_api": True,  # Key setting
    "api_key": "your_hf_token"
}

# Create provider
provider = LLMProviderFactory.create_provider("huggingface", config)

# Generate response
response = provider.generate("Analyze this financial statement...")
print(response['response'])
```

### Ensemble Usage

```python
from src.providers.factory import create_poll_ensemble

# Uses config.yaml with use_inference_api: true
ensemble = create_poll_ensemble("config.yaml")

# Generate ensemble response
response = ensemble.generate_ensemble_response(
    "Loan application analysis...",
    method="weighted_average"
)
```

## API Behavior

### Model Loading
- **Cold Start**: First request may take 10-20 seconds as model loads
- **Warm Models**: Subsequent requests are faster (1-3 seconds)
- **Auto-retry**: Code automatically retries if model is loading (503 error)

### Rate Limits
- **Free Tier**: 1,000 requests/month, rate limited
- **Pro Tier**: Higher limits, faster inference
- **Auto-retry**: Code handles 429 rate limit errors

### Response Format

#### Classification Models (FinBERT, SEC-BERT)
```json
[
  {
    "label": "POSITIVE",
    "score": 0.9234
  }
]
```

#### Generation Models (Llama, GPT)
```json
[
  {
    "generated_text": "Based on the analysis..."
  }
]
```

## Error Handling

The implementation includes robust error handling:

```python
# Automatic retries for common issues
if response.status_code == 503:  # Model loading
    time.sleep(20)
    # Retry request

if response.status_code == 429:  # Rate limit
    time.sleep(5)
    # Retry request
```

## Testing

Run the test script to verify your setup:

```bash
python test_inference_api.py
```

This will test:
- API key validation
- Individual model responses
- Ensemble functionality
- Error handling

## Comparison: Local vs Inference API

| Aspect | Local Models | Inference API |
|--------|-------------|---------------|
| **Setup Time** | Long (download GBs) | Instant |
| **First Request** | Fast | Slow (cold start) |
| **Subsequent Requests** | Fast | Fast |
| **Resource Usage** | High (GPU/RAM) | None |
| **Cost** | Hardware/electricity | Per API call |
| **Offline Usage** | Yes | No |
| **Model Updates** | Manual | Automatic |
| **Customization** | Full control | Limited |

## Best Practices

1. **API Key Security**: Never commit API keys to version control
2. **Error Handling**: Always handle API failures gracefully
3. **Caching**: Consider caching responses for repeated queries
4. **Model Selection**: Choose appropriate models for your use case
5. **Rate Limiting**: Implement backoff strategies for production use

## Troubleshooting

### Common Issues

1. **"Model not found"**: Check model name spelling and availability
2. **"Unauthorized"**: Verify API key is correct and has permissions
3. **"Model loading"**: Wait and retry, model is starting up
4. **"Rate limited"**: Reduce request frequency or upgrade plan

### Debug Mode

Enable debug logging to see API requests:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Migration Guide

### From Local to Inference API

1. Add `use_inference_api: true` to config
2. Set `HUGGINGFACE_API_KEY` environment variable
3. Remove local model files (optional)
4. Test with `test_inference_api.py`

### Hybrid Approach

You can use both modes simultaneously:

```yaml
models:
  poll:
    models:
      - provider: "huggingface"
        model_name: "local-model"
        use_inference_api: false  # Local
      - provider: "huggingface"
        model_name: "remote-model"
        use_inference_api: true   # API
```

This allows you to optimize based on model size, usage frequency, and performance requirements.
