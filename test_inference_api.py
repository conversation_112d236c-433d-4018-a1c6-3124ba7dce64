#!/usr/bin/env python3
"""
Test script to demonstrate Hugging Face Inference API usage.
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from providers.factory import LLMProviderFactory
from providers.base import LLMConfig

def test_inference_api():
    """Test Hugging Face models using Inference API."""
    
    # Check if API key is available
    api_key = os.getenv("HUGGINGFACE_API_KEY")
    if not api_key:
        print("❌ HUGGINGFACE_API_KEY environment variable not set!")
        print("Please set your Hugging Face API key:")
        print("export HUGGINGFACE_API_KEY='your_token_here'")
        return
    
    print("🚀 Testing Hugging Face Inference API...")
    print(f"API Key: {'*' * (len(api_key) - 4)}{api_key[-4:]}")
    
    # Test configurations
    test_configs = [
        {
            "provider": "huggingface",
            "model_name": "ProsusAI/finbert",
            "temperature": 0.1,
            "max_tokens": 200,
            "use_inference_api": True,
            "api_key": api_key
        },
        {
            "provider": "huggingface", 
            "model_name": "nlpaueb/sec-bert-base",
            "temperature": 0.1,
            "max_tokens": 200,
            "use_inference_api": True,
            "api_key": api_key
        },
        {
            "provider": "huggingface",
            "model_name": "meta-llama/Llama-3.1-8B-Instruct",
            "temperature": 0.1,
            "max_tokens": 200,
            "use_inference_api": True,
            "api_key": api_key
        }
    ]
    
    # Test prompt for financial analysis
    test_prompt = """
    Loan Application Analysis:
    - Applicant Income: $75,000
    - Loan Amount: $250,000
    - Credit Score: 720
    - Debt-to-Income Ratio: 28%
    - Employment: 5 years stable
    
    Please analyze this loan application and provide a recommendation.
    """
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n{'='*60}")
        print(f"Test {i}: {config['model_name']}")
        print(f"{'='*60}")
        
        try:
            # Create provider
            provider = LLMProviderFactory.create_provider("huggingface", config)
            
            # Get model info
            model_info = provider.get_model_info()
            print(f"Model Info:")
            for key, value in model_info.items():
                print(f"  {key}: {value}")
            
            print(f"\n📝 Sending prompt to {config['model_name']}...")
            
            # Generate response
            response = provider.generate(test_prompt)
            
            print(f"✅ Response received:")
            print(f"Success: {response.get('success', False)}")
            print(f"Model: {response.get('model_name', 'Unknown')}")
            print(f"Response: {response.get('response', 'No response')}")
            
            if not response.get('success', False):
                print(f"❌ Error: {response.get('error', 'Unknown error')}")
            
        except Exception as e:
            print(f"❌ Error testing {config['model_name']}: {str(e)}")
    
    print(f"\n{'='*60}")
    print("✅ Testing completed!")

def test_ensemble_inference_api():
    """Test ensemble using Inference API."""
    
    api_key = os.getenv("HUGGINGFACE_API_KEY")
    if not api_key:
        print("❌ HUGGINGFACE_API_KEY environment variable not set!")
        return
    
    print("\n🔄 Testing Ensemble with Inference API...")
    
    # Ensemble configuration
    ensemble_configs = [
        {
            "provider": "huggingface",
            "model_name": "ProsusAI/finbert",
            "weight": 0.4,
            "use_inference_api": True,
            "api_key": api_key
        },
        {
            "provider": "huggingface",
            "model_name": "nlpaueb/sec-bert-base", 
            "weight": 0.3,
            "use_inference_api": True,
            "api_key": api_key
        },
        {
            "provider": "huggingface",
            "model_name": "meta-llama/Llama-3.1-8B-Instruct",
            "weight": 0.3,
            "use_inference_api": True,
            "api_key": api_key
        }
    ]
    
    try:
        # Create ensemble
        ensemble = LLMProviderFactory.create_ensemble(ensemble_configs)
        
        test_prompt = "High-income applicant with excellent credit score applying for mortgage."
        
        print(f"📝 Testing ensemble with prompt...")
        
        # Test different ensemble methods
        methods = ["weighted_average", "majority_vote"]
        
        for method in methods:
            print(f"\n🔍 Testing {method} method:")
            response = ensemble.generate_ensemble_response(test_prompt, method=method)
            
            print(f"Success: {response.get('success', False)}")
            print(f"Method: {response.get('ensemble_method', 'Unknown')}")
            print(f"Response: {response.get('response', 'No response')[:200]}...")
            
            if 'individual_responses' in response:
                print(f"Individual model count: {len(response['individual_responses'])}")
        
    except Exception as e:
        print(f"❌ Error testing ensemble: {str(e)}")

if __name__ == "__main__":
    print("🧪 Hugging Face Inference API Test Suite")
    print("=" * 50)
    
    # Test individual models
    test_inference_api()
    
    # Test ensemble
    test_ensemble_inference_api()
    
    print("\n💡 Tips:")
    print("1. Make sure your HUGGINGFACE_API_KEY is set")
    print("2. Some models may take time to load (cold start)")
    print("3. Rate limits may apply - the code includes retry logic")
    print("4. Check https://huggingface.co/docs/api-inference for model availability")
