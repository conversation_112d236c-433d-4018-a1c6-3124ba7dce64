{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Lending Club Data Dictionary Analysis\n", "\n", "This notebook analyzes the Lending Club data dictionary and compares it with the actual loan dataset to understand:\n", "1. The structure and purpose of different sheets in the data dictionary\n", "2. Which columns from the data dictionary are present/missing in the actual loan data\n", "3. Potential reasons for missing columns\n", "\n", "**Dataset Info:**\n", "- Data Dictionary: `LCDataDictionary.xlsx` (multiple sheets)\n", "- Loan Data: `loan.csv` (~1.19 GB)\n"]}, {"cell_type": "code", "execution_count": 18, "id": "2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n", "Pandas version: 2.2.3\n", "NumPy version: 1.23.5\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set display options for better readability\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"Pandas version: {pd.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## 1. <PERSON><PERSON> and <PERSON><PERSON>ze the Data Dictionary\n", "\n", "First, we'll load the Excel data dictionary file (removing the ~$ prefix as it indicates a temporary file) and examine all sheets."]}, {"cell_type": "code", "execution_count": 19, "id": "4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data dictionary file exists: True\n", "Loan data file exists: True\n", "Loan file size: 1.11 GB\n"]}], "source": ["# Define file paths\n", "data_dir = Path(\"D:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\data\\Lending Club loan data\")\n", "dict_file = data_dir / \"LCDataDictionary.xlsx\"  # Using the main file, not the ~$ temporary file\n", "loan_file = data_dir / \"loan.csv\"\n", "\n", "# Verify files exist\n", "print(f\"Data dictionary file exists: {dict_file.exists()}\")\n", "print(f\"Loan data file exists: {loan_file.exists()}\")\n", "print(f\"Loan file size: {loan_file.stat().st_size / (1024**3):.2f} GB\")"]}, {"cell_type": "code", "execution_count": 20, "id": "5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 3 sheets in the data dictionary:\n", "1. LoanStats\n", "2. browseNotes\n", "3. RejectStats\n", "Sheet 'LoanStats': 153 rows, 2 columns\n", "Sheet 'browseNotes': 122 rows, 2 columns\n", "Sheet 'RejectStats': 9 rows, 2 columns\n"]}], "source": ["import openpyxl\n", "\n", "wb = openpyxl.load_workbook(dict_file)\n", "\n", "if \"~$\" in dict_file.name:\n", "    print(\"Temporary ~$ file detected. Please use the main Excel file for analysis.\")\n", "    exit()\n", "\n", "if sheet_names := wb.sheetnames:\n", "    print(f\"Found {len(sheet_names)} sheets in the data dictionary:\")\n", "    for i, sheet in enumerate(sheet_names, 1):\n", "        print(f\"{i}. {sheet}\")\n", "\n", "    dict_sheets = {}\n", "    # Convert each sheet to a pandas DataFrame\n", "    for sheet in sheet_names:\n", "        dict_sheets[sheet] = pd.read_excel(dict_file, sheet_name=sheet)\n", "        print(f\"Sheet '{sheet}': {dict_sheets[sheet].shape[0]} rows, {dict_sheets[sheet].shape[1]} columns\")\n", "else:\n", "    print(\"No sheets found in the data dictionary.\")\n"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## 2. Extract Column Names from Each Sheet\n", "\n", "We'll extract the first column from each sheet, which typically contains the field names/column names."]}, {"cell_type": "code", "execution_count": 27, "id": "7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==============================Sheet: {sheet_name}==============================\n", "Shape: (153, 2)\n", "Column names: ['Loan<PERSON>tatN<PERSON>', 'Description']\n", "\n", "First 3 rows:\n", "            LoanStatNew                                        Description\n", "0        acc_now_delinq  The number of accounts on which the borrower i...\n", "1  acc_open_past_24mths         Number of trades opened in past 24 months.\n", "2            addr_state  The state provided by the borrower in the loan...\n", "\n", "Extracted 151 field names from first column\n", "Sample field names: ['acc_now_delinq', 'acc_open_past_24mths', 'addr_state', 'all_util', 'annual_inc']\n", "\n", "==============================Sheet: {sheet_name}==============================\n", "Shape: (122, 2)\n", "Column names: ['BrowseNotesFile', 'Description']\n", "\n", "First 3 rows:\n", "     BrowseNotesFile                                        Description\n", "0            acceptD    The date which the borrower accepted  the offer\n", "1       accNowDelinq  The number of accounts on which the borrower i...\n", "2  accOpenPast24Mths         Number of trades opened in past 24 months.\n", "\n", "Extracted 120 field names from first column\n", "Sample field names: ['acceptD', 'accNowDelinq', 'accOpenPast24Mths', 'addrState', 'all_util']\n", "\n", "==============================Sheet: {sheet_name}==============================\n", "Shape: (9, 2)\n", "Column names: ['RejectStats File', 'Description']\n", "\n", "First 3 rows:\n", "   RejectStats File                                 Description\n", "0  Amount Requested  The total amount requested by the borrower\n", "1  Application Date        The date which the borrower applied \n", "2        Loan Title     The loan title provided by the borrower\n", "\n", "Extracted 9 field names from first column\n", "Sample field names: ['Amount Requested', 'Application Date', 'Loan Title', 'Risk_Score', 'Debt-To-Income Ratio']\n"]}], "source": ["# Extract first column from each sheet (assuming it contains field names)\n", "sheet_columns = {}\n", "\n", "for sheet_name, df in dict_sheets.items():\n", "    print(f\"\\n\" + \"=\"*30 + \"Sheet: {sheet_name}\" + \"=\"*30)\n", "    print(f\"Shape: {df.shape}\")\n", "    print(f\"Column names: {list(df.columns)}\")\n", "    \n", "    print(\"\\nFirst 3 rows:\")\n", "    print(df.head(3))\n", "    \n", "    # Extract key value data (first column)\n", "    first_col = df.iloc[:, 0].dropna().tolist()\n", "    sheet_columns[sheet_name] = first_col\n", "    print(f\"\\nExtracted {len(first_col)} field names from first column\")\n", "    print(f\"Sample field names: {first_col[:5]}\")"]}, {"cell_type": "code", "execution_count": 30, "id": "ad5d995b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'LoanStats': ['acc_now_delinq',\n", "               'acc_open_past_24mths',\n", "               'addr_state',\n", "               'all_util',\n", "               'annual_inc',\n", "               'annual_inc_joint',\n", "               'application_type',\n", "               'avg_cur_bal',\n", "               'bc_open_to_buy',\n", "               'bc_util',\n", "               'chargeoff_within_12_mths',\n", "               'collection_recovery_fee',\n", "               'collections_12_mths_ex_med',\n", "               'delinq_2yrs',\n", "               'delinq_amnt',\n", "               'desc',\n", "               'dti',\n", "               'dti_joint',\n", "               'earliest_cr_line',\n", "               'emp_length',\n", "               'emp_title',\n", "               'fico_range_high',\n", "               'fico_range_low',\n", "               'funded_amnt',\n", "               'funded_amnt_inv',\n", "               'grade',\n", "               'home_ownership',\n", "               'id',\n", "               'il_util',\n", "               'initial_list_status',\n", "               'inq_fi',\n", "               'inq_last_12m',\n", "               'inq_last_6mths',\n", "               'installment',\n", "               'int_rate',\n", "               'issue_d',\n", "               'last_credit_pull_d',\n", "               'last_fico_range_high',\n", "               'last_fico_range_low',\n", "               'last_pymnt_amnt',\n", "               'last_pymnt_d',\n", "               'loan_amnt',\n", "               'loan_status',\n", "               'max_bal_bc',\n", "               'member_id',\n", "               'mo_sin_old_il_acct',\n", "               'mo_sin_old_rev_tl_op',\n", "               'mo_sin_rcnt_rev_tl_op',\n", "               'mo_sin_rcnt_tl',\n", "               'mort_acc',\n", "               'mths_since_last_delinq',\n", "               'mths_since_last_major_derog',\n", "               'mths_since_last_record',\n", "               'mths_since_rcnt_il',\n", "               'mths_since_recent_bc',\n", "               'mths_since_recent_bc_dlq',\n", "               'mths_since_recent_inq',\n", "               'mths_since_recent_revol_delinq',\n", "               'next_pymnt_d',\n", "               'num_accts_ever_120_pd',\n", "               'num_actv_bc_tl',\n", "               'num_actv_rev_tl',\n", "               'num_bc_sats',\n", "               'num_bc_tl',\n", "               'num_il_tl',\n", "               'num_op_rev_tl',\n", "               'num_rev_accts',\n", "               'num_rev_tl_bal_gt_0',\n", "               'num_sats',\n", "               'num_tl_120dpd_2m',\n", "               'num_tl_30dpd',\n", "               'num_tl_90g_dpd_24m',\n", "               'num_tl_op_past_12m',\n", "               'open_acc',\n", "               'open_acc_6m',\n", "               'open_il_12m',\n", "               'open_il_24m',\n", "               'open_act_il',\n", "               'open_rv_12m',\n", "               'open_rv_24m',\n", "               'out_prncp',\n", "               'out_prncp_inv',\n", "               'pct_tl_nvr_dlq',\n", "               'percent_bc_gt_75',\n", "               'policy_code',\n", "               'pub_rec',\n", "               'pub_rec_bankruptcies',\n", "               'purpose',\n", "               'pymnt_plan',\n", "               'recoveries',\n", "               'revol_bal',\n", "               'revol_util',\n", "               'sub_grade',\n", "               'tax_liens',\n", "               'term',\n", "               'title',\n", "               'tot_coll_amt',\n", "               'tot_cur_bal',\n", "               'tot_hi_cred_lim',\n", "               'total_acc',\n", "               'total_bal_ex_mort',\n", "               'total_bal_il',\n", "               'total_bc_limit',\n", "               'total_cu_tl',\n", "               'total_il_high_credit_limit',\n", "               'total_pymnt',\n", "               'total_pymnt_inv',\n", "               'total_rec_int',\n", "               'total_rec_late_fee',\n", "               'total_rec_prncp',\n", "               'total_rev_hi_lim \\xa0',\n", "               'url',\n", "               'verification_status',\n", "               'verified_status_joint',\n", "               'zip_code',\n", "               'revol_bal_joint ',\n", "               'sec_app_fico_range_low ',\n", "               'sec_app_fico_range_high ',\n", "               'sec_app_earliest_cr_line ',\n", "               'sec_app_inq_last_6mths ',\n", "               'sec_app_mort_acc ',\n", "               'sec_app_open_acc ',\n", "               'sec_app_revol_util ',\n", "               'sec_app_open_act_il',\n", "               'sec_app_num_rev_accts ',\n", "               'sec_app_chargeoff_within_12_mths ',\n", "               'sec_app_collections_12_mths_ex_med ',\n", "               'sec_app_mths_since_last_major_derog ',\n", "               'hardship_flag',\n", "               'hardship_type',\n", "               'hardship_reason',\n", "               'hardship_status',\n", "               'deferral_term',\n", "               'hardship_amount',\n", "               'hardship_start_date',\n", "               'hardship_end_date',\n", "               'payment_plan_start_date',\n", "               'hardship_length',\n", "               'hardship_dpd',\n", "               'hardship_loan_status',\n", "               'orig_projected_additional_accrued_interest',\n", "               'hardship_payoff_balance_amount',\n", "               'hardship_last_payment_amount',\n", "               'disbursement_method',\n", "               'debt_settlement_flag',\n", "               'debt_settlement_flag_date',\n", "               'settlement_status',\n", "               'settlement_date',\n", "               'settlement_amount',\n", "               'settlement_percentage',\n", "               'settlement_term'],\n", " 'RejectStats': ['Amount Requested',\n", "                 'Application Date',\n", "                 'Loan Title',\n", "                 'Risk_Score',\n", "                 'Debt-To-Income Ratio',\n", "                 'Zip Code',\n", "                 'State',\n", "                 'Employment Length',\n", "                 'Policy Code'],\n", " 'browseNotes': ['acceptD',\n", "                 'accNowDelinq',\n", "                 'accOpenPast24Mths',\n", "                 'addrState',\n", "                 'all_util',\n", "                 'annual_inc_joint',\n", "                 'annualInc',\n", "                 'application_type',\n", "                 'avg_cur_bal',\n", "                 'bc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "                 'bcUtil',\n", "                 'chargeoff_within_12_mths',\n", "                 'collections_12_mths_ex_med',\n", "                 'creditPullD',\n", "                 'delinq2Yrs',\n", "                 'delinqAmnt',\n", "                 'desc',\n", "                 'dti',\n", "                 'dti_joint',\n", "                 'earliestCrLine',\n", "                 'effective_int_rate',\n", "                 'emp_title',\n", "                 'empLength',\n", "                 'expD',\n", "                 'expDefaultRate',\n", "                 'ficoRangeHigh',\n", "                 'ficoRangeLow',\n", "                 'fundedAmnt',\n", "                 'grade',\n", "                 'homeOwnership',\n", "                 'id',\n", "                 'il_util',\n", "                 'ils_exp_d',\n", "                 'initialListStatus',\n", "                 'inq_fi',\n", "                 'inq_last_12m',\n", "                 'inqLast6Mths',\n", "                 'installment',\n", "                 'intRate',\n", "                 'isIncV',\n", "                 'listD',\n", "                 'loanAmnt',\n", "                 'max_bal_bc',\n", "                 'memberId',\n", "                 'mo_sin_old_rev_tl_op',\n", "                 'mo_sin_rcnt_rev_tl_op',\n", "                 'mo_sin_rcnt_tl',\n", "                 'mortAcc',\n", "                 'msa',\n", "                 'mths_since_last_major_derog',\n", "                 'mths_since_oldest_il_open',\n", "                 'mths_since_rcnt_il',\n", "                 'mthsSinceLastDelinq',\n", "                 'mthsSinceLastRecord',\n", "                 'mthsSinceMostRecentInq',\n", "                 'mthsSinceRecentBc',\n", "                 'mthsSinceRecentLoanDelinq',\n", "                 'mthsSinceRecentRevolDelinq',\n", "                 'num_accts_ever_120_pd',\n", "                 'num_actv_bc_tl',\n", "                 'num_actv_rev_tl',\n", "                 'num_bc_sats',\n", "                 'num_bc_tl',\n", "                 'num_il_tl',\n", "                 'num_op_rev_tl',\n", "                 'num_rev_accts',\n", "                 'num_rev_tl_bal_gt_0',\n", "                 'num_sats',\n", "                 'num_tl_120dpd_2m',\n", "                 'num_tl_30dpd',\n", "                 'num_tl_90g_dpd_24m',\n", "                 'num_tl_op_past_12m',\n", "                 'open_acc_6m',\n", "                 'open_il_12m',\n", "                 'open_il_24m',\n", "                 'open_act_il',\n", "                 'open_rv_12m',\n", "                 'open_rv_24m',\n", "                 'openAcc',\n", "                 'pct_tl_nvr_dlq',\n", "                 'percentBcGt75',\n", "                 'pub_rec_bankruptcies',\n", "                 'pubRec',\n", "                 'purpose',\n", "                 'reviewStatus',\n", "                 'reviewStatusD',\n", "                 'revolBal',\n", "                 'revolUtil',\n", "                 'serviceFeeRate',\n", "                 'subGrade',\n", "                 'tax_liens',\n", "                 'term',\n", "                 'title',\n", "                 'tot_coll_amt',\n", "                 'tot_cur_bal',\n", "                 'tot_hi_cred_lim',\n", "                 'total_bal_il',\n", "                 'total_cu_tl',\n", "                 'total_il_high_credit_limit',\n", "                 'total_rev_hi_lim \\xa0',\n", "                 'totalAcc',\n", "                 'totalBalExMort',\n", "                 'totalBcLimit',\n", "                 'url',\n", "                 'verified_status_joint',\n", "                 'zip_code',\n", "                 'revol_bal_joint ',\n", "                 'sec_app_fico_range_low ',\n", "                 'sec_app_fico_range_high ',\n", "                 'sec_app_earliest_cr_line ',\n", "                 'sec_app_inq_last_6mths ',\n", "                 'sec_app_mort_acc ',\n", "                 'sec_app_open_acc ',\n", "                 'sec_app_revol_util ',\n", "                 'sec_app_open_act_il',\n", "                 'sec_app_num_rev_accts ',\n", "                 'sec_app_chargeoff_within_12_mths ',\n", "                 'sec_app_collections_12_mths_ex_med ',\n", "                 'sec_app_mths_since_last_major_derog ',\n", "                 'disbursement_method']}\n"]}], "source": ["from pprint import pprint\n", "pprint(sheet_columns)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## 3. Analyze Sheet Structure and Purpose\n", "\n", "Let's analyze what each sheet represents and provide reasoning for the data organization."]}, {"cell_type": "code", "execution_count": 28, "id": "9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SHEET ANALYSIS SUMMARY ===\n", "\n", "📊 Sheet: LoanStats\n", "   Purpose: Payment and balance related fields\n", "   Columns: 151 total, 151 unique\n", "   Duplicates: No\n", "   Sample: acc_now_delinq, acc_open_past_24mths, addr_state, all_util, annual_inc...\n", "\n", "📊 Sheet: browseNotes\n", "   Purpose: Browsable/Public loan data fields\n", "   Columns: 120 total, 120 unique\n", "   Duplicates: No\n", "   Sample: acceptD, accNowDelinq, accOpenPast24Mths, addrState, all_util...\n", "\n", "📊 Sheet: RejectStats\n", "   Purpose: Loan application/request fields\n", "   Columns: 9 total, 9 unique\n", "   Duplicates: No\n", "   Sample: Amount Requested, Application Date, Loan Title, Risk_Score, Debt-To-Income Ratio...\n"]}], "source": ["# Analyze sheet purposes and create summary\n", "sheet_analysis = {}\n", "\n", "for sheet_name, columns in sheet_columns.items():\n", "    analysis = {\n", "        'column_count': len(columns),\n", "        'sample_columns': columns[:10],  # First 10 columns as sample\n", "        'unique_columns': len(set(columns)),\n", "        'has_duplicates': len(columns) != len(set(columns))\n", "    }\n", "    \n", "    # Analyze column patterns to infer sheet purpose\n", "    column_str = ' '.join(columns).lower()\n", "    \n", "    if 'current' in sheet_name.lower():\n", "        analysis['likely_purpose'] = \"Current/Active loan data fields\"\n", "    elif 'historical' in sheet_name.lower() or 'archive' in sheet_name.lower():\n", "        analysis['likely_purpose'] = \"Historical/Archived loan data fields\"\n", "    elif 'rejected' in sheet_name.lower():\n", "        analysis['likely_purpose'] = \"Rejected loan application fields\"\n", "    elif 'browse' in sheet_name.lower():\n", "        analysis['likely_purpose'] = \"Browsable/Public loan data fields\"\n", "    else:\n", "        # Analyze content patterns\n", "        if any(term in column_str for term in ['payment', 'installment', 'balance']):\n", "            analysis['likely_purpose'] = \"Payment and balance related fields\"\n", "        elif any(term in column_str for term in ['application', 'request', 'initial']):\n", "            analysis['likely_purpose'] = \"Loan application/request fields\"\n", "        else:\n", "            analysis['likely_purpose'] = \"General loan data fields\"\n", "    \n", "    sheet_analysis[sheet_name] = analysis\n", "\n", "# Display analysis results\n", "print(\"=== SHEET ANALYSIS SUMMARY ===\")\n", "for sheet_name, analysis in sheet_analysis.items():\n", "    print(f\"\\n📊 Sheet: {sheet_name}\")\n", "    print(f\"   Purpose: {analysis['likely_purpose']}\")\n", "    print(f\"   Columns: {analysis['column_count']} total, {analysis['unique_columns']} unique\")\n", "    print(f\"   Duplicates: {'Yes' if analysis['has_duplicates'] else 'No'}\")\n", "    print(f\"   Sample: {', '.join(analysis['sample_columns'][:5])}...\")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## 4. <PERSON><PERSON> Dataset (Large File Handling)\n", "\n", "Since the loan.csv file is ~1.19 GB, we'll use efficient loading methods to handle this large dataset."]}, {"cell_type": "code", "execution_count": 31, "id": "11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Examining loan.csv structure...\n", "\n", "Sample data shape: (5, 145)\n", "\n", "Column names (145 total):\n", "  1. id\n", "  2. member_id\n", "  3. loan_amnt\n", "  4. funded_amnt\n", "  5. funded_amnt_inv\n", "  6. term\n", "  7. int_rate\n", "  8. installment\n", "  9. grade\n", " 10. sub_grade\n", " 11. emp_title\n", " 12. emp_length\n", " 13. home_ownership\n", " 14. annual_inc\n", " 15. verification_status\n", " 16. issue_d\n", " 17. loan_status\n", " 18. pymnt_plan\n", " 19. url\n", " 20. desc\n", " 21. purpose\n", " 22. title\n", " 23. zip_code\n", " 24. addr_state\n", " 25. dti\n", " 26. delinq_2yrs\n", " 27. earliest_cr_line\n", " 28. inq_last_6mths\n", " 29. mths_since_last_delinq\n", " 30. mths_since_last_record\n", " 31. open_acc\n", " 32. pub_rec\n", " 33. revol_bal\n", " 34. revol_util\n", " 35. total_acc\n", " 36. initial_list_status\n", " 37. out_prncp\n", " 38. out_prncp_inv\n", " 39. total_pymnt\n", " 40. total_pymnt_inv\n", " 41. total_rec_prncp\n", " 42. total_rec_int\n", " 43. total_rec_late_fee\n", " 44. recoveries\n", " 45. collection_recovery_fee\n", " 46. last_pymnt_d\n", " 47. last_pymnt_amnt\n", " 48. next_pymnt_d\n", " 49. last_credit_pull_d\n", " 50. collections_12_mths_ex_med\n", " 51. mths_since_last_major_derog\n", " 52. policy_code\n", " 53. application_type\n", " 54. annual_inc_joint\n", " 55. dti_joint\n", " 56. verification_status_joint\n", " 57. acc_now_delinq\n", " 58. tot_coll_amt\n", " 59. tot_cur_bal\n", " 60. open_acc_6m\n", " 61. open_act_il\n", " 62. open_il_12m\n", " 63. open_il_24m\n", " 64. mths_since_rcnt_il\n", " 65. total_bal_il\n", " 66. il_util\n", " 67. open_rv_12m\n", " 68. open_rv_24m\n", " 69. max_bal_bc\n", " 70. all_util\n", " 71. total_rev_hi_lim\n", " 72. inq_fi\n", " 73. total_cu_tl\n", " 74. inq_last_12m\n", " 75. acc_open_past_24mths\n", " 76. avg_cur_bal\n", " 77. bc_open_to_buy\n", " 78. bc_util\n", " 79. chargeoff_within_12_mths\n", " 80. delinq_amnt\n", " 81. mo_sin_old_il_acct\n", " 82. mo_sin_old_rev_tl_op\n", " 83. mo_sin_rcnt_rev_tl_op\n", " 84. mo_sin_rcnt_tl\n", " 85. mort_acc\n", " 86. mths_since_recent_bc\n", " 87. mths_since_recent_bc_dlq\n", " 88. mths_since_recent_inq\n", " 89. mths_since_recent_revol_delinq\n", " 90. num_accts_ever_120_pd\n", " 91. num_actv_bc_tl\n", " 92. num_actv_rev_tl\n", " 93. num_bc_sats\n", " 94. num_bc_tl\n", " 95. num_il_tl\n", " 96. num_op_rev_tl\n", " 97. num_rev_accts\n", " 98. num_rev_tl_bal_gt_0\n", " 99. num_sats\n", "100. num_tl_120dpd_2m\n", "101. num_tl_30dpd\n", "102. num_tl_90g_dpd_24m\n", "103. num_tl_op_past_12m\n", "104. pct_tl_nvr_dlq\n", "105. percent_bc_gt_75\n", "106. pub_rec_bankruptcies\n", "107. tax_liens\n", "108. tot_hi_cred_lim\n", "109. total_bal_ex_mort\n", "110. total_bc_limit\n", "111. total_il_high_credit_limit\n", "112. revol_bal_joint\n", "113. sec_app_earliest_cr_line\n", "114. sec_app_inq_last_6mths\n", "115. sec_app_mort_acc\n", "116. sec_app_open_acc\n", "117. sec_app_revol_util\n", "118. sec_app_open_act_il\n", "119. sec_app_num_rev_accts\n", "120. sec_app_chargeoff_within_12_mths\n", "121. sec_app_collections_12_mths_ex_med\n", "122. sec_app_mths_since_last_major_derog\n", "123. hardship_flag\n", "124. hardship_type\n", "125. hardship_reason\n", "126. hardship_status\n", "127. deferral_term\n", "128. hardship_amount\n", "129. hardship_start_date\n", "130. hardship_end_date\n", "131. payment_plan_start_date\n", "132. hardship_length\n", "133. hardship_dpd\n", "134. hardship_loan_status\n", "135. orig_projected_additional_accrued_interest\n", "136. hardship_payoff_balance_amount\n", "137. hardship_last_payment_amount\n", "138. disbursement_method\n", "139. debt_settlement_flag\n", "140. debt_settlement_flag_date\n", "141. settlement_status\n", "142. settlement_date\n", "143. settlement_amount\n", "144. settlement_percentage\n", "145. settlement_term\n", "\n", "First 3 rows:\n", "   id  member_id  loan_amnt  funded_amnt  funded_amnt_inv        term  \\\n", "0 NaN        NaN       2500         2500             2500   36 months   \n", "1 NaN        NaN      30000        30000            30000   60 months   \n", "2 NaN        NaN       5000         5000             5000   36 months   \n", "\n", "   int_rate  installment grade sub_grade       emp_title emp_length  \\\n", "0     13.56        84.92     C        C1            Chef  10+ years   \n", "1     18.94       777.23     D        D2     Postmaster   10+ years   \n", "2     17.97       180.69     D        D1  Administrative    6 years   \n", "\n", "  home_ownership  annual_inc verification_status   issue_d loan_status  \\\n", "0           RENT       55000        Not Verified  Dec-2018     Current   \n", "1       MORTGAGE       90000     Source Verified  Dec-2018     Current   \n", "2       MORTGAGE       59280     Source Verified  Dec-2018     Current   \n", "\n", "  pymnt_plan  url  desc             purpose               title zip_code  \\\n", "0          n  NaN   NaN  debt_consolidation  Debt consolidation    109xx   \n", "1          n  NaN   NaN  debt_consolidation  Debt consolidation    713xx   \n", "2          n  NaN   NaN  debt_consolidation  Debt consolidation    490xx   \n", "\n", "  addr_state    dti  delinq_2yrs earliest_cr_line  inq_last_6mths  \\\n", "0         NY  18.24            0         Apr-2001               1   \n", "1         LA  26.52            0         Jun-1987               0   \n", "2         MI  10.51            0         Apr-2011               0   \n", "\n", "   mths_since_last_delinq  mths_since_last_record  open_acc  pub_rec  \\\n", "0                     NaN                    45.0         9        1   \n", "1                    71.0                    75.0        13        1   \n", "2                     NaN                     NaN         8        0   \n", "\n", "   revol_bal  revol_util  total_acc initial_list_status  out_prncp  \\\n", "0       4341        10.3         34                   w    2386.02   \n", "1      12315        24.2         44                   w   29387.75   \n", "2       4599        19.1         13                   w    4787.21   \n", "\n", "   out_prncp_inv  total_pymnt  total_pymnt_inv  total_rec_prncp  \\\n", "0        2386.02       167.02           167.02           113.98   \n", "1       29387.75      1507.11          1507.11           612.25   \n", "2        4787.21       353.89           353.89           212.79   \n", "\n", "   total_rec_int  total_rec_late_fee  recoveries  collection_recovery_fee  \\\n", "0          53.04                 0.0         0.0                      0.0   \n", "1         894.86                 0.0         0.0                      0.0   \n", "2         141.10                 0.0         0.0                      0.0   \n", "\n", "  last_pymnt_d  last_pymnt_amnt next_pymnt_d last_credit_pull_d  \\\n", "0     Feb-2019            84.92     Mar-2019           Feb-2019   \n", "1     Feb-2019           777.23     Mar-2019           Feb-2019   \n", "2     Feb-2019           180.69     Mar-2019           Feb-2019   \n", "\n", "   collections_12_mths_ex_med  mths_since_last_major_derog  policy_code  \\\n", "0                           0                          NaN            1   \n", "1                           0                          NaN            1   \n", "2                           0                          NaN            1   \n", "\n", "  application_type  annual_inc_joint  dti_joint  verification_status_joint  \\\n", "0       Individual               NaN        NaN                        NaN   \n", "1       Individual               NaN        NaN                        NaN   \n", "2       Individual               NaN        NaN                        NaN   \n", "\n", "   acc_now_delinq  tot_coll_amt  tot_cur_bal  open_acc_6m  open_act_il  \\\n", "0               0             0        16901            2            2   \n", "1               0          1208       321915            4            4   \n", "2               0             0       110299            0            1   \n", "\n", "   open_il_12m  open_il_24m  mths_since_rcnt_il  total_bal_il  il_util  \\\n", "0            1            2                   2         12560       69   \n", "1            2            3                   3         87153       88   \n", "2            0            2                  14          7150       72   \n", "\n", "   open_rv_12m  open_rv_24m  max_bal_bc  all_util  total_rev_hi_lim  inq_fi  \\\n", "0            2            7        2137        28             42000       1   \n", "1            4            5         998        57             50800       2   \n", "2            0            2           0        35             24100       1   \n", "\n", "   total_cu_tl  inq_last_12m  acc_open_past_24mths  avg_cur_bal  \\\n", "0           11             2                     9         1878   \n", "1           15             2                    10        24763   \n", "2            5             0                     4        18383   \n", "\n", "   bc_open_to_buy  bc_util  chargeoff_within_12_mths  delinq_amnt  \\\n", "0           34360      5.9                         0            0   \n", "1           13761      8.3                         0            0   \n", "2           13800      0.0                         0            0   \n", "\n", "   mo_sin_old_il_acct  mo_sin_old_rev_tl_op  mo_sin_rcnt_rev_tl_op  \\\n", "0                 140                   212                      1   \n", "1                 163                   378                      4   \n", "2                  87                    92                     15   \n", "\n", "   mo_sin_rcnt_tl  mort_acc  mths_since_recent_bc  mths_since_recent_bc_dlq  \\\n", "0               1         0                     1                       NaN   \n", "1               3         3                     4                       NaN   \n", "2              14         2                    77                       NaN   \n", "\n", "   mths_since_recent_inq  mths_since_recent_revol_delinq  \\\n", "0                      2                             NaN   \n", "1                      4                             NaN   \n", "2                     14                             NaN   \n", "\n", "   num_accts_ever_120_pd  num_actv_bc_tl  num_actv_rev_tl  num_bc_sats  \\\n", "0                      0               2                5            3   \n", "1                      0               2                4            4   \n", "2                      0               0                3            3   \n", "\n", "   num_bc_tl  num_il_tl  num_op_rev_tl  num_rev_accts  num_rev_tl_bal_gt_0  \\\n", "0          3         16              7             18                    5   \n", "1          9         27              8             14                    4   \n", "2          3          4              6              7                    3   \n", "\n", "   num_sats  num_tl_120dpd_2m  num_tl_30dpd  num_tl_90g_dpd_24m  \\\n", "0         9                 0             0                   0   \n", "1        13                 0             0                   0   \n", "2         8                 0             0                   0   \n", "\n", "   num_tl_op_past_12m  pct_tl_nvr_dlq  percent_bc_gt_75  pub_rec_bankruptcies  \\\n", "0                   3           100.0                 0                     1   \n", "1                   6            95.0                 0                     1   \n", "2                   0           100.0                 0                     0   \n", "\n", "   tax_liens  tot_hi_cred_lim  total_bal_ex_mort  total_bc_limit  \\\n", "0          0            60124              16901           36500   \n", "1          0           372872              99468           15000   \n", "2          0           136927              11749           13800   \n", "\n", "   total_il_high_credit_limit  revol_bal_joint  sec_app_earliest_cr_line  \\\n", "0                       18124              NaN                       NaN   \n", "1                       94072              NaN                       NaN   \n", "2                       10000              NaN                       NaN   \n", "\n", "   sec_app_inq_last_6mths  sec_app_mort_acc  sec_app_open_acc  \\\n", "0                     NaN               NaN               NaN   \n", "1                     NaN               NaN               NaN   \n", "2                     NaN               NaN               NaN   \n", "\n", "   sec_app_revol_util  sec_app_open_act_il  sec_app_num_rev_accts  \\\n", "0                 NaN                  NaN                    NaN   \n", "1                 NaN                  NaN                    NaN   \n", "2                 NaN                  NaN                    NaN   \n", "\n", "   sec_app_chargeoff_within_12_mths  sec_app_collections_12_mths_ex_med  \\\n", "0                               NaN                                 NaN   \n", "1                               NaN                                 NaN   \n", "2                               NaN                                 NaN   \n", "\n", "   sec_app_mths_since_last_major_derog hardship_flag  hardship_type  \\\n", "0                                  NaN             N            NaN   \n", "1                                  NaN             N            NaN   \n", "2                                  NaN             N            NaN   \n", "\n", "   hardship_reason  hardship_status  deferral_term  hardship_amount  \\\n", "0              NaN              NaN            NaN              NaN   \n", "1              NaN              NaN            NaN              NaN   \n", "2              NaN              NaN            NaN              NaN   \n", "\n", "   hardship_start_date  hardship_end_date  payment_plan_start_date  \\\n", "0                  NaN                NaN                      NaN   \n", "1                  NaN                NaN                      NaN   \n", "2                  NaN                NaN                      NaN   \n", "\n", "   hardship_length  hardship_dpd  hardship_loan_status  \\\n", "0              NaN           NaN                   NaN   \n", "1              NaN           NaN                   NaN   \n", "2              NaN           NaN                   NaN   \n", "\n", "   orig_projected_additional_accrued_interest  hardship_payoff_balance_amount  \\\n", "0                                         NaN                             NaN   \n", "1                                         NaN                             NaN   \n", "2                                         NaN                             NaN   \n", "\n", "   hardship_last_payment_amount disbursement_method debt_settlement_flag  \\\n", "0                           NaN                Cash                    N   \n", "1                           NaN                Cash                    N   \n", "2                           NaN                Cash                    N   \n", "\n", "   debt_settlement_flag_date  settlement_status  settlement_date  \\\n", "0                        NaN                NaN              NaN   \n", "1                        NaN                NaN              NaN   \n", "2                        NaN                NaN              NaN   \n", "\n", "   settlement_amount  settlement_percentage  settlement_term  \n", "0                NaN                    NaN              NaN  \n", "1                NaN                    NaN              NaN  \n", "2                NaN                    NaN              NaN  \n"]}], "source": ["# First, let's examine the file structure without loading the entire dataset\n", "print(\"Examining loan.csv structure...\")\n", "\n", "# Read just the first few rows to understand structure\n", "sample_df = pd.read_csv(loan_file, nrows=5)\n", "print(f\"\\nSample data shape: {sample_df.shape}\")\n", "print(f\"\\nColumn names ({len(sample_df.columns)} total):\")\n", "for i, col in enumerate(sample_df.columns, 1):\n", "    print(f\"{i:3d}. {col}\")\n", "\n", "print(\"\\nFirst 3 rows:\")\n", "print(sample_df.head(3))"]}, {"cell_type": "code", "execution_count": 32, "id": "12", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counting total rows in loan.csv...\n", "Total rows in loan.csv: 2,260,668\n", "\n", "Total columns in loan.csv: 145\n", "Unique columns in loan dataset: 145\n"]}], "source": ["# Get total row count efficiently\n", "print(\"Counting total rows in loan.csv...\")\n", "row_count = sum(1 for line in open(loan_file, 'r', encoding='utf-8')) - 1  # -1 for header\n", "print(f\"Total rows in loan.csv: {row_count:,}\")\n", "\n", "# Load column names only (more memory efficient)\n", "loan_columns = list(pd.read_csv(loan_file, nrows=0).columns)\n", "print(f\"\\nTotal columns in loan.csv: {len(loan_columns)}\")\n", "\n", "# Store loan columns for comparison\n", "loan_column_set = set(loan_columns)\n", "print(f\"Unique columns in loan dataset: {len(loan_column_set)}\")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["## 5. Compare Data Dictionary with Actual Loan Data\n", "\n", "Now we'll compare each sheet's columns with the actual loan dataset to identify matches and missing columns."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COMPARISON SUMMARY ===\n", "\n", "📋 Sheet: LoanStats\n", "   Total columns in dictionary: 151\n", "   Present in loan.csv: 133 (88.1%)\n", "   Missing from loan.csv: 18 (11.9%)\n", "\n", "📋 Sheet: browseNotes\n", "   Total columns in dictionary: 120\n", "   Present in loan.csv: 58 (48.3%)\n", "   Missing from loan.csv: 62 (51.7%)\n", "\n", "📋 Sheet: RejectStats\n", "   Total columns in dictionary: 9\n", "   Present in loan.csv: 0 (0.0%)\n", "   Missing from loan.csv: 9 (100.0%)\n"]}], "source": ["# Compare each sheet with actual loan data\n", "comparison_results = {}\n", "\n", "for sheet_name, dict_columns in sheet_columns.items():\n", "    dict_column_set = set(dict_columns)\n", "    \n", "    # Find matches and missing columns\n", "    present_columns = dict_column_set.intersection(loan_column_set)\n", "    missing_columns = dict_column_set - loan_column_set\n", "    \n", "    # Calculate percentages\n", "    total_dict_cols = len(dict_column_set)\n", "    present_count = len(present_columns)\n", "    missing_count = len(missing_columns)\n", "    \n", "    present_pct = (present_count / total_dict_cols * 100) if total_dict_cols > 0 else 0\n", "    missing_pct = (missing_count / total_dict_cols * 100) if total_dict_cols > 0 else 0\n", "    \n", "    comparison_results[sheet_name] = {\n", "        'total_dict_columns': total_dict_cols,\n", "        'present_columns': present_columns,\n", "        'missing_columns': missing_columns,\n", "        'present_count': present_count,\n", "        'missing_count': missing_count,\n", "        'present_percentage': present_pct,\n", "        'missing_percentage': missing_pct\n", "    }\n", "\n", "print(\"=== COMPARISON SUMMARY ===\")\n", "for sheet_name, results in comparison_results.items():\n", "    print(f\"\\n📋 Sheet: {sheet_name}\")\n", "    print(f\"   Total columns in dictionary: {results['total_dict_columns']}\")\n", "    print(f\"   Present in loan.csv: {results['present_count']} ({results['present_percentage']:.1f}%)\")\n", "    print(f\"   Missing from loan.csv: {results['missing_count']} ({results['missing_percentage']:.1f}%)\")"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["## 6. Detailed Analysis of Missing Columns\n", "\n", "Let's examine the missing columns in detail and provide explanations for why they might be absent."]}, {"cell_type": "code", "execution_count": 38, "id": "16", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== MISSING COLUMNS ANALYSIS ===\n", "Total unique missing columns across all sheets: 75\n", "\n", "🔍 Privacy/Sensitive (1 columns):\n", "   - memberId\n", "\n", "🔍 Deprecated/Legacy (2 columns):\n", "   - mthsSinceRecentRevolDelinq\n", "   - mths_since_oldest_il_open\n", "\n", "🔍 Time-specific (1 columns):\n", "   - delinq2Yrs\n", "\n", "🔍 Other (71 columns):\n", "   - Amount Requested\n", "   - Application Date\n", "   - Debt-To-Income Ratio\n", "   - Employment Length\n", "   - Loan Title\n", "   - Policy Code\n", "   - Risk_Score\n", "   - State\n", "   - Zip Code\n", "   - accNowDelinq\n", "   - accOpenPast24Mths\n", "   - acceptD\n", "   - addrState\n", "   - annualInc\n", "   - b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "   - bc<PERSON><PERSON>\n", "   - creditPullD\n", "   - delinqAmnt\n", "   - earliestCrLine\n", "   - effective_int_rate\n", "   - emp<PERSON><PERSON><PERSON>\n", "   - expD\n", "   - expDefaultRate\n", "   - ficoRangeHigh\n", "   - ficoRangeLow\n", "   - fico_range_high\n", "   - fico_range_low\n", "   - fundedAmnt\n", "   - homeOwnership\n", "   - ils_exp_d\n", "   - initialListStatus\n", "   - inqLast6Mths\n", "   - intRate\n", "   - isIncV\n", "   - last_fico_range_high\n", "   - last_fico_range_low\n", "   - listD\n", "   - loanAmnt\n", "   - mortAcc\n", "   - msa\n", "   - mthsSinceLastDelinq\n", "   - mthsSinceLastRecord\n", "   - mthsSinceMostRecentInq\n", "   - mthsSinceRecentBc\n", "   - mthsSinceRecentLoanDelinq\n", "   - openAcc\n", "   - percentBcGt75\n", "   - pubRec\n", "   - reviewStatus\n", "   - reviewStatusD\n", "   - revolBal\n", "   - revol<PERSON><PERSON>\n", "   - revol_bal_joint \n", "   - sec_app_chargeoff_within_12_mths \n", "   - sec_app_collections_12_mths_ex_med \n", "   - sec_app_earliest_cr_line \n", "   - sec_app_fico_range_high \n", "   - sec_app_fico_range_low \n", "   - sec_app_inq_last_6mths \n", "   - sec_app_mort_acc \n", "   - sec_app_mths_since_last_major_derog \n", "   - sec_app_num_rev_accts \n", "   - sec_app_open_acc \n", "   - sec_app_revol_util \n", "   - serviceFeeRate\n", "   - subGrade\n", "   - totalAcc\n", "   - totalBalExMort\n", "   - totalBcLimit\n", "   - total_rev_hi_lim  \n", "   - verified_status_joint\n"]}], "source": ["# Analyze missing columns patterns\n", "all_missing_columns = set()\n", "for results in comparison_results.values():\n", "    all_missing_columns.update(results['missing_columns'])\n", "\n", "print(f\"=== MISSING COLUMNS ANALYSIS ===\")\n", "print(f\"Total unique missing columns across all sheets: {len(all_missing_columns)}\")\n", "\n", "# Categorize missing columns by likely reasons\n", "missing_categories = {\n", "    'Privacy/Sensitive': [],\n", "    'Deprecated/Legacy': [],\n", "    'Internal/System': [],\n", "    'Time-specific': [],\n", "    'Other': []\n", "}\n", "\n", "for col in all_missing_columns:\n", "    col_lower = col.lower()\n", "    \n", "    # Privacy/Sensitive data\n", "    if any(term in col_lower for term in ['ssn', 'social', 'address', 'phone', 'email', 'name', 'id']):\n", "        missing_categories['Privacy/Sensitive'].append(col)\n", "    # Deprecated/Legacy fields\n", "    elif any(term in col_lower for term in ['old', 'legacy', 'deprecated', 'former', 'previous']):\n", "        missing_categories['Deprecated/Legacy'].append(col)\n", "    # Internal/System fields\n", "    elif any(term in col_lower for term in ['internal', 'system', 'admin', 'process', 'workflow']):\n", "        missing_categories['Internal/System'].append(col)\n", "    # Time-specific fields\n", "    elif any(term in col_lower for term in ['2007', '2008', '2009', '2010', '2011', '2012', 'quarter', 'q1', 'q2', 'q3', 'q4']):\n", "        missing_categories['Time-specific'].append(col)\n", "    else:\n", "        missing_categories['Other'].append(col)\n", "\n", "# Display categorized missing columns\n", "for category, columns in missing_categories.items():\n", "    if columns:\n", "        print(f\"\\n🔍 {category} ({len(columns)} columns):\")\n", "        for col in sorted(columns):\n", "            print(f\"   - {col}\")"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## 7. Summary Tables and Findings\n", "\n", "Let's create comprehensive summary tables showing our findings."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COMPREHENSIVE SUMMARY TABLE ===\n", " Sheet Name                            Purpose  Total Columns  Present in loan.csv  Missing from loan.csv Present % Missing %\n", "  LoanStats Payment and balance related fields            151                  133                     18     88.1%     11.9%\n", "browseNotes  Browsable/Public loan data fields            120                   58                     62     48.3%     51.7%\n", "RejectStats    Loan application/request fields              9                    0                      9      0.0%    100.0%\n", "\n", "=== OVERALL STATISTICS ===\n", "Total columns across all dictionary sheets: 280\n", "Total present in loan.csv: 191\n", "Total missing from loan.csv: 89\n", "Overall match rate: 68.2%\n"]}], "source": ["# Create summary DataFrame for sheet comparison\n", "summary_data = []\n", "for sheet_name, results in comparison_results.items():\n", "    summary_data.append({\n", "        'Sheet Name': sheet_name,\n", "        'Purpose': sheet_analysis[sheet_name]['likely_purpose'],\n", "        'Total Columns': results['total_dict_columns'],\n", "        'Present in loan.csv': results['present_count'],\n", "        'Missing from loan.csv': results['missing_count'],\n", "        'Present %': f\"{results['present_percentage']:.1f}%\",\n", "        'Missing %': f\"{results['missing_percentage']:.1f}%\"\n", "    })\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "print(\"=== COMPREHENSIVE SUMMARY TABLE ===\")\n", "print(summary_df.to_string(index=False))\n", "\n", "total_dict_cols = sum(results['total_dict_columns'] for results in comparison_results.values())\n", "total_present = sum(results['present_count'] for results in comparison_results.values())\n", "total_missing = sum(results['missing_count'] for results in comparison_results.values())\n", "\n", "print(f\"\\n=== OVERALL STATISTICS ===\")\n", "print(f\"Total columns across all dictionary sheets: {total_dict_cols}\")\n", "print(f\"Total present in loan.csv: {total_present}\")\n", "print(f\"Total missing from loan.csv: {total_missing}\")\n", "print(f\"Overall match rate: {(total_present/total_dict_cols*100):.1f}%\")"]}, {"cell_type": "markdown", "id": "616cb676", "metadata": {}, "source": ["## Lending Club Data Dictionary Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "0e08bc05", "metadata": {}, "outputs": [{"data": {"text/plain": ["['acc_now_delinq',\n", " 'acc_open_past_24mths',\n", " 'addr_state',\n", " 'all_util',\n", " 'annual_inc',\n", " 'annual_inc_joint',\n", " 'application_type',\n", " 'avg_cur_bal',\n", " 'bc_open_to_buy',\n", " 'bc_util',\n", " 'chargeoff_within_12_mths',\n", " 'collection_recovery_fee',\n", " 'collections_12_mths_ex_med',\n", " 'delinq_2yrs',\n", " 'delinq_amnt',\n", " 'desc',\n", " 'dti',\n", " 'dti_joint',\n", " 'earliest_cr_line',\n", " 'emp_length',\n", " 'emp_title',\n", " 'fico_range_high',\n", " 'fico_range_low',\n", " 'funded_amnt',\n", " 'funded_amnt_inv',\n", " 'grade',\n", " 'home_ownership',\n", " 'id',\n", " 'il_util',\n", " 'initial_list_status',\n", " 'inq_fi',\n", " 'inq_last_12m',\n", " 'inq_last_6mths',\n", " 'installment',\n", " 'int_rate',\n", " 'issue_d',\n", " 'last_credit_pull_d',\n", " 'last_fico_range_high',\n", " 'last_fico_range_low',\n", " 'last_pymnt_amnt',\n", " 'last_pymnt_d',\n", " 'loan_amnt',\n", " 'loan_status',\n", " 'max_bal_bc',\n", " 'member_id',\n", " 'mo_sin_old_il_acct',\n", " 'mo_sin_old_rev_tl_op',\n", " 'mo_sin_rcnt_rev_tl_op',\n", " 'mo_sin_rcnt_tl',\n", " 'mort_acc',\n", " 'mths_since_last_delinq',\n", " 'mths_since_last_major_derog',\n", " 'mths_since_last_record',\n", " 'mths_since_rcnt_il',\n", " 'mths_since_recent_bc',\n", " 'mths_since_recent_bc_dlq',\n", " 'mths_since_recent_inq',\n", " 'mths_since_recent_revol_delinq',\n", " 'next_pymnt_d',\n", " 'num_accts_ever_120_pd',\n", " 'num_actv_bc_tl',\n", " 'num_actv_rev_tl',\n", " 'num_bc_sats',\n", " 'num_bc_tl',\n", " 'num_il_tl',\n", " 'num_op_rev_tl',\n", " 'num_rev_accts',\n", " 'num_rev_tl_bal_gt_0',\n", " 'num_sats',\n", " 'num_tl_120dpd_2m',\n", " 'num_tl_30dpd',\n", " 'num_tl_90g_dpd_24m',\n", " 'num_tl_op_past_12m',\n", " 'open_acc',\n", " 'open_acc_6m',\n", " 'open_il_12m',\n", " 'open_il_24m',\n", " 'open_act_il',\n", " 'open_rv_12m',\n", " 'open_rv_24m',\n", " 'out_prncp',\n", " 'out_prncp_inv',\n", " 'pct_tl_nvr_dlq',\n", " 'percent_bc_gt_75',\n", " 'policy_code',\n", " 'pub_rec',\n", " 'pub_rec_bankruptcies',\n", " 'purpose',\n", " 'pymnt_plan',\n", " 'recoveries',\n", " 'revol_bal',\n", " 'revol_util',\n", " 'sub_grade',\n", " 'tax_liens',\n", " 'term',\n", " 'title',\n", " 'tot_coll_amt',\n", " 'tot_cur_bal',\n", " 'tot_hi_cred_lim',\n", " 'total_acc',\n", " 'total_bal_ex_mort',\n", " 'total_bal_il',\n", " 'total_bc_limit',\n", " 'total_cu_tl',\n", " 'total_il_high_credit_limit',\n", " 'total_pymnt',\n", " 'total_pymnt_inv',\n", " 'total_rec_int',\n", " 'total_rec_late_fee',\n", " 'total_rec_prncp',\n", " 'total_rev_hi_lim \\xa0',\n", " 'url',\n", " 'verification_status',\n", " 'verified_status_joint',\n", " 'zip_code',\n", " 'revol_bal_joint ',\n", " 'sec_app_fico_range_low ',\n", " 'sec_app_fico_range_high ',\n", " 'sec_app_earliest_cr_line ',\n", " 'sec_app_inq_last_6mths ',\n", " 'sec_app_mort_acc ',\n", " 'sec_app_open_acc ',\n", " 'sec_app_revol_util ',\n", " 'sec_app_open_act_il',\n", " 'sec_app_num_rev_accts ',\n", " 'sec_app_chargeoff_within_12_mths ',\n", " 'sec_app_collections_12_mths_ex_med ',\n", " 'sec_app_mths_since_last_major_derog ',\n", " 'hardship_flag',\n", " 'hardship_type',\n", " 'hardship_reason',\n", " 'hardship_status',\n", " 'deferral_term',\n", " 'hardship_amount',\n", " 'hardship_start_date',\n", " 'hardship_end_date',\n", " 'payment_plan_start_date',\n", " 'hardship_length',\n", " 'hardship_dpd',\n", " 'hardship_loan_status',\n", " 'orig_projected_additional_accrued_interest',\n", " 'hardship_payoff_balance_amount',\n", " 'hardship_last_payment_amount',\n", " 'disbursement_method',\n", " 'debt_settlement_flag',\n", " 'debt_settlement_flag_date',\n", " 'settlement_status',\n", " 'settlement_date',\n", " 'settlement_amount',\n", " 'settlement_percentage',\n", " 'settlement_term',\n", " 'acceptD',\n", " 'accNowDelinq',\n", " 'accOpenPast24Mths',\n", " 'addrState',\n", " 'all_util',\n", " 'annual_inc_joint',\n", " 'annualInc',\n", " 'application_type',\n", " 'avg_cur_bal',\n", " 'bc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'bcUtil',\n", " 'chargeoff_within_12_mths',\n", " 'collections_12_mths_ex_med',\n", " 'creditPullD',\n", " 'delinq2Yrs',\n", " 'delinqAmnt',\n", " 'desc',\n", " 'dti',\n", " 'dti_joint',\n", " 'earliestCrLine',\n", " 'effective_int_rate',\n", " 'emp_title',\n", " 'empLength',\n", " 'expD',\n", " 'expDefaultRate',\n", " 'ficoRangeHigh',\n", " 'ficoRangeLow',\n", " 'fundedAmnt',\n", " 'grade',\n", " 'homeOwnership',\n", " 'id',\n", " 'il_util',\n", " 'ils_exp_d',\n", " 'initialListStatus',\n", " 'inq_fi',\n", " 'inq_last_12m',\n", " 'inqLast6Mths',\n", " 'installment',\n", " 'intRate',\n", " 'isIncV',\n", " 'listD',\n", " 'loanAmnt',\n", " 'max_bal_bc',\n", " 'memberId',\n", " 'mo_sin_old_rev_tl_op',\n", " 'mo_sin_rcnt_rev_tl_op',\n", " 'mo_sin_rcnt_tl',\n", " 'mortAcc',\n", " 'msa',\n", " 'mths_since_last_major_derog',\n", " 'mths_since_oldest_il_open',\n", " 'mths_since_rcnt_il',\n", " 'mthsSinceLastDelinq',\n", " 'mthsSinceLastRecord',\n", " 'mthsSinceMostRecentInq',\n", " 'mthsSinceRecentBc',\n", " 'mthsSinceRecentLoanDelinq',\n", " 'mthsSinceRecentRevolDelinq',\n", " 'num_accts_ever_120_pd',\n", " 'num_actv_bc_tl',\n", " 'num_actv_rev_tl',\n", " 'num_bc_sats',\n", " 'num_bc_tl',\n", " 'num_il_tl',\n", " 'num_op_rev_tl',\n", " 'num_rev_accts',\n", " 'num_rev_tl_bal_gt_0',\n", " 'num_sats',\n", " 'num_tl_120dpd_2m',\n", " 'num_tl_30dpd',\n", " 'num_tl_90g_dpd_24m',\n", " 'num_tl_op_past_12m',\n", " 'open_acc_6m',\n", " 'open_il_12m',\n", " 'open_il_24m',\n", " 'open_act_il',\n", " 'open_rv_12m',\n", " 'open_rv_24m',\n", " 'openAcc',\n", " 'pct_tl_nvr_dlq',\n", " 'percentBcGt75',\n", " 'pub_rec_bankruptcies',\n", " 'pubRec',\n", " 'purpose',\n", " 'reviewStatus',\n", " 'reviewStatusD',\n", " 'revolBal',\n", " 'revolUtil',\n", " 'serviceFeeRate',\n", " 'subGrade',\n", " 'tax_liens',\n", " 'term',\n", " 'title',\n", " 'tot_coll_amt',\n", " 'tot_cur_bal',\n", " 'tot_hi_cred_lim',\n", " 'total_bal_il',\n", " 'total_cu_tl',\n", " 'total_il_high_credit_limit',\n", " 'total_rev_hi_lim \\xa0',\n", " 'totalAcc',\n", " 'totalBalExMort',\n", " 'totalBcLimit',\n", " 'url',\n", " 'verified_status_joint',\n", " 'zip_code',\n", " 'revol_bal_joint ',\n", " 'sec_app_fico_range_low ',\n", " 'sec_app_fico_range_high ',\n", " 'sec_app_earliest_cr_line ',\n", " 'sec_app_inq_last_6mths ',\n", " 'sec_app_mort_acc ',\n", " 'sec_app_open_acc ',\n", " 'sec_app_revol_util ',\n", " 'sec_app_open_act_il',\n", " 'sec_app_num_rev_accts ',\n", " 'sec_app_chargeoff_within_12_mths ',\n", " 'sec_app_collections_12_mths_ex_med ',\n", " 'sec_app_mths_since_last_major_derog ',\n", " 'disbursement_method',\n", " 'Amount Requested',\n", " 'Application Date',\n", " 'Loan Title',\n", " 'Risk_Score',\n", " 'Debt-To-Income Ratio',\n", " 'Zip Code',\n", " 'State',\n", " 'Employment Length',\n", " 'Policy Code']"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dict_columns = list()\n", "for columns in sheet_columns.values():\n", "    # Flatten the list of lists\n", "    all_dict_columns += columns\n", "all_dict_columns"]}, {"cell_type": "code", "execution_count": 59, "id": "832aece9", "metadata": {}, "outputs": [{"data": {"text/plain": ["['all_util',\n", " 'annual_inc_joint',\n", " 'application_type',\n", " 'avg_cur_bal',\n", " 'chargeoff_within_12_mths',\n", " 'collections_12_mths_ex_med',\n", " 'desc',\n", " 'dti',\n", " 'dti_joint',\n", " 'emp_title',\n", " 'grade',\n", " 'id',\n", " 'il_util',\n", " 'inq_fi',\n", " 'inq_last_12m',\n", " 'installment',\n", " 'max_bal_bc',\n", " 'mo_sin_old_rev_tl_op',\n", " 'mo_sin_rcnt_rev_tl_op',\n", " 'mo_sin_rcnt_tl',\n", " 'mths_since_last_major_derog',\n", " 'mths_since_rcnt_il',\n", " 'num_accts_ever_120_pd',\n", " 'num_actv_bc_tl',\n", " 'num_actv_rev_tl',\n", " 'num_bc_sats',\n", " 'num_bc_tl',\n", " 'num_il_tl',\n", " 'num_op_rev_tl',\n", " 'num_rev_accts',\n", " 'num_rev_tl_bal_gt_0',\n", " 'num_sats',\n", " 'num_tl_120dpd_2m',\n", " 'num_tl_30dpd',\n", " 'num_tl_90g_dpd_24m',\n", " 'num_tl_op_past_12m',\n", " 'open_acc_6m',\n", " 'open_il_12m',\n", " 'open_il_24m',\n", " 'open_act_il',\n", " 'open_rv_12m',\n", " 'open_rv_24m',\n", " 'pct_tl_nvr_dlq',\n", " 'pub_rec_bankruptcies',\n", " 'purpose',\n", " 'tax_liens',\n", " 'term',\n", " 'title',\n", " 'tot_coll_amt',\n", " 'tot_cur_bal',\n", " 'tot_hi_cred_lim',\n", " 'total_bal_il',\n", " 'total_cu_tl',\n", " 'total_il_high_credit_limit',\n", " 'total_rev_hi_lim \\xa0',\n", " 'url',\n", " 'verified_status_joint',\n", " 'zip_code',\n", " 'revol_bal_joint ',\n", " 'sec_app_fico_range_low ',\n", " 'sec_app_fico_range_high ',\n", " 'sec_app_earliest_cr_line ',\n", " 'sec_app_inq_last_6mths ',\n", " 'sec_app_mort_acc ',\n", " 'sec_app_open_acc ',\n", " 'sec_app_revol_util ',\n", " 'sec_app_open_act_il',\n", " 'sec_app_num_rev_accts ',\n", " 'sec_app_chargeoff_within_12_mths ',\n", " 'sec_app_collections_12_mths_ex_med ',\n", " 'sec_app_mths_since_last_major_derog ',\n", " 'disbursement_method']"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["df_columns = pd.Series(all_dict_columns)\n", "duplicate_columns = df_columns[df_columns.duplicated()].tolist()\n", "duplicate_columns"]}, {"cell_type": "code", "execution_count": 61, "id": "a03c9fc6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined sheets keys: 280\n", "Unique combined sheets keys: 208\n", "Duplicated sheets keys: 72\n"]}], "source": ["print(f\"Combined sheets keys: {len(all_dict_columns)}\")\n", "print(f\"Unique combined sheets keys: {len(set(all_dict_columns))}\")\n", "print(f\"Duplicated sheets keys: {len(duplicate_columns)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}