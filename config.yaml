models:
  # Single Frontier Model Configuration
  frontier:
    provider: "google" # Options: "openai", "anthropic", "google", "groq"
    model_name: "gemini-2.0-flash"
    temperature: 0.1
    max_tokens: 2000

  alternatives:
    openai:
      model_name: "gpt-4-turbo-preview"
      temperature: 0.1
      max_tokens: 2000
    google:
      model_name: "gemini-1.5-pro"
      temperature: 0.1
      max_tokens: 2000
    # groq:
    #   model_name: "llama3-70b-8192"
    #   temperature: 0.1
    #   max_tokens: 2000

  # PoLL (Plateau of LLMs) Configuration
  poll:
    models:
      - provider: "huggingface"
        model_name: "microsoft/DialoGPT-medium"
        weight: 0.3
      - provider: "huggingface"
        model_name: "ProsusAI/finbert"
        weight: 0.4
      - provider: "huggingface"
        model_name: "nlpaueb/sec-bert-base"
        weight: 0.3
    ensemble_method: "weighted_average" # Options: "majority_vote", "weighted_average", "stacking"

  # Court System Configuration
  court:
    pros_model:
      provider: "anthropic"
      model_name: "claude-3-haiku-20240307"
      temperature: 0.2
    cons_model:
      provider: "anthropic"
      model_name: "claude-3-haiku-20240307"
      temperature: 0.2
    judge_model:
      provider: "anthropic"
      model_name: "claude-3-5-sonnet-20241022"
      temperature: 0.1

# Data Processing Configuration
data:
  chunk_size: 1000 # For processing large CSV files
  max_records: null # Set to limit processing for testing
  required_columns:
    - "loan_amnt"
    - "int_rate"
    - "grade"
    - "sub_grade"
    - "annual_inc"
    - "dti"
    - "fico_range_low"
    - "fico_range_high"
    - "loan_status"
  target_column: "loan_status"

# Evaluation Configuration
evaluation:
  test_split: 0.2
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "auc_roc"
  benchmark_approaches:
    - "single_frontier"
    - "poll"
    - "court_system"

# Langfuse Configuration
langfuse:
  enabled: true
  project_name: "loan-approval-prediction"
  trace_sample_rate: 1.0 # Sample rate for tracing (0.0 to 1.0)

# Logging Configuration
logging:
  level: "INFO" # Options: "DEBUG", "INFO", "WARNING", "ERROR"
  file: "logs/loan_prediction.log"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"

# Output Configuration
output:
  results_dir: "results"
  save_predictions: true
  save_explanations: true
  export_format: "json" # Options: "json", "csv", "parquet"
