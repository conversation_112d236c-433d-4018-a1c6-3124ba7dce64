"""
Hugging Face provider implementation for PoLL approach.
"""

import os
import json
from typing import Any, Dict, Optional
import torch
from transformers import <PERSON>Tokenizer, AutoModelForSequenceClassification, pipeline
from transformers import AutoModelForCausalLM, AutoConfig
from huggingface_hub import InferenceClient

from .base import <PERSON><PERSON><PERSON>aceProvider, LLMConfig


class HuggingFaceFinancialProvider(HuggingFaceProvider):
    """Hugging Face provider for financial models."""

    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv(
            "HUGGINGFACE_API_KEY") or os.getenv("HF_TOKEN")
        self.use_inference_api = config.additional_params.get(
            "use_inference_api", False)
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.inference_client = None

    def _initialize_client(self) -> Any:
        """Initialize the Hugging Face model and tokenizer."""
        if self.use_inference_api:
            return self._initialize_inference_api()
        else:
            return self._load_model_and_tokenizer()

    def _initialize_inference_api(self):
        """Initialize the Inference API client."""
        if not self.api_key:
            raise ValueError(
                "Hugging Face API key (HF_TOKEN or HUGGINGFACE_API_KEY) is required for Inference API")

        try:
            # Initialize the InferenceClient
            self.inference_client = InferenceClient(
                model=self.model_name,
                token=self.api_key
            )
            return self.inference_client
        except Exception as e:
            raise Exception(f"Failed to initialize Inference API: {str(e)}")

    def _load_model_and_tokenizer(self):
        """Load the Hugging Face model and tokenizer."""
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                token=self.api_key,
                trust_remote_code=True
            )

            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Determine model type and load accordingly
            if "finbert" in self.model_name.lower():
                # FinBERT for financial sentiment/classification
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_name,
                    token=self.api_key,
                    trust_remote_code=True
                )
                self.pipeline = pipeline(
                    "text-classification",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=0 if self.device == "cuda" else -1
                )

            elif "sec-bert" in self.model_name.lower():
                # SEC-BERT for financial document analysis
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_name,
                    token=self.api_key,
                    trust_remote_code=True
                )
                self.pipeline = pipeline(
                    "text-classification",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=0 if self.device == "cuda" else -1
                )

            else:
                # General language model
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    token=self.api_key,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
                )
                self.pipeline = pipeline(
                    "text-generation",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=0 if self.device == "cuda" else -1
                )

            # Move model to device
            if self.model:
                self.model.to(self.device)

            return self.pipeline

        except Exception as e:
            raise Exception(
                f"Failed to load Hugging Face model {self.model_name}: {str(e)}")

    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from Hugging Face model."""
        if self.use_inference_api:
            return self._generate_inference_api_response(prompt, **kwargs)
        else:
            return self._generate_local_response(prompt, **kwargs)

    def _generate_inference_api_response(self, prompt: str, **kwargs) -> str:
        """Generate response using Hugging Face Inference API."""
        if self.inference_client is None:
            self.get_client()

        try:
            # Check if this is a classification model or generation model
            if "finbert" in self.model_name.lower() or "sec-bert" in self.model_name.lower():
                # Classification task using text_classification
                result = self.inference_client.text_classification(prompt)
                return self._process_classification_result(result)
            else:
                # Text generation task using chat completions
                max_tokens = kwargs.get(
                    "max_tokens", kwargs.get("max_length", 200))
                temperature = kwargs.get(
                    "temperature", self.config.temperature)

                # Use chat completions format
                messages = [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]

                completion = self.inference_client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature
                )

                # Extract the response content
                if completion.choices and len(completion.choices) > 0:
                    return completion.choices[0].message.content
                else:
                    return "Unable to generate response"

        except Exception as e:
            return f"Error generating response via Inference API: {str(e)}"

    def _generate_local_response(self, prompt: str, **kwargs) -> str:
        """Generate response using locally loaded model."""
        if self.pipeline is None:
            self.get_client()

        try:
            if "finbert" in self.model_name.lower() or "sec-bert" in self.model_name.lower():
                # Classification models - return sentiment/classification
                result = self.pipeline(prompt)
                return self._process_classification_result(result)

            else:
                # Text generation models
                max_length = kwargs.get("max_length", 200)
                temperature = kwargs.get(
                    "temperature", self.config.temperature)

                result = self.pipeline(
                    prompt,
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    **kwargs
                )

                return self._process_generation_result(result, prompt)

        except Exception as e:
            return f"Error generating response: {str(e)}"

    def _process_classification_result(self, result) -> str:
        """Process classification result from both local and API responses."""
        if isinstance(result, list) and len(result) > 0:
            # Format classification result
            top_result = result[0]
            label = top_result.get('label', 'UNKNOWN')
            score = top_result.get('score', 0.0)

            # Map financial sentiment to loan decision context
            if label.upper() in ['POSITIVE', 'BULLISH']:
                decision_context = "APPROVE"
                confidence = "HIGH" if score > 0.8 else "MEDIUM"
            elif label.upper() in ['NEGATIVE', 'BEARISH']:
                decision_context = "DENY"
                confidence = "HIGH" if score > 0.8 else "MEDIUM"
            else:
                decision_context = "CONDITIONAL"
                confidence = "LOW"

            return f"Financial Analysis: {label} (confidence: {score:.3f})\nLoan Recommendation: {decision_context}\nConfidence Level: {confidence}"
        else:
            return "Unable to classify financial context"

    def _process_generation_result(self, result, prompt: str) -> str:
        """Process generation result from both local and API responses."""
        if isinstance(result, list) and len(result) > 0:
            if 'generated_text' in result[0]:
                # Local model response format
                generated_text = result[0].get('generated_text', '')
                # Remove the original prompt from the response
                if generated_text.startswith(prompt):
                    generated_text = generated_text[len(prompt):].strip()
                return generated_text
            else:
                # API response format
                return result[0].get('text', result[0].get('generated_text', ''))
        else:
            return "Unable to generate response"

    def validate_config(self) -> bool:
        """Validate Hugging Face configuration."""
        # Check if model name is valid for financial analysis
        financial_models = [
            "ProsusAI/finbert",
            "nlpaueb/sec-bert-base",
            "microsoft/DialoGPT-medium",
            "yiyanghkust/finbert-tone",
            "ElKulako/cryptobert"
        ]

        return any(model in self.model_name for model in financial_models)

    def get_model_info(self) -> Dict[str, Any]:
        """Get Hugging Face model information."""
        info = super().get_model_info()
        info.update({
            "use_inference_api": self.use_inference_api,
            "device": self.device if not self.use_inference_api else "remote",
            "model_loaded": self.model is not None if not self.use_inference_api else "N/A (using API)",
            "tokenizer_loaded": self.tokenizer is not None if not self.use_inference_api else "N/A (using API)",
            "pipeline_ready": self.pipeline is not None if not self.use_inference_api else "N/A (using API)",
            "inference_client_ready": self.inference_client is not None if self.use_inference_api else "N/A (using local)",
            "supported_features": ["financial_analysis", "text_classification", "sentiment_analysis"]
        })
        return info

    def cleanup(self):
        """Clean up model resources."""
        if self.model:
            del self.model
        if self.tokenizer:
            del self.tokenizer
        if self.pipeline:
            del self.pipeline

        # Clear CUDA cache if using GPU
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


class HuggingFaceEnsemble:
    """Ensemble of Hugging Face models for PoLL approach."""

    def __init__(self, model_configs: list):
        self.providers = []
        self.weights = []

        for config_dict in model_configs:
            config = LLMConfig(**config_dict)
            provider = HuggingFaceFinancialProvider(config)
            weight = config_dict.get("weight", 1.0)

            self.providers.append(provider)
            self.weights.append(weight)

        # Normalize weights
        total_weight = sum(self.weights)
        self.weights = [w / total_weight for w in self.weights]

    def generate_ensemble_response(self, prompt: str, method: str = "weighted_average") -> Dict[str, Any]:
        """Generate ensemble response from multiple models."""
        individual_responses = []

        for provider, weight in zip(self.providers, self.weights):
            try:
                response_data = provider.generate(prompt)
                response_data["weight"] = weight
                individual_responses.append(response_data)
            except Exception as e:
                # Log error but continue with other models
                individual_responses.append({
                    "response": None,
                    "error": str(e),
                    "model_name": provider.model_name,
                    "weight": weight,
                    "success": False
                })

        # Combine responses based on method
        if method == "weighted_average":
            return self._weighted_average_combination(individual_responses)
        elif method == "majority_vote":
            return self._majority_vote_combination(individual_responses)
        else:
            return self._simple_combination(individual_responses)

    def _weighted_average_combination(self, responses: list) -> Dict[str, Any]:
        """Combine responses using weighted average."""
        successful_responses = [
            r for r in responses if r.get("success", False)]

        if not successful_responses:
            return {
                "response": "All models failed to generate responses",
                "success": False,
                "individual_responses": responses
            }

        # Simple combination - in practice, you'd want more sophisticated logic
        combined_response = "Ensemble Analysis:\n"
        for resp in successful_responses:
            weight = resp.get("weight", 0)
            model_name = resp.get("model_name", "Unknown")
            response_text = resp.get("response", "")
            combined_response += f"\n{model_name} (weight: {weight:.2f}):\n{response_text}\n"

        return {
            "response": combined_response,
            "success": True,
            "individual_responses": responses,
            "ensemble_method": "weighted_average"
        }

    def _majority_vote_combination(self, responses: list) -> Dict[str, Any]:
        """Combine responses using majority vote."""
        # Simplified majority vote - extract decisions and vote
        decisions = []
        for resp in responses:
            if resp.get("success", False):
                response_text = resp.get("response", "").upper()
                if "APPROVE" in response_text:
                    decisions.append("APPROVE")
                elif "DENY" in response_text:
                    decisions.append("DENY")
                else:
                    decisions.append("CONDITIONAL")

        if not decisions:
            return {
                "response": "No valid decisions from ensemble",
                "success": False,
                "individual_responses": responses
            }

        # Count votes
        vote_counts = {}
        for decision in decisions:
            vote_counts[decision] = vote_counts.get(decision, 0) + 1

        # Get majority decision
        majority_decision = max(vote_counts, key=vote_counts.get)

        combined_response = f"Ensemble Decision: {majority_decision}\n"
        combined_response += f"Vote Distribution: {vote_counts}\n"
        combined_response += "Individual Model Responses:\n"

        for resp in responses:
            if resp.get("success", False):
                model_name = resp.get("model_name", "Unknown")
                response_text = resp.get("response", "")
                combined_response += f"\n{model_name}:\n{response_text}\n"

        return {
            "response": combined_response,
            "success": True,
            "individual_responses": responses,
            "ensemble_method": "majority_vote",
            "vote_distribution": vote_counts
        }

    def _simple_combination(self, responses: list) -> Dict[str, Any]:
        """Simple combination of responses."""
        successful_responses = [
            r for r in responses if r.get("success", False)]

        combined_response = "Combined Model Responses:\n"
        for resp in successful_responses:
            model_name = resp.get("model_name", "Unknown")
            response_text = resp.get("response", "")
            combined_response += f"\n{model_name}:\n{response_text}\n"

        return {
            "response": combined_response,
            "success": len(successful_responses) > 0,
            "individual_responses": responses,
            "ensemble_method": "simple_combination"
        }

    def cleanup(self):
        """Clean up all model resources."""
        for provider in self.providers:
            provider.cleanup()
