# Core dependencies
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0
python-dotenv>=1.0.0
pyyaml>=6.0

# Lang<PERSON>hain and LangGraph
langchain>=0.1.0
langchain-community>=0.0.20
langchain-core>=0.1.0
langgraph>=0.0.30
langchain-openai>=0.0.8
langchain-anthropic>=0.1.0
langchain-google-genai>=1.0.0

# LLM Providers
openai>=1.10.0
anthropic>=0.18.0
google-generativeai>=0.4.0
groq>=0.4.0

# Hugging Face for PoLL approach
transformers>=4.36.0
torch>=2.1.0
accelerate>=0.25.0
datasets>=2.16.0

# Evaluation and Benchmarking
langfuse>=2.0.0
scikit-learn>=1.3.0

# Utilities
tqdm>=4.65.0
rich>=13.0.0
typer>=0.9.0
loguru>=0.7.0
requests>=2.31.0  # For Hugging Face Inference API

# Data processing
polars>=0.20.0  # For large CSV files
pyarrow>=14.0.0

# Optional: For advanced financial models
# quantlib>=1.32
# yfinance>=0.2.0
