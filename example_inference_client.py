#!/usr/bin/env python3
"""
Example script demonstrating direct usage of Hugging Face InferenceClient
as shown in the user's example.
"""

import os
from dotenv import load_dotenv
from huggingface_hub import InferenceClient

def test_direct_inference_client():
    """Test direct InferenceClient usage."""
    load_dotenv(override=True)
    # Check for API key
    api_key = os.environ.get("HF_TOKEN")
    if not api_key:
        print("❌ HF_TOKEN environment variable not set!")
        print("Please set: export HF_TOKEN='your_token_here'")
        return
    
    print("🚀 Testing Direct InferenceClient Usage")
    print("=" * 50)
    
    # Create client as shown in user's example
    client = InferenceClient(
        api_key=api_key,
    )
    
    # Test with different models
    test_models = [
        "TheFinAI/Fino1-8B",
        "meta-llama/Llama-3.1-8B-Instruct",
        "microsoft/DialoGPT-medium"
    ]
    
    test_questions = [
        "What is the capital of France?",
        "Analyze this loan application: Income $75k, Credit Score 720, Loan Amount $250k",
        "What are the key factors in loan approval decisions?"
    ]
    
    for model_name in test_models:
        print(f"\n📝 Testing model: {model_name}")
        print("-" * 40)
        
        try:
            for question in test_questions:
                print(f"\nQuestion: {question}")
                
                completion = client.chat.completions.create(
                    model=model_name,
                    messages=[
                        {
                            "role": "user",
                            "content": question
                        }
                    ],
                    max_tokens=150,
                    temperature=0.1
                )
                
                if completion.choices and len(completion.choices) > 0:
                    response = completion.choices[0].message.content
                    print(f"Response: {response[:200]}...")
                else:
                    print("❌ No response received")
                    
        except Exception as e:
            print(f"❌ Error with {model_name}: {str(e)}")
    
    print(f"\n{'='*50}")
    print("✅ Direct InferenceClient testing completed!")

def test_classification_models():
    """Test classification models using InferenceClient."""
    
    api_key = os.getenv("HF_TOKEN")
    if not api_key:
        print("❌ HF_TOKEN environment variable not set!")
        return
    
    print("\n🔍 Testing Classification Models")
    print("=" * 50)
    
    client = InferenceClient(api_key=api_key)
    
    # Financial classification models
    classification_models = [
        "ProsusAI/finbert",
        "nlpaueb/sec-bert-base"
    ]
    
    test_texts = [
        "The company reported strong quarterly earnings with revenue growth of 15%",
        "Due to market volatility, we expect decreased profitability this quarter",
        "The loan applicant has excellent credit history and stable income"
    ]
    
    for model_name in classification_models:
        print(f"\n📊 Testing classification model: {model_name}")
        print("-" * 40)
        
        try:
            for text in test_texts:
                print(f"\nText: {text[:60]}...")
                
                # Use text_classification method for classification models
                result = client.text_classification(text, model=model_name)
                
                if result and len(result) > 0:
                    top_result = result[0]
                    label = top_result.get('label', 'UNKNOWN')
                    score = top_result.get('score', 0.0)
                    print(f"Classification: {label} (confidence: {score:.3f})")
                else:
                    print("❌ No classification result")
                    
        except Exception as e:
            print(f"❌ Error with {model_name}: {str(e)}")

def compare_with_our_implementation():
    """Compare direct usage with our provider implementation."""
    
    print("\n🔄 Comparing with Our Provider Implementation")
    print("=" * 50)
    
    # Import our provider
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent / "src"))
    
    try:
        from providers.factory import LLMProviderFactory
        
        api_key = os.getenv("HF_TOKEN")
        if not api_key:
            print("❌ HF_TOKEN environment variable not set!")
            return
        
        # Test our provider implementation
        config = {
            "provider": "huggingface",
            "model_name": "meta-llama/Llama-3.1-8B-Instruct",
            "temperature": 0.1,
            "max_tokens": 150,
            "use_inference_api": True,
            "api_key": api_key
        }
        
        provider = LLMProviderFactory.create_provider("huggingface", config)
        
        test_prompt = "What are the key factors in loan approval decisions?"
        
        print(f"📝 Testing our provider with: {test_prompt}")
        
        response = provider.generate(test_prompt)
        
        print(f"✅ Our Provider Response:")
        print(f"Success: {response.get('success', False)}")
        print(f"Response: {response.get('response', 'No response')}...")
        
        # Show model info
        model_info = provider.get_model_info()
        print(f"\n📊 Model Info:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ Error testing our provider: {str(e)}")

if __name__ == "__main__":
    print("🧪 Hugging Face InferenceClient Direct Usage Examples")
    print("Based on the user's provided code pattern")
    print("=" * 60)
    
    # Test direct InferenceClient usage
    test_direct_inference_client()
    
    # Test classification models
    test_classification_models()
    
    # Compare with our implementation
    compare_with_our_implementation()
    
    print("\n💡 Key Points:")
    print("1. InferenceClient provides a modern, robust API interface")
    print("2. Supports both chat completions and text classification")
    print("3. Automatic error handling and retries")
    print("4. No model downloads required")
    print("5. Works seamlessly with our provider implementation")
